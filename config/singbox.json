{"log": {"level": "info"}, "dns": {"servers": [{"tag": "dns_proxy", "address": "*******"}, {"tag": "dns_direct", "address": "*********"}], "rules": [{"domain": ["geosite:cn"], "server": "dns_direct"}], "final": "dns_proxy", "strategy": "prefer_ipv4"}, "inbounds": [{"type": "socks", "tag": "socks_1099", "listen_port": 1099, "listen": "127.0.0.1"}, {"type": "socks", "tag": "socks_1098", "listen_port": 1098, "listen": "127.0.0.1"}, {"type": "socks", "tag": "socks_1111", "listen_port": 1111, "listen": "127.0.0.1"}, {"type": "socks", "tag": "socks_1112", "listen_port": 1112, "listen": "127.0.0.1"}], "outbounds": [{"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 39597, "tag": "socks_1099_8eb1cb7ced121bf93c56ef09b8642f61_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 45288, "tag": "socks_1099_ae240cedbd9d6ec7a6d0786f4b920ebc_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 29510, "tag": "socks_1099_de0b65ee86b186cc312bd2428a682435_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 28516, "tag": "socks_1099_c363cc442b2b1866b7f4d85a14680d1d_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 45311, "tag": "socks_1099_5fb220b95c8eb5ff5b0eda2d25f58bc3_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 40291, "tag": "socks_1099_4c71edef4b5c83509b3ed0cfa83ff481_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 46042, "tag": "socks_1099_469940d0c854d2a58b1147dfabef36d2_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 59969, "tag": "socks_1099_2283f5e4c4b05951f1a60923db1fdbec_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 15305, "tag": "socks_1099_e87f1112dfa8fe7d9e71c9b547ea1715_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 59793, "tag": "socks_1099_a2057d5c1385cd5ed272342623f87108_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 28829, "tag": "socks_1099_36ea5b6e749354324f1db63d9082ef77_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 45299, "tag": "socks_1099_984ff46debbbc6f7658cb36a508c73d9_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1099_8eb1cb7ced121bf93c56ef09b8642f61_proxy", "socks_1099_ae240cedbd9d6ec7a6d0786f4b920ebc_proxy", "socks_1099_de0b65ee86b186cc312bd2428a682435_proxy", "socks_1099_c363cc442b2b1866b7f4d85a14680d1d_proxy", "socks_1099_5fb220b95c8eb5ff5b0eda2d25f58bc3_proxy", "socks_1099_4c71edef4b5c83509b3ed0cfa83ff481_proxy", "socks_1099_469940d0c854d2a58b1147dfabef36d2_proxy", "socks_1099_2283f5e4c4b05951f1a60923db1fdbec_proxy", "socks_1099_e87f1112dfa8fe7d9e71c9b547ea1715_proxy", "socks_1099_a2057d5c1385cd5ed272342623f87108_proxy", "socks_1099_36ea5b6e749354324f1db63d9082ef77_proxy", "socks_1099_984ff46debbbc6f7658cb36a508c73d9_proxy"], "tag": "socks_1099_urltest", "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1099_urltest", "socks_1099_8eb1cb7ced121bf93c56ef09b8642f61_proxy", "socks_1099_ae240cedbd9d6ec7a6d0786f4b920ebc_proxy", "socks_1099_de0b65ee86b186cc312bd2428a682435_proxy", "socks_1099_c363cc442b2b1866b7f4d85a14680d1d_proxy", "socks_1099_5fb220b95c8eb5ff5b0eda2d25f58bc3_proxy", "socks_1099_4c71edef4b5c83509b3ed0cfa83ff481_proxy", "socks_1099_469940d0c854d2a58b1147dfabef36d2_proxy", "socks_1099_2283f5e4c4b05951f1a60923db1fdbec_proxy", "socks_1099_e87f1112dfa8fe7d9e71c9b547ea1715_proxy", "socks_1099_a2057d5c1385cd5ed272342623f87108_proxy", "socks_1099_36ea5b6e749354324f1db63d9082ef77_proxy", "socks_1099_984ff46debbbc6f7658cb36a508c73d9_proxy"], "tag": "socks_1099_selector", "type": "selector"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 48486, "tag": "socks_1098_808915e594f4331956728c34dc38b7e1_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 58745, "tag": "socks_1098_26d756d42df8680d4628823153e1ace6_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 13171, "tag": "socks_1098_8de6cce8c35f80899febe686a23ac8d9_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 29120, "tag": "socks_1098_7cbeec1dca613c2a0cf7852df2af7d63_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1098_808915e594f4331956728c34dc38b7e1_proxy", "socks_1098_26d756d42df8680d4628823153e1ace6_proxy", "socks_1098_8de6cce8c35f80899febe686a23ac8d9_proxy", "socks_1098_7cbeec1dca613c2a0cf7852df2af7d63_proxy"], "tag": "socks_1098_urltest", "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1098_urltest", "socks_1098_808915e594f4331956728c34dc38b7e1_proxy", "socks_1098_26d756d42df8680d4628823153e1ace6_proxy", "socks_1098_8de6cce8c35f80899febe686a23ac8d9_proxy", "socks_1098_7cbeec1dca613c2a0cf7852df2af7d63_proxy"], "tag": "socks_1098_selector", "type": "selector"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 41617, "tag": "socks_1111_c9d6be775f1ccb530a316a73504f0f3c_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 39114, "tag": "socks_1111_225f87c766407c09c1e75b25a6ca9285_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 35998, "tag": "socks_1111_bca6a0f4c7ae9ec90e886dabd899b903_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 14765, "tag": "socks_1111_c31a88f94921c76e90d3077354200755_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 19957, "tag": "socks_1111_71ebf8cd8933ac7a0ea085d3b1823688_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 27746, "tag": "socks_1111_8d71ab7e9a0c0053498b1cd8941c7373_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1111_c9d6be775f1ccb530a316a73504f0f3c_proxy", "socks_1111_225f87c766407c09c1e75b25a6ca9285_proxy", "socks_1111_bca6a0f4c7ae9ec90e886dabd899b903_proxy", "socks_1111_c31a88f94921c76e90d3077354200755_proxy", "socks_1111_71ebf8cd8933ac7a0ea085d3b1823688_proxy", "socks_1111_8d71ab7e9a0c0053498b1cd8941c7373_proxy"], "tag": "socks_1111_urltest", "tolerance": 50, "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1111_urltest", "socks_1111_c9d6be775f1ccb530a316a73504f0f3c_proxy", "socks_1111_225f87c766407c09c1e75b25a6ca9285_proxy", "socks_1111_bca6a0f4c7ae9ec90e886dabd899b903_proxy", "socks_1111_c31a88f94921c76e90d3077354200755_proxy", "socks_1111_71ebf8cd8933ac7a0ea085d3b1823688_proxy", "socks_1111_8d71ab7e9a0c0053498b1cd8941c7373_proxy"], "tag": "socks_1111_selector", "type": "selector"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 35991, "tag": "socks_1112_16fef65971fc06eb537dd0948f4ad75a_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 10240, "tag": "socks_1112_1e889de1be63fc9d5a49eaeda1a9b891_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1112_16fef65971fc06eb537dd0948f4ad75a_proxy", "socks_1112_1e889de1be63fc9d5a49eaeda1a9b891_proxy"], "tag": "socks_1112_urltest", "tolerance": 50, "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1112_urltest", "socks_1112_16fef65971fc06eb537dd0948f4ad75a_proxy", "socks_1112_1e889de1be63fc9d5a49eaeda1a9b891_proxy"], "tag": "socks_1112_selector", "type": "selector"}, {"tag": "direct", "type": "direct"}], "route": {"rules": [{"inbound": ["socks_1099"], "outbound": "socks_1099_selector"}, {"inbound": ["socks_1098"], "outbound": "socks_1098_selector"}, {"inbound": ["socks_1111"], "outbound": "socks_1111_selector"}, {"inbound": ["socks_1112"], "outbound": "socks_1112_selector"}], "final": "direct", "auto_detect_interface": true}}