package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"singboxui/internal/config"
	"singboxui/internal/web"

	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("Starting SingBox UI...")

	// 加载配置
	fmt.Println("Loading configuration...")
	cfg, err := config.Load("config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	// 创建 Web 服务器
	fmt.Println("Initializing web server...")
	webServer, err := web.NewWebServer(cfg)
	if err != nil {
		log.Fatalf("Failed to create web server: %v", err)
	}

	// 创建 Gin 路由
	r := gin.Default()

	// 设置路由
	fmt.Println("Setting up routes...")
	webServer.SetupRoutes(r)

	// 创建 HTTP 服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: r,
	}

	// 启动服务器
	go func() {
		fmt.Printf("Web server starting on http://localhost:%d\n", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 启动 sing-box，使用 CommandContext 管理子进程
	_, err = webServer.Box.Start(cfg.SingBox.ConfigPath)
	if err != nil {
		fmt.Printf("Failed to start sing-box: %v\n", err)
	}

	fmt.Println("SingBox UI started successfully!")

	// 优雅退出时停止 sing-box
	defer webServer.Box.Stop()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("Shutting down...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	fmt.Println("Server exiting")
}
