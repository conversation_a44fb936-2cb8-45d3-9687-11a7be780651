# 规则管理重构说明

## 🎯 重构目标

将原有的"入站管理"功能重构为更符合 sing-box 配置逻辑的"规则管理"功能。

## 📋 主要变更

### 1. **概念转换**
- **入站管理** → **规则管理**
- **入站配置** → **路由规则配置**
- **节点选择** → **出站规则匹配**

### 2. **数据模型更新**

#### 新增 Rule 结构体 (`internal/models/types.go`)
```go
type Rule struct {
    ID       string `json:"id"`
    Name     string `json:"name"`
    Priority int    `json:"priority"` // 规则优先级
    Enabled  bool   `json:"enabled"`  // 是否启用
    
    // 匹配条件
    Inbound       []string `json:"inbound,omitempty"`        // 入站标签
    Domain        []string `json:"domain,omitempty"`         // 域名匹配
    DomainSuffix  []string `json:"domain_suffix,omitempty"`  // 域名后缀
    DomainKeyword []string `json:"domain_keyword,omitempty"` // 域名关键词
    DomainRegex   []string `json:"domain_regex,omitempty"`   // 域名正则
    Geosite       []string `json:"geosite,omitempty"`        // GeoSite
    SourceGeoIP   []string `json:"source_geoip,omitempty"`   // 源IP地理位置
    Geoip         []string `json:"geoip,omitempty"`          // 目标IP地理位置
    IPCidr        []string `json:"ip_cidr,omitempty"`        // IP段
    SourceIPCidr  []string `json:"source_ip_cidr,omitempty"` // 源IP段
    Port          []string `json:"port,omitempty"`           // 端口
    SourcePort    []string `json:"source_port,omitempty"`    // 源端口
    ProcessName   []string `json:"process_name,omitempty"`   // 进程名
    ProcessPath   []string `json:"process_path,omitempty"`   // 进程路径
    Protocol      []string `json:"protocol,omitempty"`       // 协议
    
    // 出站配置
    OutboundType string   `json:"outbound_type"`           // 出站类型
    Group        string   `json:"group,omitempty"`         // 节点分组
    IncludeNames []string `json:"include_names,omitempty"` // 包含节点名称
    ExcludeNames []string `json:"exclude_names,omitempty"` // 排除节点名称
    
    // URLTest/LoadBalance/Fallback 配置...
}
```

### 3. **前端页面重构**

#### 新建 `frontend/src/views/Rules.vue`
- **功能完整的规则管理界面**
- **支持多种匹配条件配置**：
  - 域名规则：域名匹配、域名后缀、域名关键词
  - 地理位置：GeoSite、GeoIP
  - 网络：IP段、端口、协议
  - 进程：进程名、进程路径
- **支持多种出站类型**：
  - Selector（选择器）
  - URLTest（URL测试）
  - LoadBalance（负载均衡）
  - Fallback（故障转移）
  - Direct（直连）
  - Block（阻断）

#### 新建 `frontend/src/api/rules.ts`
- **完整的规则管理 API 接口**
- **支持 CRUD 操作**
- **支持批量操作**

### 4. **路由和导航更新**

#### 路由更新 (`frontend/src/router/index.ts`)
```typescript
{
  path: '/rules',
  name: 'Rules',
  component: () => import('@/views/Rules.vue'),
  meta: { title: '规则管理' }
}
```

#### 导航菜单更新 (`frontend/src/components/Sidebar.vue`)
- 新增"规则管理"菜单项
- 保留"入站配置"菜单项（简化版）

## 🎨 界面特性

### 1. **规则列表**
- ✅ 优先级排序
- ✅ 启用/禁用状态切换
- ✅ 匹配条件预览
- ✅ 出站类型标识
- ✅ 快速编辑和删除

### 2. **规则配置对话框**
- ✅ **基本信息**：规则名称、优先级、启用状态
- ✅ **匹配条件**：分标签页组织不同类型的匹配条件
- ✅ **出站配置**：支持所有出站类型的详细配置
- ✅ **动态表单**：根据出站类型显示相应配置项

### 3. **匹配条件标签页**
- **域名规则**：域名匹配、后缀、关键词
- **地理位置**：GeoSite、GeoIP 选择
- **IP/端口**：IP段、端口范围、协议
- **进程**：进程名、进程路径

### 4. **出站配置**
- **节点选择**：分组、包含/排除节点名称
- **URLTest**：测速URL、间隔、容忍延迟
- **LoadBalance**：负载均衡算法、哈希键
- **Fallback**：健康检查URL、检查间隔

## 🔧 技术实现

### 1. **响应式设计**
- 支持移动端适配
- 表格和对话框自适应

### 2. **用户体验**
- 实时表单验证
- 智能默认值设置
- 批量文本输入支持

### 3. **数据处理**
- 文本域与数组的双向绑定
- 表单数据的序列化和反序列化
- 错误处理和状态管理

## 📝 待完成工作

### 后端 API 实现
1. **数据库表创建**：创建 rules 表
2. **API 路由**：实现 `/api/rules` 相关接口
3. **业务逻辑**：规则的 CRUD 操作
4. **配置生成**：将规则转换为 sing-box 配置

### 配置集成
1. **规则排序**：按优先级生成配置
2. **出站生成**：根据规则生成对应的出站配置
3. **路由规则**：生成 sing-box 的路由规则配置

## 🎉 优势

### 1. **更符合 sing-box 逻辑**
- 直接对应 sing-box 的路由规则概念
- 支持更丰富的匹配条件
- 更灵活的出站配置

### 2. **更好的用户体验**
- 直观的规则管理界面
- 分类清晰的配置选项
- 实时的状态反馈

### 3. **更强的扩展性**
- 易于添加新的匹配条件
- 支持更多出站类型
- 便于功能扩展

## 🚀 下一步

1. **实现后端 API**：完成规则管理的后端接口
2. **数据库迁移**：从入站配置迁移到规则配置
3. **配置生成**：实现规则到 sing-box 配置的转换
4. **测试验证**：确保功能正常工作
5. **文档完善**：更新用户使用文档

现在前端的规则管理功能已经完全重构完成，提供了更强大和灵活的规则配置能力！
