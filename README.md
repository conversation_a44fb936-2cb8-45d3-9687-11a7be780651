# SingBox UI

一个基于 sing-box 的代理管理工具，类似 passwall2 的功能。

## 功能特性

- 🚀 基于 sing-box 的高性能代理服务器
- 🌐 现代化的 Web 管理界面
- 📱 响应式设计，支持移动端
- 🔧 支持多种代理协议（Shadowsocks、VMess、Trojan、VLESS）
- ⚡ 实时配置重载
- 🎯 简洁的 CLI 命令行界面

## 支持的协议

- **Shadowsocks**: 支持多种加密方式
- **VMess**: 支持 WebSocket、HTTP/2 等传输方式
- **Trojan**: 支持 TLS 伪装
- **VLESS**: 轻量级协议

## 快速开始

### 环境要求

- **Go**: 1.21 或更高版本
- **Node.js**: 18 或更高版本
- **npm**: 9 或更高版本

### 0. 准备 sing-box 二进制（可选）

程序会自动下载最新版本的 sing-box 核心。如果你想手动下载，请前往 [sing-box 官方发布页](https://github.com/SagerNet/sing-box/releases) 下载与你系统匹配的 sing-box 可执行文件（如 `sing-box-darwin-amd64` 或 `sing-box-linux-amd64`），重命名为 `sing-box` 并放到本项目根目录，并赋予可执行权限：

```bash
chmod +x ./sing-box
```

### 1. 构建项目

#### 方法一：使用 Makefile（推荐）

```bash
# 显示所有可用命令
make help

# 构建前后端
make build

# 仅构建前端
make build-frontend

# 仅构建后端
make build-backend

# 生产环境构建
make build-prod

# 开发模式运行
make dev
```

#### 方法二：使用构建脚本

```bash
# 基础构建脚本
./build.sh

# 高级构建脚本（支持更多选项）
./build-all.sh --help

# 示例用法
./build-all.sh                    # 构建前后端
./build-all.sh -f                 # 仅构建前端
./build-all.sh -b                 # 仅构建后端
./build-all.sh -c -p              # 清理并生产构建
```

#### 方法三：手动构建

```bash
# 1. 构建前端
cd frontend
npm install
npm run build
cd ..

# 2. 构建后端
go mod tidy
go build -ldflags="-s -w" -o singboxui main.go
```

### 2. 运行程序

```bash
# 运行构建后的程序
./singboxui

# 或直接运行（开发模式）
go run main.go
```

### 3. 访问管理界面

打开浏览器访问: http://localhost:8080

## 配置说明

### 应用配置 (config.yaml)

```yaml
web_port: 8080          # Web管理界面端口
log_level: info         # 日志级别
data_dir: ./data        # 数据目录
singbox_config: ./config/singbox.json  # sing-box配置文件路径
```

### sing-box 配置

程序会自动创建默认的 sing-box 配置文件，包含：

- 入站配置：HTTP/SOCKS5 代理 (端口 7890)
- 出站配置：直连和阻止
- 路由规则：默认直连

## 使用说明

### Web 界面

1. **状态监控**: 查看服务运行状态
2. **出站管理**: 添加、删除、编辑代理服务器
3. **配置重载**: 实时重载 sing-box 配置

### 添加代理服务器

1. 点击"添加出站"按钮
2. 选择协议类型
3. 填写服务器信息
4. 保存配置

### CLI 命令

```bash
# 查看状态
./singboxui status

# 查看配置信息
./singboxui config
```

## 项目结构

```
singboxui/
├── main.go                 # 主程序入口
├── go.mod                  # Go模块文件
├── config.yaml             # 应用配置文件
├── build.sh                # 基础构建脚本
├── build-all.sh            # 高级构建脚本
├── Makefile                # Make构建文件
├── BUILD.md                # 构建说明文档
├── cmd/                    # CLI命令
│   └── root.go
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   │   └── config.go
│   ├── server/            # 服务器管理
│   │   ├── singbox.go     # sing-box管理
│   │   └── web.go         # Web服务器
│   ├── service/           # 业务逻辑层
│   ├── storage/           # 数据存储层
│   └── webapi/            # Web API层
├── frontend/              # Vue3前端项目
│   ├── src/               # 源代码
│   │   ├── components/    # Vue组件
│   │   ├── views/         # 页面视图
│   │   ├── store/         # Pinia状态管理
│   │   ├── api/           # API接口
│   │   └── utils/         # 工具函数
│   ├── dist/              # 构建输出目录
│   ├── package.json       # 前端依赖配置
│   └── vite.config.ts     # Vite配置
├── web/                   # 旧版Web前端
│   └── templates/
│       └── index.html     # 主页面
└── config/                # 配置文件目录
    └── singbox.json       # sing-box配置
```

## 开发

### 构建

详细构建说明请参考 [BUILD.md](./BUILD.md) 文件。

#### 快速构建

```bash
# 使用 Makefile
make build

# 使用构建脚本
./build.sh

# 手动构建
cd frontend && npm install && npm run build && cd ..
go build -o singboxui main.go
```

#### 开发模式

```bash
# 启动开发模式（前后端同时运行）
make dev

# 或分别启动
# 前端: cd frontend && npm run dev
# 后端: go run main.go
```

### 测试

```bash
# 运行所有测试
make test

# 或直接运行
go test ./...
```

### 代码质量

```bash
# 格式化代码
make format

# 代码检查
make lint

# 完整构建流程
make all
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 相关链接

- [sing-box](https://github.com/SagerNet/sing-box)
- [Gin Web Framework](https://github.com/gin-gonic/gin)
- [Cobra CLI Framework](https://github.com/spf13/cobra)

## 启动时阻塞进程问题解决方案

为避免启动时阻塞进程，已对后端启动流程做如下优化：

- 订阅自动更新调度器在后台异步启动，主程序不会因其阻塞。
- 数据库初始化增加超时和连接池参数，提升启动速度。
- 启动日志更详细，便于排查问题。

### 推荐启动方式

请使用 `./start.sh` 脚本启动项目，该脚本会自动检查依赖文件和目录，并以优化模式启动后端服务。

```bash
chmod +x start.sh
./start.sh
```

如需前端构建，请先进入 `frontend` 目录执行：

```bash
cd frontend
npm install
npm run build
```

Web 界面地址： http://localhost:8080

如遇端口占用或其他问题，请检查是否有其他进程占用 8080 端口。 