# 入站管理配置方式说明

## 🎯 功能概述

入站管理现在支持多种配置方式，每种方式都有其特定的用途和配置选项。

## 📋 支持的配置方式

### 1. **Selector (选择器)** - 默认方式
- **用途**: 手动选择代理节点，支持自动切换
- **特点**: 
  - 用户可以手动选择使用哪个节点
  - 支持启用自动切换功能
  - 兼容原有的入站管理逻辑

#### 配置选项
- **分组**: 选择订阅分组来筛选节点
- **包含名称**: 只包含名称匹配的节点
- **排除名称**: 排除名称匹配的节点
- **自动切换**: 启用后自动选择最快节点
  - **测速URL**: 用于测试节点速度的URL
  - **测速间隔**: 多久测试一次节点速度
  - **容忍延迟**: 允许的延迟差异（毫秒）

#### 生成的配置
```json
{
  "type": "selector",
  "tag": "inbound_selector",
  "outbounds": ["urltest_tag", "node1", "node2", "node3"]
}
```

如果启用自动切换，还会生成：
```json
{
  "type": "urltest",
  "tag": "inbound_urltest", 
  "outbounds": ["node1", "node2", "node3"],
  "url": "http://www.gstatic.com/generate_204",
  "interval": "60s",
  "tolerance": "50ms"
}
```

### 2. **URLTest (URL测试)**
- **用途**: 自动选择最快的节点
- **特点**: 定期测试所有节点，自动选择延迟最低的

#### 配置选项
- **包含名称**: 只包含名称匹配的节点
- **排除名称**: 排除名称匹配的节点
- **测速URL**: 用于测试的URL（必需）
- **测速间隔**: 测试间隔时间（必需）
- **容忍延迟**: 允许的延迟差异

#### 生成的配置
```json
{
  "type": "urltest",
  "tag": "inbound_urltest",
  "outbounds": ["node1", "node2", "node3"],
  "url": "http://www.gstatic.com/generate_204",
  "interval": "60s",
  "tolerance": "50ms"
}
```

### 3. **LoadBalance (负载均衡)**
- **用途**: 在多个节点间分配流量
- **特点**: 支持多种负载均衡算法

#### 配置选项
- **包含名称**: 只包含名称匹配的节点
- **排除名称**: 排除名称匹配的节点
- **负载均衡算法**: 
  - `round_robin`: 轮询
  - `least_load`: 最少连接
  - `random`: 随机
  - `consistent_hash`: 一致性哈希
- **哈希键** (仅一致性哈希):
  - `source_ip`: 源IP
  - `destination`: 目标地址
  - `source_port`: 源端口

#### 生成的配置
```json
{
  "type": "loadbalance",
  "tag": "inbound_loadbalance",
  "outbounds": ["node1", "node2", "node3"],
  "strategy": "round_robin"
}
```

### 4. **Fallback (故障转移)**
- **用途**: 主备节点切换
- **特点**: 主节点失效时自动切换到备用节点

#### 配置选项
- **包含名称**: 只包含名称匹配的节点
- **排除名称**: 排除名称匹配的节点
- **健康检查URL**: 用于检查节点健康状态
- **检查间隔**: 健康检查的间隔时间

#### 生成的配置
```json
{
  "type": "fallback",
  "tag": "inbound_fallback",
  "outbounds": ["node1", "node2", "node3"],
  "url": "http://www.gstatic.com/generate_204",
  "interval": "30s"
}
```

### 5. **Direct (直连)**
- **用途**: 直接连接，不经过代理
- **特点**: 所有流量直接访问目标地址

#### 生成的配置
```json
{
  "type": "direct",
  "tag": "inbound_direct"
}
```

### 6. **Block (阻断)**
- **用途**: 阻断所有连接
- **特点**: 拒绝所有连接请求

#### 生成的配置
```json
{
  "type": "block",
  "tag": "inbound_block"
}
```

## 🔧 使用建议

### 场景推荐

1. **日常使用**: 推荐使用 **Selector** 配置方式
   - 可以手动选择节点
   - 启用自动切换获得最佳体验

2. **自动化场景**: 推荐使用 **URLTest** 配置方式
   - 完全自动化，无需手动干预
   - 始终使用最快节点

3. **高可用场景**: 推荐使用 **Fallback** 配置方式
   - 主备切换，保证连接稳定性
   - 适合重要业务场景

4. **负载分散**: 推荐使用 **LoadBalance** 配置方式
   - 分散流量，避免单点压力
   - 适合高流量场景

5. **特殊需求**:
   - **Direct**: 某些流量需要直连时使用
   - **Block**: 需要屏蔽某些流量时使用

### 配置技巧

1. **节点筛选**: 合理使用包含/排除名称来筛选节点
2. **测速配置**: 选择稳定的测速URL，如 `http://www.gstatic.com/generate_204`
3. **间隔设置**: 根据网络环境调整测速间隔，避免过于频繁
4. **容忍延迟**: 设置合理的容忍延迟，避免频繁切换节点

## ✅ 修复内容

1. **数据库支持**: 添加了新配置方式字段的数据库支持
2. **NULL值处理**: 正确处理数据库中的NULL值
3. **前端界面**: 完善了各种配置方式的用户界面
4. **后端逻辑**: 实现了各种配置方式的配置生成逻辑
5. **兼容性**: 保持了与原有功能的兼容性

现在入站管理的新增和修改功能已经完全正常工作！
