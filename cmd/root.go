package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "singboxui",
	Short: "A GUI management tool for sing-box",
	Long: `SingBox UI is a comprehensive management tool for sing-box proxy server.
It provides both web interface and command line interface for managing sing-box configurations.`,
}

func Execute() error {
	return rootCmd.Execute()
}

func init() {
	rootCmd.AddCommand(startCmd)
	rootCmd.AddCommand(statusCmd)
	rootCmd.AddCommand(configCmd)
}

var startCmd = &cobra.Command{
	Use:   "start",
	Short: "Start the sing-box service",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("Starting sing-box service...")
		// 这里会由main.go处理
	},
}

var statusCmd = &cobra.Command{
	Use:   "status",
	Short: "Show service status",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("Service status: Running")
		fmt.Println("Web interface: http://localhost:8080")
		fmt.Println("Proxy port: 7890")
	},
}

var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Manage configuration",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("Configuration management:")
		fmt.Println("- Web interface: http://localhost:8080")
		fmt.Println("- Config file: ./config/singbox.json")
		fmt.Println("- Data directory: ./data")
	},
} 