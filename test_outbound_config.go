package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"singboxui/internal/models"
	"singboxui/internal/service"
	"singboxui/internal/storage"
)

func main1() {
	fmt.Println("🧪 测试 SingBox 出站配置生成...")

	// 创建存储实例
	storage, err := storage.NewJSONStorage("./data/config.json")
	if err != nil {
		log.Fatalf("创建存储失败: %v", err)
	}

	// 创建服务实例
	svc := service.NewService(storage)

	// 测试节点数据
	testNodes := []models.Node{
		{
			ID:            "vmess-test",
			Name:          "VMess 测试节点",
			Type:          "vmess",
			Address:       "vmess.example.com",
			Port:          443,
			UUID:          "12345678-1234-1234-1234-123456789abc",
			Security:      "auto",
			Network:       "ws",
			Path:          "/vmess",
			TlsEnabled:    ptrInt(1),
			TlsServerName: "vmess.example.com",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			ID:              "vless-test",
			Name:            "VLESS 测试节点",
			Type:            "vless",
			Address:         "vless.example.com",
			Port:            443,
			UUID:            "*************-4321-4321-cba987654321",
			Network:         "grpc",
			Flow:            "xtls-rprx-vision",
			GrpcServiceName: "vless-service",
			TlsEnabled:      ptrInt(1),
			TlsServerName:   "vless.example.com",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			ID:            "trojan-test",
			Name:          "Trojan 测试节点",
			Type:          "trojan",
			Address:       "trojan.example.com",
			Port:          443,
			Password:      "trojan-password",
			Network:       "ws",
			Path:          "/trojan",
			TlsServerName: "trojan.example.com",
			TlsInsecure:   ptrInt(0),
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			ID:         "ss-test",
			Name:       "Shadowsocks 测试节点",
			Type:       "shadowsocks",
			Address:    "ss.example.com",
			Port:       8388,
			Password:   "ss-password",
			Security:   "aes-256-gcm",
			Plugin:     "v2ray-plugin",
			PluginOpts: "server;tls;host=ss.example.com",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
	}

	// 创建测试入站
	testInbound := models.Inbound{
		ID:        "test-inbound",
		Name:      "测试入站",
		Type:      "socks",
		Port:      1080,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 清理并添加测试数据
	fmt.Println("📝 准备测试数据...")
	storage.DeleteAllNodes()
	storage.CreateInbound(&testInbound)

	for _, node := range testNodes {
		if err := storage.CreateNode(&node); err != nil {
			log.Printf("创建节点失败 %s: %v", node.Name, err)
		}
	}

	// 生成配置
	fmt.Println("⚙️ 生成 SingBox 配置...")
	config, err := svc.GenerateConfig()
	if err != nil {
		log.Fatalf("生成配置失败: %v", err)
	}

	// 输出配置
	configJSON, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		log.Fatalf("序列化配置失败: %v", err)
	}

	fmt.Println("📄 生成的配置:")
	fmt.Println(string(configJSON))

	// 验证各协议配置
	fmt.Println("\n🔍 验证各协议配置...")

	for _, outbound := range config.Outbounds {
		if outboundType, ok := outbound["type"].(string); ok {
			switch outboundType {
			case "vmess":
				validateVMessConfig(outbound)
			case "vless":
				validateVLessConfig(outbound)
			case "trojan":
				validateTrojanConfig(outbound)
			case "shadowsocks":
				validateShadowsocksConfig(outbound)
			}
		}
	}

	fmt.Println("✅ 配置生成测试完成!")
}

func validateVMessConfig(config map[string]interface{}) {
	fmt.Println("  🔸 验证 VMess 配置:")

	required := []string{"server", "server_port", "uuid", "security", "alter_id"}
	for _, field := range required {
		if _, ok := config[field]; !ok {
			fmt.Printf("    ❌ 缺少必需字段: %s\n", field)
		} else {
			fmt.Printf("    ✅ 字段存在: %s\n", field)
		}
	}

	// 检查 alter_id 是否为 0
	if alterId, ok := config["alter_id"].(int); ok && alterId == 0 {
		fmt.Println("    ✅ alter_id 设置正确 (0)")
	}
}

func validateVLessConfig(config map[string]interface{}) {
	fmt.Println("  🔸 验证 VLESS 配置:")

	required := []string{"server", "server_port", "uuid", "encryption"}
	for _, field := range required {
		if _, ok := config[field]; !ok {
			fmt.Printf("    ❌ 缺少必需字段: %s\n", field)
		} else {
			fmt.Printf("    ✅ 字段存在: %s\n", field)
		}
	}

	// 检查 encryption 是否为 none
	if encryption, ok := config["encryption"].(string); ok && encryption == "none" {
		fmt.Println("    ✅ encryption 设置正确 (none)")
	}

	// 检查 flow 字段
	if _, ok := config["flow"]; ok {
		fmt.Println("    ✅ flow 字段存在")
	}
}

func validateTrojanConfig(config map[string]interface{}) {
	fmt.Println("  🔸 验证 Trojan 配置:")

	required := []string{"server", "server_port", "password", "tls"}
	for _, field := range required {
		if _, ok := config[field]; !ok {
			fmt.Printf("    ❌ 缺少必需字段: %s\n", field)
		} else {
			fmt.Printf("    ✅ 字段存在: %s\n", field)
		}
	}

	// 检查 TLS 配置
	if tls, ok := config["tls"].(map[string]interface{}); ok {
		if enabled, ok := tls["enabled"].(bool); ok && enabled {
			fmt.Println("    ✅ TLS 已启用")
		}
	}
}

func validateShadowsocksConfig(config map[string]interface{}) {
	fmt.Println("  🔸 验证 Shadowsocks 配置:")

	required := []string{"server", "server_port", "password", "method"}
	for _, field := range required {
		if _, ok := config[field]; !ok {
			fmt.Printf("    ❌ 缺少必需字段: %s\n", field)
		} else {
			fmt.Printf("    ✅ 字段存在: %s\n", field)
		}
	}

	// 检查插件配置
	if _, ok := config["plugin"]; ok {
		fmt.Println("    ✅ 插件配置存在")
	}
}

func ptrInt(v int) *int {
	return &v
}
