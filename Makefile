# SingBox UI Makefile

.PHONY: help build build-frontend build-backend clean test dev install-deps docker-build docker-run

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
help: ## 显示帮助信息
	@echo "🚀 SingBox UI Build Commands"
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "Examples:"
	@echo "  make build              # 构建前后端"
	@echo "  make build-frontend     # 仅构建前端"
	@echo "  make build-backend      # 仅构建后端"
	@echo "  make clean              # 清理构建文件"
	@echo "  make dev                # 开发模式运行"

# 安装依赖
install-deps: ## 安装所有依赖
	@echo "📦 Installing dependencies..."
	@cd frontend && npm install
	@go mod tidy
	@echo "✅ Dependencies installed"

# 构建前端
build-frontend: ## 构建前端
	@echo "📦 Building frontend..."
	@cd frontend && (npm run build || (echo "⚠️  Type checking failed, trying build without type check..." && npx vite build))
	@if [ -d web/dist ]; then rm -rf web/dist; fi
	@mkdir -p web/dist
	@cp -r frontend/dist/* web/dist/
	@echo "✅ Frontend built and copied to web/dist"

# 构建后端
build-backend: ## 构建后端
	@echo "🔧 Building backend..."
	@go build -ldflags="-s -w" -o singboxui main.go
	@echo "✅ Backend built successfully"

# 构建前后端
build: install-deps build-frontend build-backend ## 构建前后端
	@echo "🎉 Full build completed!"
	@echo "📁 Generated files:"
	@echo "  - Backend: ./singboxui"
	@echo "  - Frontend: ./frontend/dist/"
	@echo "  - Web: ./web/dist/"
	@echo ""
	@echo "🚀 Run with: ./singboxui"

# 生产构建
build-prod: ## 生产环境构建
	@echo "🏭 Building for production..."
	@cd frontend && npm run build
	@go build -ldflags="-s -w" -o singboxui main.go
	@echo "✅ Production build completed"

# 清理构建文件
clean: ## 清理构建文件
	@echo "🧹 Cleaning build files..."
	@rm -rf frontend/dist
	@rm -f singboxui
	@go clean -cache
	@echo "✅ Clean completed"

# 运行测试
test: ## 运行测试
	@echo "🧪 Running tests..."
	@go test ./...
	@echo "✅ Tests completed"

# 开发模式
dev: ## 开发模式运行
	@echo "🚀 Starting development mode..."
	@echo "Frontend: http://localhost:5173"
	@echo "Backend:  http://localhost:8080"
	@echo ""
	@echo "Press Ctrl+C to stop"
	@cd frontend && npm run dev & \
	go run main.go & \
	wait

# 快速构建（跳过依赖安装）
quick-build: ## 快速构建（跳过依赖安装）
	@echo "⚡ Quick build..."
	@cd frontend && npm run build
	@go build -o singboxui main.go
	@echo "✅ Quick build completed"

# 检查依赖
check-deps: ## 检查依赖是否安装
	@echo "🔍 Checking dependencies..."
	@command -v go >/dev/null 2>&1 || { echo "❌ Go is not installed"; exit 1; }
	@command -v node >/dev/null 2>&1 || { echo "❌ Node.js is not installed"; exit 1; }
	@command -v npm >/dev/null 2>&1 || { echo "❌ npm is not installed"; exit 1; }
	@echo "✅ All dependencies are available"

# 显示版本信息
version: ## 显示版本信息
	@echo "📋 Version Information:"
	@echo "  Go: $(shell go version)"
	@echo "  Node: $(shell node --version)"
	@echo "  npm: $(shell npm --version)"
	@echo "  Go modules: $(shell go list -m all | wc -l) modules"

# 格式化代码
format: ## 格式化代码
	@echo "🎨 Formatting code..."
	@go fmt ./...
	@cd frontend && npm run format 2>/dev/null || echo "No format script in frontend"
	@echo "✅ Code formatted"

# 代码检查
lint: ## 代码检查
	@echo "🔍 Linting code..."
	@go vet ./...
	@cd frontend && npm run lint 2>/dev/null || echo "No lint script in frontend"
	@echo "✅ Linting completed"

# 完整构建流程
all: check-deps clean install-deps test build ## 完整构建流程（检查依赖、清理、安装、测试、构建）
	@echo "🎉 Full build pipeline completed!" 