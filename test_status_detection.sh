#!/bin/bash

echo "=== 核心状态检测测试 ==="
echo "测试时间: $(date)"
echo ""

# 测试1: 检查API响应时间
echo "测试1: API响应时间测试"
start_time=$(date +%s.%N)
curl -s http://localhost:8080/api/core/status > /dev/null
end_time=$(date +%s.%N)
response_time=$(echo "$end_time - $start_time" | bc)
echo "响应时间: ${response_time}秒"
echo ""

# 测试2: 连续多次请求测试
echo "测试2: 连续请求测试"
for i in {1..5}; do
    echo "请求 $i:"
    start_time=$(date +%s.%N)
    curl -s http://localhost:8080/api/core/status | jq -r '.running'
    end_time=$(date +%s.%N)
    response_time=$(echo "$end_time - $start_time" | bc)
    echo "响应时间: ${response_time}秒"
    sleep 1
done
echo ""

# 测试3: 检查进程检测
echo "测试3: 进程检测测试"
echo "当前sing-box进程:"
pgrep -f "sing-box" || echo "未找到sing-box进程"
echo ""

# 测试4: 检查日志中的状态检测频率
echo "测试4: 日志分析"
echo "最近的状态检测请求:"
tail -n 20 singboxui.log | grep "GET.*core/status" | tail -n 5
echo ""

echo "=== 测试完成 ===" 