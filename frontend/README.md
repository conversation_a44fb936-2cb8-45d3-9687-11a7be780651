# SingBox UI Frontend

基于 Vue 3 + TypeScript + Element Plus 的 SingBox 管理界面前端项目。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 下一代前端构建工具
- **Element Plus** - Vue 3 组件库
- **Pinia** - Vue 状态管理库
- **Vue Router** - Vue.js 官方路由管理器
- **Axios** - HTTP 客户端

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口封装
│   │   ├── core.ts        # 核心管理 API
│   │   ├── inbounds.ts    # 入站管理 API
│   │   ├── outbounds.ts   # 出站管理 API
│   │   ├── routing.ts     # 路由管理 API
│   │   ├── settings.ts    # 设置管理 API
│   │   ├── stats.ts       # 统计信息 API
│   │   └── subscriptions.ts # 订阅管理 API
│   ├── components/        # 通用组件
│   │   ├── Header.vue     # 顶部导航栏
│   │   └── Sidebar.vue    # 侧边栏导航
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── store/             # Pinia 状态管理
│   │   ├── core.ts        # 核心状态管理
│   │   ├── inbounds.ts    # 入站状态管理
│   │   ├── outbounds.ts   # 出站状态管理
│   │   ├── routing.ts     # 路由状态管理
│   │   ├── settings.ts    # 设置状态管理
│   │   ├── stats.ts       # 统计状态管理
│   │   └── subscriptions.ts # 订阅状态管理
│   ├── utils/             # 工具函数
│   │   └── request.ts     # HTTP 请求封装
│   ├── views/             # 页面组件
│   │   ├── Dashboard.vue  # 仪表盘
│   │   ├── Inbounds.vue   # 入站管理
│   │   ├── Outbounds.vue  # 出站管理
│   │   ├── Routing.vue    # 路由管理
│   │   ├── Settings.vue   # 系统设置
│   │   └── Subscriptions.vue # 节点订阅
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── style.css          # 全局样式
├── package.json           # 项目依赖
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md              # 项目说明
```

## 功能模块

### 1. 仪表盘 (Dashboard)
- 核心状态监控
- 连接统计信息
- 配置信息概览
- 快速操作按钮
- 系统日志显示

### 2. 入站管理 (Inbounds)
- 入站代理配置管理
- 支持多种协议 (HTTP, SOCKS, Shadowsocks, VMess, Trojan)
- 启用/禁用控制
- 配置编辑和删除

### 3. 出站管理 (Outbounds)
- 出站代理配置管理
- 支持多种协议 (Direct, Block, Shadowsocks, VMess, Trojan, WireGuard)
- 服务器配置
- 启用/禁用控制

### 4. 路由管理 (Routing)
- 路由规则配置
- 支持多种匹配类型 (域名, IP, 协议, 端口)
- 优先级设置
- 出站选择

### 5. 节点订阅 (Subscriptions)
- 订阅地址管理
- 自动更新配置
- 节点数量统计
- 更新历史记录

### 6. 系统设置 (Settings)
- 核心管理 (启动/停止/重载/更新)
- 系统配置
- 配置备份和恢复
- 日志级别设置

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd frontend
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 配置说明

### 环境变量

在项目根目录创建 `.env` 文件：

```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:8080

# 应用标题
VITE_APP_TITLE=SingBox UI

# 开发模式
VITE_DEV_MODE=true
```

### API 接口

前端通过 RESTful API 与后端通信，主要接口包括：

- `GET /api/core/status` - 获取核心状态
- `POST /api/core/start` - 启动核心
- `POST /api/core/stop` - 停止核心
- `POST /api/core/reload` - 重载配置
- `POST /api/core/update` - 更新核心
- `GET /api/stats` - 获取统计信息
- `GET /api/inbounds` - 获取入站配置
- `POST /api/inbounds` - 添加入站配置
- `PUT /api/inbounds/:id` - 更新入站配置
- `DELETE /api/inbounds/:id` - 删除入站配置
- 更多接口请参考 `src/api/` 目录下的文件

## 开发规范

### 代码风格

- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 Composition API 规范
- 使用 Element Plus 组件库
- 遵循 ESLint 和 Prettier 配置

### 组件开发

- 使用 `<script setup>` 语法
- 定义明确的 Props 和 Emits 类型
- 使用 Pinia 进行状态管理
- 组件命名使用 PascalCase

### API 开发

- 统一使用 `src/utils/request.ts` 进行 HTTP 请求
- 在 `src/api/` 目录下按模块组织 API 文件
- 使用 TypeScript 定义接口类型
- 统一错误处理

## 部署说明

### 构建

```bash
npm run build
```

构建产物位于 `dist/` 目录。

### 部署

将 `dist/` 目录下的文件部署到 Web 服务器，或使用 nginx 等反向代理。

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。 