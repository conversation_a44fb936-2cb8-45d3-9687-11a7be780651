{"name": "singbox-ui-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "nprogress": "^0.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "typescript": "^5.3.0", "vue-tsc": "^1.8.27", "vite": "^5.0.0"}}