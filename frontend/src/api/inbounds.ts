import { request } from '@/utils/request'
import type { Inbound } from '@/store/inbounds'

export const inboundsApi = {
  // 获取所有入站配置
  getInbounds(): Promise<Inbound[]> {
    return request.get('/api/inbounds')
  },

  // 添加入站配置
  addInbound(inbound: Omit<Inbound, 'id'>): Promise<Inbound> {
    return request.post('/api/inbounds', inbound)
  },

  // 更新入站配置
  updateInbound(id: string, inbound: Partial<Inbound>): Promise<Inbound> {
    return request.put(`/api/inbounds/${id}`, inbound)
  },

  // 删除入站配置
  deleteInbound(id: string): Promise<void> {
    return request.delete(`/api/inbounds/${id}`)
  }
} 