import { request } from '@/utils/request'
import type { Node } from '@/store/nodes'

export const nodesApi = {
  // 获取所有节点
  getNodes(): Promise<Node[]> {
    return request.get('/api/nodes')
  },

  // 获取按订阅分组的节点
  getNodesBySubscription(): Promise<Record<string, Node[]>> {
    return request.get('/api/nodes/by-subscription')
  },

  // 获取指定节点
  getNode(id: string): Promise<Node> {
    return request.get(`/api/nodes/${id}`)
  },

  // 添加节点
  addNode(node: Omit<Node, 'id' | 'created_at' | 'updated_at'>): Promise<Node> {
    return request.post('/api/nodes', node)
  },

  // 更新节点
  updateNode(id: string, node: Partial<Node>): Promise<Node> {
    return request.put(`/api/nodes/${id}`, node)
  },

  // 删除节点
  deleteNode(id: string): Promise<void> {
    return request.delete(`/api/nodes/${id}`)
  },

  // 删除所有节点
  deleteAllNodes(): Promise<void> {
    return request.delete('/api/nodes/all')
  }
} 