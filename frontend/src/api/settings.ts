import { request } from '@/utils/request'
import type { SystemSettings } from '@/store/settings'

export const settingsApi = {
  // 获取系统设置
  getSettings(): Promise<SystemSettings> {
    return request.get('/api/settings')
  },

  // 保存系统设置
  saveSettings(settings: SystemSettings): Promise<void> {
    return request.put('/api/settings', settings)
  },

  // 导出配置
  exportConfig(): Promise<void> {
    return request.post('/api/settings/export')
  },

  // 导入配置
  importConfig(): Promise<void> {
    return request.post('/api/settings/import')
  },

  // 重置配置
  resetConfig(): Promise<void> {
    return request.post('/api/settings/reset')
  },

  // 恢复备份
  restoreBackup(backupName: string): Promise<void> {
    return request.post(`/api/settings/backup/${backupName}/restore`)
  },

  // 删除备份
  deleteBackup(backupName: string): Promise<void> {
    return request.delete(`/api/settings/backup/${backupName}`)
  }
} 