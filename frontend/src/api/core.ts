import { request } from '@/utils/request'
import type { CoreStatus } from '@/store/core'

export const coreApi = {
  // 获取核心状态
  getStatus(): Promise<CoreStatus> {
    return request.get('/api/core/status')
  },

  // 启动核心
  start(): Promise<void> {
    return request.post('/api/core/start')
  },

  // 停止核心
  stop(): Promise<void> {
    return request.post('/api/core/stop')
  },

  // 重载配置
  reloadConfig(): Promise<void> {
    return request.post('/api/core/reload')
  },

  // 更新核心
  update(): Promise<void> {
    return request.post('/api/core/update')
  },

  // 生成配置
  generateConfig(): Promise<any> {
    return request.post('/api/config/generate')
  },

  // 导出配置
  exportConfig(): Promise<any> {
    return request.post('/api/config/export')
  }
} 