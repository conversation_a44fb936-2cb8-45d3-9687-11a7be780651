import { request } from '@/utils/request'
import type { Subscription } from '@/store/subscriptions'

export const subscriptionsApi = {
  // 获取所有订阅
  getSubscriptions(): Promise<Subscription[]> {
    return request.get('/api/subscriptions')
  },

  // 添加订阅
  addSubscription(subscription: Omit<Subscription, 'id' | 'nodeCount' | 'lastUpdate'>): Promise<Subscription> {
    return request.post('/api/subscriptions', subscription)
  },

  // 更新订阅
  updateSubscription(id: string, subscription: Partial<Subscription>): Promise<Subscription> {
    return request.put(`/api/subscriptions/${id}`, subscription)
  },

  // 删除订阅
  deleteSubscription(id: string): Promise<void> {
    return request.delete(`/api/subscriptions/${id}`)
  },

  // 更新订阅节点
  updateNodes(id: string): Promise<void> {
    return request.post(`/api/subscriptions/${id}/import`)
  },

  // 强制更新订阅
  forceUpdateSubscription(id: string): Promise<void> {
    return request.post(`/api/subscriptions/${id}/force-update`)
  },

  // 获取调度器状态
  getSchedulerStatus(): Promise<{ running: boolean; message: string }> {
    return request.get('/api/scheduler/status')
  },

  // 启动调度器
  startScheduler(): Promise<{ message: string }> {
    return request.post('/api/scheduler/start')
  },

  // 停止调度器
  stopScheduler(): Promise<{ message: string }> {
    return request.post('/api/scheduler/stop')
  }
} 