<template>
  <div class="app-header">
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="toggle-btn"
      >
        <el-icon><Fold v-if="!isCollapse" /><Expand v-else /></el-icon>
      </el-button>
      <h1 class="app-title">SingBox UI</h1>
    </div>

    <div class="header-right">
      <el-dropdown>
        <span class="user-info">
          <el-avatar size="small" icon="UserFilled" />
          <span class="username">管理员</span>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="showProfile">
              <el-icon><User /></el-icon>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item @click="showSettings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Fold,
  Expand,
  User,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  isCollapse: boolean
}

interface Emits {
  (e: 'toggle-sidebar'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const showProfile = () => {
  ElMessage.info('个人资料功能开发中...')
}

const showSettings = () => {
  ElMessage.info('系统设置功能开发中...')
}

const logout = () => {
  ElMessage.info('退出登录功能开发中...')
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-btn {
  margin-right: 16px;
  font-size: 18px;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin-left: 8px;
  font-size: 14px;
  color: #606266;
}

.el-dropdown-menu .el-icon {
  margin-right: 8px;
}
</style> 