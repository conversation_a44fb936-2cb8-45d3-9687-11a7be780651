<template>
  <el-menu
    :default-active="activeMenu"
    class="sidebar-menu"
    :collapse="isCollapse"
    background-color="#304156"
    text-color="#bfcbd9"
    active-text-color="#409EFF"
    router
  >
    <el-menu-item index="/">
      <el-icon><Monitor /></el-icon>
      <template #title>仪表盘</template>
    </el-menu-item>

    <el-menu-item index="/settings">
      <el-icon><Setting /></el-icon>
      <template #title>系统设置</template>
    </el-menu-item>

    <el-menu-item index="/subscriptions">
      <el-icon><Link /></el-icon>
      <template #title>订阅管理</template>
    </el-menu-item>

    <el-menu-item index="/inbounds">
      <el-icon><Operation /></el-icon>
      <template #title>规则管理</template>
    </el-menu-item>
  </el-menu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  <PERSON>,
  Connection,
  <PERSON>,
  DataBoard,
  Setting,
  Operation
} from '@element-plus/icons-vue'

interface Props {
  isCollapse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isCollapse: false
})

const route = useRoute()

const activeMenu = computed(() => route.path)
</script>

<style scoped>
.sidebar-menu {
  height: 100%;
  border-right: none;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 200px;
}

.el-menu-item {
  height: 50px;
  line-height: 50px;
}

.el-menu-item .el-icon {
  margin-right: 8px;
}
</style> 