import { defineStore } from 'pinia'
import { ref } from 'vue'
import { settingsApi } from '@/api/settings'

export interface SystemSettings {
  webPort: number
  logLevel: string
  autoStart: boolean
  configPath: string
}

export const useSettingsStore = defineStore('settings', () => {
  const settings = ref<SystemSettings>({
    webPort: 8080,
    logLevel: 'info',
    autoStart: false,
    configPath: ''
  })

  const loading = ref(false)

  const loadSettings = async () => {
    try {
      loading.value = true
      const data = await settingsApi.getSettings()
      settings.value = data
    } catch (error) {
      console.error('加载设置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const saveSettings = async (newSettings: SystemSettings) => {
    try {
      loading.value = true
      await settingsApi.saveSettings(newSettings)
      settings.value = newSettings
    } catch (error) {
      console.error('保存设置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const exportConfig = async () => {
    try {
      loading.value = true
      await settingsApi.exportConfig()
    } catch (error) {
      console.error('导出配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const importConfig = async () => {
    try {
      loading.value = true
      await settingsApi.importConfig()
    } catch (error) {
      console.error('导入配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const resetConfig = async () => {
    try {
      loading.value = true
      await settingsApi.resetConfig()
    } catch (error) {
      console.error('重置配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const restoreBackup = async (backupName: string) => {
    try {
      loading.value = true
      await settingsApi.restoreBackup(backupName)
    } catch (error) {
      console.error('恢复备份失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteBackup = async (backupName: string) => {
    try {
      loading.value = true
      await settingsApi.deleteBackup(backupName)
    } catch (error) {
      console.error('删除备份失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    settings,
    loading,
    loadSettings,
    saveSettings,
    exportConfig,
    importConfig,
    resetConfig,
    restoreBackup,
    deleteBackup
  }
}) 