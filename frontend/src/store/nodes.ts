import { defineStore } from 'pinia'
import { ref } from 'vue'
import { nodesApi } from '@/api/nodes'

export interface Node {
  id: string
  name: string
  type: string // vmess, vless, trojan, ss, ssr, hysteria, etc.
  address: string
  port: number
  uuid?: string
  password?: string
  security?: string
  network?: string
  outbound_id?: string
  group?: string
  subscription?: string
  created_at: string
  updated_at: string
  // 新增拆分字段
  tls_enabled?: number | boolean
  tls_server_name?: string
  tls_insecure?: number | boolean
  transport_type?: string
  grpc_service_name?: string
  grpc_idle_timeout?: string
  grpc_ping_timeout?: string
  grpc_permit_without_stream?: number
  // Hysteria 字段
  up_mbps?: number
  down_mbps?: number
  // 传输协议相关字段
  ws_path?: string
  ws_headers?: string
  h2_path?: string
  h2_host?: string
  // 兼容旧逻辑
  tls?: any
  transport?: any
  // 测试相关
  testing?: boolean
  latency?: number
  speed?: string
  available?: boolean
  // 速度测试相关属性
  latency?: number
  speed?: string
  lastTestTime?: string
  testing?: boolean
  available?: boolean
}

export const useNodesStore = defineStore('nodes', () => {
  const nodes = ref<Node[]>([])
  const nodesBySubscription = ref<Record<string, Node[]>>({})
  const loading = ref(false)

  const getNodes = async () => {
    try {
      loading.value = true
      const data = await nodesApi.getNodes()
      nodes.value = data
      return data
    } catch (error) {
      console.error('获取节点失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getNodesBySubscription = async () => {
    try {
      loading.value = true
      const data = await nodesApi.getNodesBySubscription()
      nodesBySubscription.value = data
      return data
    } catch (error) {
      console.error('获取按订阅分组的节点失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getNode = async (id: string) => {
    try {
      loading.value = true
      const data = await nodesApi.getNode(id)
      return data
    } catch (error) {
      console.error('获取节点失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addNode = async (node: Omit<Node, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      loading.value = true
      const data = await nodesApi.addNode(node)
      await getNodes()
      return data
    } catch (error) {
      console.error('添加节点失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateNode = async (id: string, node: Partial<Node>) => {
    try {
      loading.value = true
      const data = await nodesApi.updateNode(id, node)
      await getNodes()
      return data
    } catch (error) {
      console.error('更新节点失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteNode = async (id: string) => {
    await nodesApi.deleteNode(id)
    await getNodesBySubscription()
  }

  const deleteAllNodes = async () => {
    await nodesApi.deleteAllNodes()
    nodes.value = []
    nodesBySubscription.value = {}
  }

  return {
    nodes,
    nodesBySubscription,
    loading,
    getNodes,
    getNodesBySubscription,
    getNode,
    addNode,
    updateNode,
    deleteNode,
    deleteAllNodes
  }
}) 