import { defineStore } from 'pinia'
import { ref } from 'vue'
import { subscriptionsApi } from '@/api/subscriptions'

export interface Subscription {
  id: string
  name: string
  url: string
  nodeCount: number
  lastUpdate: string
  updateInterval: number
  enabled: boolean
  description: string
}

export const useSubscriptionsStore = defineStore('subscriptions', () => {
  const subscriptions = ref<Subscription[]>([])
  const loading = ref(false)

  const getSubscriptions = async () => {
    try {
      loading.value = true
      const data = await subscriptionsApi.getSubscriptions()
      subscriptions.value = data
      return data
    } catch (error) {
      console.error('获取订阅失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addSubscription = async (subscription: Omit<Subscription, 'id' | 'nodeCount' | 'lastUpdate'>) => {
    try {
      loading.value = true
      const data = await subscriptionsApi.addSubscription(subscription)
      await getSubscriptions()
      return data
    } catch (error) {
      console.error('添加订阅失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSubscription = async (id: string, subscription: Partial<Subscription>) => {
    try {
      loading.value = true
      const data = await subscriptionsApi.updateSubscription(id, subscription)
      await getSubscriptions()
      return data
    } catch (error) {
      console.error('更新订阅失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteSubscription = async (id: string) => {
    try {
      loading.value = true
      await subscriptionsApi.deleteSubscription(id)
      await getSubscriptions()
    } catch (error) {
      console.error('删除订阅失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSubscriptionNodes = async (id: string) => {
    try {
      loading.value = true
      await subscriptionsApi.updateNodes(id)
      await getSubscriptions()
    } catch (error) {
      console.error('更新订阅节点失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const forceUpdateSubscription = async (id: string) => {
    try {
      loading.value = true
      await subscriptionsApi.forceUpdateSubscription(id)
      await getSubscriptions()
    } catch (error) {
      console.error('强制更新订阅失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getSchedulerStatus = async () => {
    try {
      return await subscriptionsApi.getSchedulerStatus()
    } catch (error) {
      console.error('获取调度器状态失败:', error)
      throw error
    }
  }

  const startScheduler = async () => {
    try {
      return await subscriptionsApi.startScheduler()
    } catch (error) {
      console.error('启动调度器失败:', error)
      throw error
    }
  }

  const stopScheduler = async () => {
    try {
      return await subscriptionsApi.stopScheduler()
    } catch (error) {
      console.error('停止调度器失败:', error)
      throw error
    }
  }

  return {
    subscriptions,
    loading,
    getSubscriptions,
    addSubscription,
    updateSubscription,
    deleteSubscription,
    updateSubscriptionNodes,
    forceUpdateSubscription,
    getSchedulerStatus,
    startScheduler,
    stopScheduler
  }
}) 