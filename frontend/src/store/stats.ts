import { defineStore } from 'pinia'
import { ref } from 'vue'
import { statsApi } from '@/api/stats'

export interface Stats {
  activeConnections: number
  totalTraffic: string
  todayTraffic: string
  uploadSpeed: string
  downloadSpeed: string
}

export const useStatsStore = defineStore('stats', () => {
  const stats = ref<Stats>({
    activeConnections: 0,
    totalTraffic: '0MB',
    todayTraffic: '0MB',
    uploadSpeed: '0KB/s',
    downloadSpeed: '0KB/s'
  })

  const loading = ref(false)
  const lastFetchTime = ref(0)
  const cacheDuration = 10000 // 10秒缓存

  const getStats = async (force = false) => {
    const now = Date.now()
    
    // 如果强制刷新或者缓存已过期，则重新获取
    if (force || now - lastFetchTime.value > cacheDuration) {
      try {
        loading.value = true
        const data = await statsApi.getStats()
        stats.value = data
        lastFetchTime.value = now
      } catch (error) {
        console.error('获取统计信息失败:', error)
        throw error
      } finally {
        loading.value = false
      }
    }
  }

  const startRealTimeStats = () => {
    // 启动实时统计更新
    const interval = setInterval(async () => {
      try {
        await getStats()
      } catch (error) {
        console.error('更新实时统计失败:', error)
      }
    }, 30000) // 每30秒更新一次（减少频率）

    return () => clearInterval(interval)
  }

  return {
    stats,
    loading,
    getStats,
    startRealTimeStats
  }
}) 