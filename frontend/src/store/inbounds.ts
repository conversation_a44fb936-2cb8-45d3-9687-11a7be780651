import { defineStore } from 'pinia'
import { ref } from 'vue'
import { inboundsApi } from '@/api/inbounds'

export interface Inbound {
  id: string
  name: string
  type: string
  port: number
  username?: string
  password?: string
  config_type?: string // 配置方式: selector, urltest, loadbalance, fallback, direct, block
  group?: string
  include_names?: string[]
  exclude_names?: string[]

  // URLTest 配置
  auto_switch?: boolean
  switch_delay?: number
  switch_delay_unit?: 's' | 'm'
  urltest_url?: string
  urltest_interval?: number
  urltest_interval_unit?: 's' | 'm'
  urltest_tolerance?: number

  // LoadBalance 配置
  loadbalance_strategy?: string
  loadbalance_hash_key?: string

  // Fallback 配置
  fallback_url?: string
  fallback_interval_value?: number
  fallback_interval_unit?: 's' | 'm'

  settings?: Record<string, any>
  sniffing?: {
    enabled: boolean
    dest_override: string[]
  }
  created_at?: string
  updated_at?: string
}

export const useInboundsStore = defineStore('inbounds', () => {
  const inbounds = ref<Inbound[]>([])
  const loading = ref(false)

  const getInbounds = async () => {
    try {
      loading.value = true
      const data = await inboundsApi.getInbounds()
      inbounds.value = data
      return data
    } catch (error) {
      console.error('获取入站配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addInbound = async (inbound: Omit<Inbound, 'id'>) => {
    try {
      loading.value = true
      const data = await inboundsApi.addInbound(inbound)
      await getInbounds()
      return data
    } catch (error) {
      console.error('添加入站配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateInbound = async (id: string, inbound: Partial<Inbound>) => {
    try {
      loading.value = true
      const data = await inboundsApi.updateInbound(id, inbound)
      await getInbounds()
      return data
    } catch (error) {
      console.error('更新入站配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteInbound = async (id: string) => {
    try {
      loading.value = true
      await inboundsApi.deleteInbound(id)
      await getInbounds()
    } catch (error) {
      console.error('删除入站配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    inbounds,
    loading,
    getInbounds,
    addInbound,
    updateInbound,
    deleteInbound
  }
}) 