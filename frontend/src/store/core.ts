import { defineStore } from 'pinia'
import { ref } from 'vue'
import { coreApi } from '@/api/core'

export interface CoreStatus {
  running: boolean
  version: string
  uptime: string
  memory: string
  configPath: string
}

export const useCoreStore = defineStore('core', () => {
  const status = ref<CoreStatus>({
    running: false,
    version: '',
    uptime: '',
    memory: '',
    configPath: ''
  })

  const loading = ref(false)
  const lastFetchTime = ref(0)
  const cacheDuration = 5000 // 5秒缓存

  const getCoreStatus = async (force = false) => {
    const now = Date.now()
    
    // 如果强制刷新或者缓存已过期，则重新获取
    if (force || now - lastFetchTime.value > cacheDuration) {
      try {
        loading.value = true
        const data = await coreApi.getStatus()
        status.value = data
        lastFetchTime.value = now
      } catch (error) {
        console.error('获取核心状态失败:', error)
        throw error
      } finally {
        loading.value = false
      }
    }
  }

  const startCore = async () => {
    try {
      loading.value = true
      await coreApi.start()
      await getCoreStatus(true) // 强制刷新状态
    } catch (error) {
      console.error('启动核心失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const stopCore = async () => {
    try {
      loading.value = true
      await coreApi.stop()
      await getCoreStatus(true) // 强制刷新状态
    } catch (error) {
      console.error('停止核心失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const reloadConfig = async () => {
    try {
      loading.value = true
      await coreApi.reloadConfig()
    } catch (error) {
      console.error('重载配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateCore = async () => {
    try {
      loading.value = true
      await coreApi.update()
      await getCoreStatus(true) // 强制刷新状态
    } catch (error) {
      console.error('更新核心失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    status,
    loading,
    getCoreStatus,
    startCore,
    stopCore,
    reloadConfig,
    updateCore
  }
}) 