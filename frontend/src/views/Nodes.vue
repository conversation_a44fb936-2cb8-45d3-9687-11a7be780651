<template>
  <div class="nodes">
    <div class="page-header">
      <h2>节点管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="batchTestSpeed" :loading="batchTesting">
          <el-icon><Refresh /></el-icon>
          批量测试
        </el-button>
        <el-button type="warning" @click="autoDetectNodes" :loading="autoDetecting">
          <el-icon><Monitor /></el-icon>
          自动检测
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加节点
        </el-button>
        <el-button type="danger" @click="onDeleteAllNodes" style="margin-left: 12px;">
          <el-icon><Delete /></el-icon>
          删除所有节点
        </el-button>
      </div>
    </div>

    <!-- 按订阅分组的选项卡 -->
    <el-tabs v-model="activeTab" type="card" v-loading="loading">
      <el-tab-pane 
        v-for="(nodes, subscriptionName) in nodesBySubscription" 
        :key="subscriptionName"
        :label="`${subscriptionName} (${nodes.length})`"
        :name="subscriptionName"
      >
        <el-table :data="nodes" style="width: 100%">
          <el-table-column prop="name" label="节点名称" width="150" />
          <el-table-column prop="type" label="协议类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getProtocolTagType(row.type)">{{ row.type.toUpperCase() }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="服务器地址" width="150" />
          <el-table-column prop="port" label="端口" width="80" />
          <el-table-column prop="group" label="分组" width="100" />
          <el-table-column prop="security" label="加密方式" width="120" />
          <el-table-column prop="network" label="传输协议" width="120" />
          <el-table-column label="延迟" width="100">
            <template #default="{ row }">
              <span v-if="row.latency" :class="getLatencyClass(row.latency)">
                {{ row.latency }}ms
              </span>
              <span v-else class="no-test">未测试</span>
            </template>
          </el-table-column>
          <el-table-column label="速度" width="120">
            <template #default="{ row }">
              <span v-if="row.speed" class="speed-value">
                {{ row.speed }}
              </span>
              <span v-else class="no-test">未测试</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag v-if="row.available !== undefined" :type="row.available ? 'success' : 'danger'">
                {{ row.available ? '可用' : '不可用' }}
              </el-tag>
              <el-tag v-else type="info">未检测</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="testNodeSpeed(row)" :loading="row.testing">
                测试速度
              </el-button>
              <el-button size="small" @click="editNode(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteNode(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingNode ? '编辑节点' : '添加节点'"
      width="600px"
    >
      <el-form :model="nodeForm" label-width="100px" :rules="rules" ref="nodeFormRef">
        <el-form-item label="节点名称" prop="name">
          <el-input v-model="nodeForm.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="协议类型" prop="type">
          <el-select v-model="nodeForm.type" placeholder="请选择协议类型" style="width: 100%" @change="onTypeChange">
            <el-option label="Shadowsocks" value="shadowsocks" />
            <el-option label="ShadowsocksR" value="ssr" />
            <el-option label="VMess" value="vmess" />
            <el-option label="VLESS" value="vless" />
            <el-option label="Trojan" value="trojan" />
            <el-option label="Hysteria" value="hysteria" />
            <el-option label="Hysteria2" value="hysteria2" />
            <el-option label="Tuic" value="tuic" />
            <el-option label="NaiveProxy" value="naiveproxy" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器地址" prop="address">
          <el-input v-model="nodeForm.address" placeholder="请输入服务器地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="nodeForm.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        
        <!-- VMess 和 VLess 使用 UUID -->
        <el-form-item label="UUID" prop="uuid" v-if="['vmess', 'vless'].includes(nodeForm.type)">
          <el-input v-model="nodeForm.uuid" placeholder="请输入UUID" />
        </el-form-item>
        
        <!-- Trojan 使用密码 -->
        <el-form-item label="密码" prop="password" v-if="['trojan'].includes(nodeForm.type)">
          <el-input v-model="nodeForm.password" placeholder="请输入密码" />
        </el-form-item>
        
        <!-- Shadowsocks 和 SSR 使用密码 -->
        <el-form-item label="密码" prop="password" v-if="['shadowsocks', 'ssr'].includes(nodeForm.type)">
          <el-input v-model="nodeForm.password" placeholder="请输入密码" />
        </el-form-item>
        
        <!-- Hysteria 系列使用密码 -->
        <el-form-item label="密码" prop="password" v-if="['hysteria', 'hysteria2'].includes(nodeForm.type)">
          <el-input v-model="nodeForm.password" placeholder="请输入密码" />
        </el-form-item>
        
        <!-- Tuic 和 NaiveProxy 使用密码 -->
        <el-form-item label="密码" prop="password" v-if="['tuic', 'naiveproxy'].includes(nodeForm.type)">
          <el-input v-model="nodeForm.password" placeholder="请输入密码" />
        </el-form-item>
        
        <!-- Shadowsocks 和 SSR 的加密方式 -->
        <el-form-item label="加密方式" prop="security" v-if="['shadowsocks', 'ssr'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.security" placeholder="请选择加密方式" style="width: 100%">
            <el-option label="aes-256-gcm" value="aes-256-gcm" />
            <el-option label="aes-128-gcm" value="aes-128-gcm" />
            <el-option label="chacha20-poly1305" value="chacha20-poly1305" />
            <el-option label="aes-256-cfb" value="aes-256-cfb" />
            <el-option label="aes-128-cfb" value="aes-128-cfb" />
            <el-option label="chacha20" value="chacha20" />
          </el-select>
        </el-form-item>
        
        <!-- VMess 的安全设置 -->
        <el-form-item label="安全设置" prop="security" v-if="['vmess'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.security" placeholder="请选择安全设置" style="width: 100%">
            <el-option label="auto" value="auto" />
            <el-option label="none" value="none" />
            <el-option label="tls" value="tls" />
          </el-select>
        </el-form-item>
        
        <!-- VLess 的安全设置 -->
        <el-form-item label="安全设置" prop="security" v-if="['vless'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.security" placeholder="请选择安全设置" style="width: 100%">
            <el-option label="none" value="none" />
            <el-option label="tls" value="tls" />
          </el-select>
        </el-form-item>
        
        <!-- Trojan 的安全设置 -->
        <el-form-item label="安全设置" prop="security" v-if="['trojan'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.security" placeholder="请选择安全设置" style="width: 100%">
            <el-option label="tls" value="tls" />
            <el-option label="none" value="none" />
          </el-select>
        </el-form-item>
        
        <!-- Hysteria2 的安全设置 -->
        <el-form-item label="安全设置" prop="security" v-if="['hysteria2'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.security" placeholder="请选择安全设置" style="width: 100%">
            <el-option label="none" value="none" />
            <el-option label="tls" value="tls" />
          </el-select>
        </el-form-item>
        
        <!-- VMess 的传输协议 -->
        <el-form-item label="传输协议" prop="network" v-if="['vmess'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.network" placeholder="请选择传输协议" style="width: 100%">
            <el-option label="tcp" value="tcp" />
            <el-option label="ws" value="ws" />
            <el-option label="http" value="http" />
            <el-option label="grpc" value="grpc" />
            <el-option label="quic" value="quic" />
          </el-select>
        </el-form-item>
        
        <!-- VLess 的传输协议 -->
        <el-form-item label="传输协议" prop="network" v-if="['vless'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.network" placeholder="请选择传输协议" style="width: 100%">
            <el-option label="tcp" value="tcp" />
            <el-option label="ws" value="ws" />
            <el-option label="http" value="http" />
            <el-option label="grpc" value="grpc" />
            <el-option label="quic" value="quic" />
          </el-select>
        </el-form-item>
        
        <!-- Trojan 的传输协议 -->
        <el-form-item label="传输协议" prop="network" v-if="['trojan'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.network" placeholder="请选择传输协议" style="width: 100%">
            <el-option label="tcp" value="tcp" />
            <el-option label="ws" value="ws" />
            <el-option label="grpc" value="grpc" />
          </el-select>
        </el-form-item>
        
        <!-- Hysteria2 的传输协议 -->
        <el-form-item label="传输协议" prop="network" v-if="['hysteria2'].includes(nodeForm.type)">
          <el-select v-model="nodeForm.network" placeholder="请选择传输协议" style="width: 100%">
            <el-option label="udp" value="udp" />
            <el-option label="tcp" value="tcp" />
          </el-select>
        </el-form-item>
        
        <!-- SSR 的协议+混淆 -->
        <el-form-item label="协议+混淆" prop="network" v-if="['ssr'].includes(nodeForm.type)">
          <el-input v-model="nodeForm.network" placeholder="例如: origin+plain" />
        </el-form-item>
        
        <!-- Trojan TLS/GRPC 拆分字段表单 -->
        <el-form-item label="TLS 启用" v-if="nodeForm.type==='trojan'">
          <el-switch v-model="nodeForm.tls_enabled" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="TLS SNI" v-if="nodeForm.type==='trojan'">
          <el-input v-model="nodeForm.tls_server_name" placeholder="如 usb07.nwncd.com" />
        </el-form-item>
        <el-form-item label="TLS Insecure" v-if="nodeForm.type==='trojan'">
          <el-switch v-model="nodeForm.tls_insecure" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="GRPC Service Name" v-if="nodeForm.type==='trojan' && nodeForm.network==='grpc'">
          <el-input v-model="nodeForm.grpc_service_name" placeholder="如 mygrpc" />
        </el-form-item>
        <el-form-item label="GRPC Idle Timeout" v-if="nodeForm.type==='trojan' && nodeForm.network==='grpc'">
          <el-input v-model="nodeForm.grpc_idle_timeout" placeholder="如 60s" />
        </el-form-item>
        <el-form-item label="GRPC Ping Timeout" v-if="nodeForm.type==='trojan' && nodeForm.network==='grpc'">
          <el-input v-model="nodeForm.grpc_ping_timeout" placeholder="如 20s" />
        </el-form-item>
        <el-form-item label="GRPC Permit Without Stream" v-if="nodeForm.type==='trojan' && nodeForm.network==='grpc'">
          <el-switch v-model="nodeForm.grpc_permit_without_stream" :active-value="1" :inactive-value="0" />
        </el-form-item>
        
        <el-form-item label="分组" prop="group">
          <el-input v-model="nodeForm.group" placeholder="请输入分组名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveNode">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Plus, Delete, Refresh, Monitor } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useNodesStore } from '@/store/nodes'
import type { Node } from '@/store/nodes'

const nodesStore = useNodesStore()

const loading = ref(false)
const showAddDialog = ref(false)
const editingNode = ref<Node | null>(null)
const nodeFormRef = ref()
const activeTab = ref('')
const batchTesting = ref(false)
const autoDetecting = ref(false)

const nodesBySubscription = ref<Record<string, Node[]>>({})

const nodeForm = ref({
  name: '',
  type: 'shadowsocks',
  address: '',
  port: 443,
  uuid: '',
  password: '',
  security: 'aes-256-gcm',
  network: 'tcp',
  group: '',
  tls_enabled: 1,
  tls_server_name: '',
  tls_insecure: 0,
  transport_type: '',
  grpc_service_name: '',
  grpc_idle_timeout: '',
  grpc_ping_timeout: '',
  grpc_permit_without_stream: 0
})

const rules = {
  name: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择协议类型', trigger: 'change' }],
  address: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }]
}

const getProtocolTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    shadowsocks: 'success',
    ssr: 'warning',
    vmess: 'primary',
    vless: 'info',
    trojan: 'danger',
    hysteria: 'success',
    hysteria2: 'success',
    tuic: 'warning',
    naiveproxy: 'info'
  }
  return typeMap[type] || 'default'
}

const loadNodes = async () => {
  loading.value = true
  try {
    const data = await nodesStore.getNodesBySubscription()
    nodesBySubscription.value = data
    
    // 设置默认选中的选项卡
    if (Object.keys(data).length > 0) {
      activeTab.value = Object.keys(data)[0]
    }
  } catch (error) {
    ElMessage.error('加载节点失败')
  } finally {
    loading.value = false
  }
}

const editNode = (node: Node) => {
  editingNode.value = node
  nodeForm.value = {
    name: node.name,
    type: node.type,
    address: node.address,
    port: node.port,
    uuid: node.uuid || '',
    password: node.password || '',
    security: node.security || 'aes-256-gcm',
    network: node.network || 'tcp',
    group: node.group || '',
    tls_enabled: node.tls_enabled ?? 1,
    tls_server_name: node.tls_server_name || '',
    tls_insecure: node.tls_insecure ?? 0,
    transport_type: node.transport_type || '',
    grpc_service_name: node.grpc_service_name || '',
    grpc_idle_timeout: node.grpc_idle_timeout || '',
    grpc_ping_timeout: node.grpc_ping_timeout || '',
    grpc_permit_without_stream: node.grpc_permit_without_stream ?? 0
  }
  showAddDialog.value = true
}

const saveNode = async () => {
  if (!nodeFormRef.value) return
  
  try {
    await nodeFormRef.value.validate()
    
    if (editingNode.value) {
      await nodesStore.updateNode(editingNode.value.id, nodeForm.value)
      ElMessage.success('更新成功')
    } else {
      await nodesStore.addNode(nodeForm.value)
      ElMessage.success('添加成功')
    }
    
    showAddDialog.value = false
    editingNode.value = null
    resetForm()
    await loadNodes()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteNode = async (node: Node) => {
  try {
    await ElMessageBox.confirm('确定要删除这个节点吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await nodesStore.deleteNode(node.id)
    ElMessage.success('删除成功')
    await loadNodes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const resetForm = () => {
  nodeForm.value = {
    name: '',
    type: 'shadowsocks',
    address: '',
    port: 443,
    uuid: '',
    password: '',
    security: 'aes-256-gcm',
    network: 'tcp',
    group: '',
    tls_enabled: 1,
    tls_server_name: '',
    tls_insecure: 0,
    transport_type: '',
    grpc_service_name: '',
    grpc_idle_timeout: '',
    grpc_ping_timeout: '',
    grpc_permit_without_stream: 0
  }
}

const onTypeChange = () => {
  // 根据协议类型设置默认值
  switch (nodeForm.value.type) {
    case 'shadowsocks':
      nodeForm.value.security = 'aes-256-gcm'
      nodeForm.value.network = 'tcp'
      nodeForm.value.uuid = ''
      break
    case 'ssr':
      nodeForm.value.security = 'aes-256-cfb'
      nodeForm.value.network = 'origin+plain'
      nodeForm.value.uuid = ''
      break
    case 'vmess':
      nodeForm.value.security = 'auto'
      nodeForm.value.network = 'tcp'
      nodeForm.value.password = ''
      break
    case 'vless':
      nodeForm.value.security = 'none'
      nodeForm.value.network = 'tcp'
      nodeForm.value.password = ''
      break
    case 'trojan':
      nodeForm.value.security = 'tls'
      nodeForm.value.network = 'tcp'
      nodeForm.value.uuid = ''
      break
    case 'hysteria':
      nodeForm.value.security = 'none'
      nodeForm.value.network = 'udp'
      nodeForm.value.uuid = ''
      break
    case 'hysteria2':
      nodeForm.value.security = 'none'
      nodeForm.value.network = 'udp'
      nodeForm.value.uuid = ''
      break
    case 'tuic':
      nodeForm.value.security = 'none'
      nodeForm.value.network = 'udp'
      nodeForm.value.uuid = ''
      break
    case 'naiveproxy':
      nodeForm.value.security = 'none'
      nodeForm.value.network = 'tcp'
      nodeForm.value.uuid = ''
      break
  }
}

const onDeleteAllNodes = async () => {
  try {
    await ElMessageBox.confirm('确定要删除所有节点吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await nodesStore.deleteAllNodes()
    ElMessage.success('所有节点已删除')
    await loadNodes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 测试节点速度
const testNodeSpeed = async (node: Node) => {
  try {
    // 设置测试状态
    node.testing = true
    
    const response = await fetch('/api/nodes/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        node_id: node.id,
        url: 'https://www.google.com'
      })
    })
    
    if (!response.ok) {
      throw new Error('测试失败')
    }
    
    const result = await response.json()
    
    // 更新节点数据
    node.latency = result.latency
    node.speed = result.speed
    node.lastTestTime = new Date().toLocaleString()
    
    ElMessage.success(`测试完成: 延迟 ${result.latency}ms, 速度 ${result.speed}`)
  } catch (error) {
    ElMessage.error('速度测试失败')
    console.error('Speed test error:', error)
  } finally {
    node.testing = false
  }
}

// 批量测试速度
const batchTestSpeed = async () => {
  try {
    batchTesting.value = true
    
    // 获取所有节点ID
    const allNodeIds: string[] = []
    Object.values(nodesBySubscription.value).forEach(nodes => {
      nodes.forEach(node => {
        allNodeIds.push(node.id)
      })
    })
    
    if (allNodeIds.length === 0) {
      ElMessage.warning('没有可测试的节点')
      return
    }
    
    const response = await fetch('/api/nodes/batch-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        node_ids: allNodeIds,
        url: 'https://www.google.com'
      })
    })
    
    if (!response.ok) {
      throw new Error('批量测试失败')
    }
    
    const result = await response.json()
    
    // 更新所有节点的测试结果
    result.results.forEach((testResult: any) => {
      Object.values(nodesBySubscription.value).forEach(nodes => {
        const node = nodes.find(n => n.id === testResult.node_id)
        if (node && testResult.status === 'success') {
          node.latency = testResult.latency
          node.speed = testResult.speed
          node.lastTestTime = new Date().toLocaleString()
        }
      })
    })
    
    ElMessage.success(`批量测试完成，共测试 ${result.results.length} 个节点`)
  } catch (error) {
    ElMessage.error('批量测试失败')
    console.error('Batch test error:', error)
  } finally {
    batchTesting.value = false
  }
}

// 自动检测节点
const autoDetectNodes = async () => {
  try {
    autoDetecting.value = true
    
    // 获取所有节点ID
    const allNodeIds: string[] = []
    Object.values(nodesBySubscription.value).forEach(nodes => {
      nodes.forEach(node => {
        allNodeIds.push(node.id)
      })
    })
    
    if (allNodeIds.length === 0) {
      ElMessage.warning('没有可检测的节点')
      return
    }
    
    const response = await fetch('/api/nodes/auto-detect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        node_ids: allNodeIds
      })
    })
    
    if (!response.ok) {
      throw new Error('自动检测失败')
    }
    
    const result = await response.json()
    
    // 更新所有节点的检测结果
    result.results.forEach((testResult: any) => {
      Object.values(nodesBySubscription.value).forEach(nodes => {
        const node = nodes.find(n => n.id === testResult.node_id)
        if (node && testResult.status === 'success') {
          node.latency = testResult.latency
          node.speed = testResult.speed
          node.lastTestTime = new Date().toLocaleString()
        }
      })
    })
    
    ElMessage.success(`自动检测完成，共检测 ${result.results.length} 个节点`)
  } catch (error) {
    ElMessage.error('自动检测失败')
    console.error('Auto detect error:', error)
  } finally {
    autoDetecting.value = false
  }
}

// 获取延迟样式类
const getLatencyClass = (latency: number) => {
  if (latency < 100) return 'latency-excellent'
  if (latency < 200) return 'latency-good'
  if (latency < 300) return 'latency-normal'
  return 'latency-poor'
}

onMounted(() => {
  loadNodes()
})
</script>

<style scoped>
.nodes {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 延迟样式 */
.latency-excellent {
  color: #67c23a;
  font-weight: bold;
}

.latency-good {
  color: #409eff;
  font-weight: bold;
}

.latency-normal {
  color: #e6a23c;
  font-weight: bold;
}

.latency-poor {
  color: #f56c6c;
  font-weight: bold;
}

.speed-value {
  color: #409eff;
  font-weight: bold;
}

.no-test {
  color: #909399;
  font-style: italic;
}
</style> 