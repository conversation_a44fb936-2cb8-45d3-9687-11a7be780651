<template>
  <div class="subscriptions">
    <div class="page-header">
      <h2>订阅管理</h2>
      <div class="header-actions">
        <!-- 根据当前Tab显示不同的操作按钮 -->
        <template v-if="activeTab === 'subscriptions'">
          <el-button @click="checkSchedulerStatus" :loading="schedulerLoading">
            调度器状态
          </el-button>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加订阅
          </el-button>
        </template>
        <template v-else-if="activeTab === 'nodes'">
          <el-button type="success" @click="batchTestSpeed" :loading="batchTesting">
            <el-icon><Refresh /></el-icon>
            批量测试
          </el-button>
          <el-button type="warning" @click="autoDetectNodes" :loading="autoDetecting">
            <el-icon><Monitor /></el-icon>
            自动检测
          </el-button>
          <el-button type="primary" @click="showAddNodeDialog = true">
            <el-icon><Plus /></el-icon>
            添加节点
          </el-button>
          <el-button type="danger" @click="onDeleteAllNodes" style="margin-left: 12px;">
            <el-icon><Delete /></el-icon>
            删除所有节点
          </el-button>
        </template>
      </div>
    </div>

    <!-- Tab页结构 -->
    <el-tabs v-model="activeTab" type="card">
      <!-- 订阅列表Tab -->
      <el-tab-pane label="订阅列表" name="subscriptions">
        <!-- 调度器状态卡片 -->
        <el-card v-if="schedulerStatus" style="margin-bottom: 20px;">
          <template #header>
            <div class="card-header">
              <span>自动更新调度器</span>
              <div>
                <el-button
                  v-if="!schedulerStatus.running"
                  type="success"
                  size="small"
                  @click="startScheduler"
                  :loading="schedulerLoading"
                >
                  启动
                </el-button>
                <el-button
                  v-else
                  type="danger"
                  size="small"
                  @click="stopScheduler"
                  :loading="schedulerLoading"
                >
                  停止
                </el-button>
              </div>
            </div>
          </template>
          <div class="scheduler-status">
            <el-tag :type="schedulerStatus.running ? 'success' : 'danger'">
              {{ schedulerStatus.running ? '运行中' : '已停止' }}
            </el-tag>
            <span style="margin-left: 10px; color: #666;">
              {{ schedulerStatus.message }}
            </span>
          </div>
        </el-card>

        <el-table :data="subscriptions" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="订阅名称" width="150" />
          <el-table-column prop="url" label="订阅地址" min-width="300" show-overflow-tooltip />
          <el-table-column prop="nodeCount" label="节点数量" width="100">
            <template #default="{ row }">
              <el-button
                type="text"
                @click="activeTab = 'nodes'; selectedSubscription = row.name"
                :disabled="!row.nodeCount"
              >
                {{ row.nodeCount || 0 }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="lastUpdate" label="最后更新" width="150" />
          <el-table-column prop="enabled" label="状态" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                @change="toggleSubscription(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="updateSubscription(row)">更新</el-button>
              <el-button size="small" @click="editSubscription(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteSubscription(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 节点管理Tab -->
      <el-tab-pane label="节点管理" name="nodes">
        <!-- 按订阅分组的选项卡 -->
        <el-tabs v-model="nodeActiveTab" type="card" v-loading="nodeLoading">
          <el-tab-pane
            v-for="(nodes, subscriptionName) in nodesBySubscription"
            :key="subscriptionName"
            :label="`${subscriptionName} (${nodes.length})`"
            :name="subscriptionName"
          >
            <el-table :data="nodes" style="width: 100%">
              <el-table-column prop="name" label="节点名称" width="150" />
              <el-table-column prop="type" label="协议类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getProtocolTagType(row.type)">{{ row.type.toUpperCase() }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="address" label="服务器地址" width="150" />
              <el-table-column prop="port" label="端口" width="80" />
              <el-table-column prop="group" label="分组" width="100" />
              <el-table-column prop="security" label="加密方式" width="120" />
              <el-table-column prop="network" label="传输协议" width="120" />
              <el-table-column label="延迟" width="100">
                <template #default="{ row }">
                  <span v-if="row.latency" :class="getLatencyClass(row.latency)">
                    {{ row.latency }}ms
                  </span>
                  <span v-else class="no-test">未测试</span>
                </template>
              </el-table-column>
              <el-table-column label="速度" width="120">
                <template #default="{ row }">
                  <span v-if="row.speed" class="speed-value">
                    {{ row.speed }}
                  </span>
                  <span v-else class="no-test">未测试</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-tag v-if="row.available !== undefined" :type="row.available ? 'success' : 'danger'">
                    {{ row.available ? '可用' : '不可用' }}
                  </el-tag>
                  <el-tag v-else type="info">未检测</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="280" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" @click="testNodeSpeed(row)" :loading="row.testing">
                    测试速度
                  </el-button>
                  <el-button size="small" @click="editNode(row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteNode(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingSubscription ? '编辑订阅' : '添加订阅'"
      width="600px"
    >
      <el-form :model="subscriptionForm" label-width="100px" :rules="rules" ref="subscriptionFormRef">
        <el-form-item label="订阅名称" prop="name">
          <el-input v-model="subscriptionForm.name" placeholder="请输入订阅名称" />
        </el-form-item>
        <el-form-item label="订阅地址" prop="url">
          <el-input v-model="subscriptionForm.url" placeholder="请输入订阅地址" />
        </el-form-item>
        <el-form-item label="更新间隔" prop="updateInterval">
          <el-input-number v-model="subscriptionForm.updateInterval" :min="1" :max="168" />
          <span style="margin-left: 10px;">小时</span>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="subscriptionForm.description" type="textarea" placeholder="请输入描述信息" />
        </el-form-item>
        <el-form-item label="启用" prop="enabled">
          <el-switch v-model="subscriptionForm.enabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSubscription">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 节点添加/编辑对话框 -->
    <el-dialog
      v-model="showAddNodeDialog"
      :title="editingNode ? '编辑节点' : '添加节点'"
      width="600px"
    >
      <el-form :model="nodeForm" label-width="120px" :rules="nodeRules" ref="nodeFormRef">
        <el-form-item label="节点名称" prop="name">
          <el-input v-model="nodeForm.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="协议类型" prop="type">
          <el-select v-model="nodeForm.type" placeholder="请选择协议类型" style="width: 100%" @change="onNodeTypeChange">
            <el-option label="Shadowsocks" value="shadowsocks" />
            <el-option label="VMess" value="vmess" />
            <el-option label="VLESS" value="vless" />
            <el-option label="Trojan" value="trojan" />
            <el-option label="Hysteria" value="hysteria" />
            <el-option label="Hysteria2" value="hysteria2" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器地址" prop="address">
          <el-input v-model="nodeForm.address" placeholder="请输入服务器地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="nodeForm.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>

        <!-- Shadowsocks 特有字段 -->
        <template v-if="nodeForm.type === 'shadowsocks'">
          <el-form-item label="密码" prop="password">
            <el-input v-model="nodeForm.password" type="password" placeholder="请输入密码" />
          </el-form-item>
          <el-form-item label="加密方式" prop="security">
            <el-select v-model="nodeForm.security" placeholder="请选择加密方式" style="width: 100%">
              <el-option label="aes-128-gcm" value="aes-128-gcm" />
              <el-option label="aes-256-gcm" value="aes-256-gcm" />
              <el-option label="chacha20-ietf-poly1305" value="chacha20-ietf-poly1305" />
              <el-option label="xchacha20-ietf-poly1305" value="xchacha20-ietf-poly1305" />
              <el-option label="2022-blake3-aes-128-gcm" value="2022-blake3-aes-128-gcm" />
              <el-option label="2022-blake3-aes-256-gcm" value="2022-blake3-aes-256-gcm" />
              <el-option label="2022-blake3-chacha20-poly1305" value="2022-blake3-chacha20-poly1305" />
            </el-select>
          </el-form-item>
        </template>

        <!-- VMess/VLESS 特有字段 -->
        <template v-if="['vmess', 'vless'].includes(nodeForm.type)">
          <el-form-item label="UUID" prop="uuid">
            <el-input v-model="nodeForm.uuid" placeholder="请输入UUID" />
          </el-form-item>
          <el-form-item v-if="nodeForm.type === 'vmess'" label="加密方式" prop="security">
            <el-select v-model="nodeForm.security" placeholder="请选择加密方式" style="width: 100%">
              <el-option label="auto" value="auto" />
              <el-option label="aes-128-gcm" value="aes-128-gcm" />
              <el-option label="chacha20-poly1305" value="chacha20-poly1305" />
              <el-option label="none" value="none" />
            </el-select>
          </el-form-item>
        </template>

        <!-- Trojan 特有字段 -->
        <template v-if="nodeForm.type === 'trojan'">
          <el-form-item label="密码" prop="password">
            <el-input v-model="nodeForm.password" type="password" placeholder="请输入密码" />
          </el-form-item>
        </template>

        <!-- Hysteria/Hysteria2 特有字段 -->
        <template v-if="['hysteria', 'hysteria2'].includes(nodeForm.type)">
          <el-form-item label="密码" prop="password">
            <el-input v-model="nodeForm.password" type="password" placeholder="请输入密码" />
          </el-form-item>
          <el-form-item label="上行速度" prop="up_mbps">
            <el-input-number v-model="nodeForm.up_mbps" :min="1" placeholder="上行速度 (Mbps)" style="width: 100%" />
          </el-form-item>
          <el-form-item label="下行速度" prop="down_mbps">
            <el-input-number v-model="nodeForm.down_mbps" :min="1" placeholder="下行速度 (Mbps)" style="width: 100%" />
          </el-form-item>
        </template>

        <!-- 传输协议配置 (VMess/VLESS/Trojan) -->
        <template v-if="['vmess', 'vless', 'trojan'].includes(nodeForm.type)">
          <el-form-item label="传输协议" prop="network">
            <el-select v-model="nodeForm.network" placeholder="请选择传输协议" style="width: 100%" @change="onNetworkChange">
              <el-option label="TCP" value="tcp" />
              <el-option label="WebSocket" value="ws" />
              <el-option label="gRPC" value="grpc" />
              <el-option label="HTTP/2" value="h2" />
              <el-option label="QUIC" value="quic" />
            </el-select>
          </el-form-item>

          <!-- WebSocket 配置 -->
          <template v-if="nodeForm.network === 'ws'">
            <el-form-item label="WebSocket路径" prop="ws_path">
              <el-input v-model="nodeForm.ws_path" placeholder="如: /path" />
            </el-form-item>
            <el-form-item label="WebSocket头部" prop="ws_headers">
              <el-input v-model="nodeForm.ws_headers" type="textarea" :rows="2" placeholder='如: {"Host": "example.com"}' />
            </el-form-item>
          </template>

          <!-- gRPC 配置 -->
          <template v-if="nodeForm.network === 'grpc'">
            <el-form-item label="gRPC服务名" prop="grpc_service_name">
              <el-input v-model="nodeForm.grpc_service_name" placeholder="请输入gRPC服务名" />
            </el-form-item>
          </template>

          <!-- HTTP/2 配置 -->
          <template v-if="nodeForm.network === 'h2'">
            <el-form-item label="HTTP/2路径" prop="h2_path">
              <el-input v-model="nodeForm.h2_path" placeholder="如: /path" />
            </el-form-item>
            <el-form-item label="HTTP/2主机" prop="h2_host">
              <el-input v-model="nodeForm.h2_host" placeholder="如: example.com" />
            </el-form-item>
          </template>
        </template>

        <!-- TLS 配置 (除Shadowsocks外都支持) -->
        <template v-if="nodeForm.type !== 'shadowsocks'">
          <el-form-item label="启用TLS">
            <el-switch v-model="nodeForm.tls_enabled" />
          </el-form-item>
          <template v-if="nodeForm.tls_enabled">
            <el-form-item label="TLS服务器名" prop="tls_server_name">
              <el-input v-model="nodeForm.tls_server_name" placeholder="如: example.com" />
            </el-form-item>
            <el-form-item label="跳过证书验证">
              <el-switch v-model="nodeForm.tls_insecure" />
            </el-form-item>
          </template>
        </template>

        <!-- 订阅分组字段：只在添加时显示，编辑时隐藏 -->
        <el-form-item v-if="!editingNode" label="订阅分组" prop="subscription">
          <el-select v-model="nodeForm.subscription" placeholder="请选择订阅分组" style="width: 100%">
            <el-option
              v-for="sub in subscriptions"
              :key="sub.id"
              :label="sub.name"
              :value="sub.name"
            />
          </el-select>
        </el-form-item>

        <!-- 编辑时显示当前订阅分组（只读） -->
        <el-form-item v-else label="订阅分组">
          <el-input v-model="nodeForm.subscription" readonly style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddNodeDialog = false">取消</el-button>
          <el-button type="primary" @click="saveNode">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Plus, Refresh, Monitor, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSubscriptionsStore } from '@/store/subscriptions'
import { useNodesStore } from '@/store/nodes'
import type { Subscription } from '@/store/subscriptions'
import type { Node } from '@/store/nodes'

const subscriptionsStore = useSubscriptionsStore()
const nodesStore = useNodesStore()

// Tab 控制
const activeTab = ref('subscriptions')
const nodeActiveTab = ref('')
const selectedSubscription = ref('')

// 订阅管理相关
const loading = ref(false)
const schedulerLoading = ref(false)
const showAddDialog = ref(false)
const editingSubscription = ref<Subscription | null>(null)
const subscriptionFormRef = ref()
const schedulerStatus = ref<{ running: boolean; message: string } | null>(null)

const subscriptions = ref<Subscription[]>([])

const subscriptionForm = ref({
  name: '',
  url: '',
  updateInterval: 24,
  description: '',
  enabled: true
})

// 节点管理相关
const nodeLoading = ref(false)
const showAddNodeDialog = ref(false)
const editingNode = ref<Node | null>(null)
const nodeFormRef = ref()
const batchTesting = ref(false)
const autoDetecting = ref(false)

const nodes = ref<Node[]>([])
const nodesBySubscription = ref<Record<string, Node[]>>({})

const nodeForm = ref({
  name: '',
  type: 'shadowsocks',
  address: '',
  port: 443,
  subscription: '',
  // 认证信息
  uuid: '',
  password: '',
  security: '',
  // 传输协议
  network: 'tcp',
  // WebSocket 配置
  ws_path: '',
  ws_headers: '',
  // gRPC 配置
  grpc_service_name: '',
  // HTTP/2 配置
  h2_path: '',
  h2_host: '',
  // TLS 配置
  tls_enabled: false,
  tls_server_name: '',
  tls_insecure: false,
  // Hysteria 配置
  up_mbps: 100,
  down_mbps: 100
})

const rules = {
  name: [{ required: true, message: '请输入订阅名称', trigger: 'blur' }],
  url: [{ required: true, message: '请输入订阅地址', trigger: 'blur' }],
  updateInterval: [{ required: true, message: '请输入更新间隔', trigger: 'blur' }]
}

const nodeRules = computed(() => {
  const baseRules = {
    name: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择协议类型', trigger: 'change' }],
    address: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
    port: [{ required: true, message: '请输入端口', trigger: 'blur' }]
  }

  // 订阅分组字段：只在添加时必填，编辑时不验证
  if (!editingNode.value) {
    baseRules.subscription = [{ required: true, message: '请选择订阅分组', trigger: 'change' }]
  }

  // 根据协议类型添加特定验证规则
  if (nodeForm.value.type === 'shadowsocks') {
    baseRules.password = [{ required: true, message: '请输入密码', trigger: 'blur' }]
    baseRules.security = [{ required: true, message: '请选择加密方式', trigger: 'change' }]
  } else if (['vmess', 'vless'].includes(nodeForm.value.type)) {
    baseRules.uuid = [{ required: true, message: '请输入UUID', trigger: 'blur' }]
    if (nodeForm.value.type === 'vmess') {
      baseRules.security = [{ required: true, message: '请选择加密方式', trigger: 'change' }]
    }
  } else if (nodeForm.value.type === 'trojan') {
    baseRules.password = [{ required: true, message: '请输入密码', trigger: 'blur' }]
  } else if (['hysteria', 'hysteria2'].includes(nodeForm.value.type)) {
    baseRules.password = [{ required: true, message: '请输入密码', trigger: 'blur' }]
  }

  return baseRules
})



const loadSubscriptions = async () => {
  loading.value = true
  try {
    const data = await subscriptionsStore.getSubscriptions()
    subscriptions.value = data
  } catch (error) {
    ElMessage.error('加载订阅失败')
  } finally {
    loading.value = false
  }
}

const editSubscription = (subscription: Subscription) => {
  editingSubscription.value = subscription
  subscriptionForm.value = { 
    name: subscription.name,
    url: subscription.url,
    updateInterval: subscription.updateInterval,
    description: subscription.description,
    enabled: subscription.enabled
  }
  showAddDialog.value = true
}

const saveSubscription = async () => {
  if (!subscriptionFormRef.value) return
  
  try {
    await subscriptionFormRef.value.validate()
    
    if (editingSubscription.value) {
      await subscriptionsStore.updateSubscription(editingSubscription.value.id, subscriptionForm.value)
      ElMessage.success('更新成功')
    } else {
      await subscriptionsStore.addSubscription(subscriptionForm.value)
      ElMessage.success('添加成功')
    }
    showAddDialog.value = false
    editingSubscription.value = null
    resetForm()
    await loadSubscriptions()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteSubscription = async (subscription: Subscription) => {
  try {
    await ElMessageBox.confirm('确定要删除这个订阅吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await subscriptionsStore.deleteSubscription(subscription.id)
    ElMessage.success('删除成功')
    await loadSubscriptions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const toggleSubscription = async (subscription: Subscription) => {
  try {
    await subscriptionsStore.updateSubscription(subscription.id, { enabled: subscription.enabled })
    ElMessage.success(subscription.enabled ? '已启用' : '已禁用')
  } catch (error) {
    subscription.enabled = !subscription.enabled // 恢复状态
    ElMessage.error('操作失败')
  }
}

const updateSubscription = async (subscription: Subscription) => {
  try {
    await subscriptionsStore.updateSubscriptionNodes(subscription.id)
    ElMessage.success('更新成功')
    await loadSubscriptions()
  } catch (error) {
    ElMessage.error('更新失败')
  }
}

const resetForm = () => {
  subscriptionForm.value = {
    name: '',
    url: '',
    updateInterval: 24,
    description: '',
    enabled: true
  }
}

const checkSchedulerStatus = async () => {
  schedulerLoading.value = true
  try {
    schedulerStatus.value = await subscriptionsStore.getSchedulerStatus()
  } catch (error) {
    ElMessage.error('获取调度器状态失败')
  } finally {
    schedulerLoading.value = false
  }
}

const startScheduler = async () => {
  schedulerLoading.value = true
  try {
    await subscriptionsStore.startScheduler()
    ElMessage.success('调度器启动成功')
    await checkSchedulerStatus()
  } catch (error) {
    ElMessage.error('启动调度器失败')
  } finally {
    schedulerLoading.value = false
  }
}

const stopScheduler = async () => {
  schedulerLoading.value = true
  try {
    await subscriptionsStore.stopScheduler()
    ElMessage.success('调度器停止成功')
    await checkSchedulerStatus()
  } catch (error) {
    ElMessage.error('停止调度器失败')
  } finally {
    schedulerLoading.value = false
  }
}

// 协议类型变化处理
const onNodeTypeChange = (type: string) => {
  // 重置相关字段
  nodeForm.value.uuid = ''
  nodeForm.value.password = ''
  nodeForm.value.security = ''
  nodeForm.value.network = 'tcp'
  nodeForm.value.tls_enabled = false

  // 根据协议类型设置默认值
  switch (type) {
    case 'shadowsocks':
      nodeForm.value.security = 'aes-256-gcm'
      nodeForm.value.port = 443
      break
    case 'vmess':
      nodeForm.value.security = 'auto'
      nodeForm.value.port = 443
      nodeForm.value.tls_enabled = true
      break
    case 'vless':
      nodeForm.value.port = 443
      nodeForm.value.tls_enabled = true
      break
    case 'trojan':
      nodeForm.value.port = 443
      nodeForm.value.tls_enabled = true
      break
    case 'hysteria':
    case 'hysteria2':
      nodeForm.value.port = 443
      nodeForm.value.tls_enabled = true
      break
  }
}

// 传输协议变化处理
const onNetworkChange = (network: string) => {
  // 重置传输相关字段
  nodeForm.value.ws_path = ''
  nodeForm.value.ws_headers = ''
  nodeForm.value.grpc_service_name = ''
  nodeForm.value.h2_path = ''
  nodeForm.value.h2_host = ''

  // 根据传输协议设置默认值
  switch (network) {
    case 'ws':
      nodeForm.value.ws_path = '/'
      break
    case 'grpc':
      nodeForm.value.grpc_service_name = 'GunService'
      break
    case 'h2':
      nodeForm.value.h2_path = '/'
      break
  }
}

// 节点管理方法
const loadNodes = async () => {
  nodeLoading.value = true
  try {
    const data = await nodesStore.getNodesBySubscription()
    // 清空原有数据并赋值新数据
    nodesBySubscription.value = data

    // 设置默认的节点Tab
    if (Object.keys(data).length > 0 && !nodeActiveTab.value) {
      nodeActiveTab.value = Object.keys(data)[0]
    }
  } catch (error) {
    ElMessage.error('加载节点失败')
  } finally {
    nodeLoading.value = false
  }
}

const editNode = (node: Node) => {
  editingNode.value = node
  nodeForm.value = {
    name: node.name,
    type: node.type,
    address: node.address,
    port: node.port,
    subscription: node.subscription || '',
    // 认证信息
    uuid: node.uuid || '',
    password: node.password || '',
    security: node.security || '',
    // 传输协议
    network: node.network || 'tcp',
    // WebSocket 配置
    ws_path: node.transport?.path || '',
    ws_headers: JSON.stringify(node.transport?.headers || {}),
    // gRPC 配置
    grpc_service_name: node.grpc_service_name || '',
    // HTTP/2 配置
    h2_path: node.transport?.path || '',
    h2_host: node.transport?.host || '',
    // TLS 配置
    tls_enabled: Boolean(node.tls_enabled),
    tls_server_name: node.tls_server_name || '',
    tls_insecure: Boolean(node.tls_insecure),
    // Hysteria 配置
    up_mbps: node.up_mbps || 100,
    down_mbps: node.down_mbps || 100
  }
  showAddNodeDialog.value = true
}

const saveNode = async () => {
  if (!nodeFormRef.value) return

  try {
    await nodeFormRef.value.validate()

    if (editingNode.value) {
      await nodesStore.updateNode(editingNode.value.id, nodeForm.value)
      ElMessage.success('更新成功')
    } else {
      await nodesStore.addNode(nodeForm.value)
      ElMessage.success('添加成功')
    }
    showAddNodeDialog.value = false
    editingNode.value = null
    resetNodeForm()
    await loadNodes()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteNode = async (node: Node) => {
  try {
    await ElMessageBox.confirm('确定要删除这个节点吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await nodesStore.deleteNode(node.id)
    ElMessage.success('删除成功')
    await loadNodes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const resetNodeForm = () => {
  nodeForm.value = {
    name: '',
    type: 'shadowsocks',
    address: '',
    port: 443,
    subscription: '',
    // 认证信息
    uuid: '',
    password: '',
    security: 'aes-256-gcm',
    // 传输协议
    network: 'tcp',
    // WebSocket 配置
    ws_path: '',
    ws_headers: '',
    // gRPC 配置
    grpc_service_name: '',
    // HTTP/2 配置
    h2_path: '',
    h2_host: '',
    // TLS 配置
    tls_enabled: false,
    tls_server_name: '',
    tls_insecure: false,
    // Hysteria 配置
    up_mbps: 100,
    down_mbps: 100
  }
}

const testNodeSpeed = async (node: Node) => {
  node.testing = true
  try {
    // TODO: 实现节点测速功能
    ElMessage.info('节点测速功能开发中')
    await loadNodes()
  } catch (error) {
    ElMessage.error('测试失败')
  } finally {
    node.testing = false
  }
}

const batchTestSpeed = async () => {
  batchTesting.value = true
  try {
    // TODO: 实现批量测试功能
    ElMessage.info('批量测试功能开发中')
    await loadNodes()
  } catch (error) {
    ElMessage.error('批量测试失败')
  } finally {
    batchTesting.value = false
  }
}

const autoDetectNodes = async () => {
  autoDetecting.value = true
  try {
    // TODO: 实现自动检测功能
    ElMessage.info('自动检测功能开发中')
    await loadNodes()
  } catch (error) {
    ElMessage.error('自动检测失败')
  } finally {
    autoDetecting.value = false
  }
}

const onDeleteAllNodes = async () => {
  try {
    await ElMessageBox.confirm('确定要删除所有节点吗？此操作不可恢复！', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await nodesStore.deleteAllNodes()
    ElMessage.success('删除成功')
    await loadNodes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 辅助方法
const getProtocolTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'shadowsocks': 'primary',
    'vmess': 'success',
    'vless': 'warning',
    'trojan': 'danger',
    'hysteria': 'info',
    'hysteria2': 'info'
  }
  return typeMap[type] || ''
}

const getLatencyClass = (latency: number) => {
  if (latency < 100) return 'latency-good'
  if (latency < 300) return 'latency-medium'
  return 'latency-bad'
}

onMounted(() => {
  loadSubscriptions()
  loadNodes()
  checkSchedulerStatus()
})
</script>

<style scoped>
.subscriptions {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scheduler-status {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-table .cell) {
  word-break: break-all;
}

/* 节点管理相关样式 */
.latency-good {
  color: #67c23a;
  font-weight: bold;
}

.latency-medium {
  color: #e6a23c;
  font-weight: bold;
}

.latency-bad {
  color: #f56c6c;
  font-weight: bold;
}

.no-test {
  color: #909399;
  font-style: italic;
}

.speed-value {
  color: #409eff;
  font-weight: bold;
}
</style> 