<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="status-card">
          <template #header>
            <div class="card-header">
              <span>核心状态</span>
              <el-tag :type="coreStatus.type">{{ coreStatus.text }}</el-tag>
            </div>
          </template>
          <div class="status-content">
            <p><strong>版本:</strong> {{ coreInfo.version || '未知' }}</p>
            <p><strong>运行时间:</strong> {{ coreInfo.uptime || '0s' }}</p>
            <p><strong>内存使用:</strong> {{ coreInfo.memory || '0MB' }}</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="status-card">
          <template #header>
            <div class="card-header">
              <span>连接统计</span>
            </div>
          </template>
          <div class="status-content">
            <p><strong>活跃连接:</strong> {{ stats.activeConnections || 0 }}</p>
            <p><strong>总流量:</strong> {{ stats.totalTraffic || '0MB' }}</p>
            <p><strong>今日流量:</strong> {{ stats.todayTraffic || '0MB' }}</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="status-card">
          <template #header>
            <div class="card-header">
              <span>配置信息</span>
            </div>
          </template>
          <div class="status-content">
            <p><strong>入站数量:</strong> {{ configInfo.inboundCount || 0 }}</p>
            <p><strong>出站数量:</strong> {{ configInfo.outboundCount || 0 }}</p>
            <p><strong>路由规则:</strong> {{ configInfo.routeCount || 0 }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="startCore" :disabled="coreStatus.running">
              启动核心
            </el-button>
            <el-button type="danger" @click="stopCore" :disabled="!coreStatus.running">
              停止核心
            </el-button>
            <el-button type="warning" @click="reloadConfig" :disabled="!coreStatus.running">
              重载配置
            </el-button>
            <el-button @click="updateCore">
              更新核心
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统日志</span>
              <div class="log-controls">
                <el-select v-model="selectedLogLevel" placeholder="日志级别" size="small" style="width: 100px; margin-right: 10px;">
                  <el-option label="全部" value="all"></el-option>
                  <el-option label="信息" value="info"></el-option>
                  <el-option label="警告" value="warn"></el-option>
                  <el-option label="错误" value="error"></el-option>
                </el-select>
                <el-button size="small" @click="clearLogs" type="danger">清除日志</el-button>
              </div>
            </div>
          </template>
          <div class="log-container">
            <div v-for="(log, index) in filteredLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            <div v-if="filteredLogs.length === 0" class="no-logs">
              暂无日志记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useCoreStore } from '@/store/core'
import { useStatsStore } from '@/store/stats'

const coreStore = useCoreStore()
const statsStore = useStatsStore()

// 定时器引用
let statusTimer: NodeJS.Timeout | null = null
let statsTimer: NodeJS.Timeout | null = null

// 计算属性：核心状态
const coreStatus = computed(() => {
  const running = coreStore.status.running
  return {
    running,
    type: running ? 'success' : 'danger',
    text: running ? '运行中' : '未运行'
  }
})

// 计算属性：核心信息
const coreInfo = computed(() => ({
  version: coreStore.status.version || '未知',
  uptime: coreStore.status.uptime || '0s',
  memory: coreStore.status.memory || '0MB'
}))

// 计算属性：统计信息
const stats = computed(() => ({
  activeConnections: statsStore.stats.activeConnections || 0,
  totalTraffic: statsStore.stats.totalTraffic || '0MB',
  todayTraffic: statsStore.stats.todayTraffic || '0MB'
}))

const configInfo = ref({
  inboundCount: 0,
  outboundCount: 0,
  routeCount: 0
})

const recentLogs = ref([
  { time: '12:34:56', level: 'info', message: '系统启动完成' },
  { time: '12:34:55', level: 'warn', message: '配置文件加载中...' },
  { time: '12:34:54', level: 'error', message: '核心启动失败' }
])

// 添加新日志的函数
const addLog = (level: 'info' | 'warn' | 'error', message: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  recentLogs.value.unshift({ time, level, message })
  
  // 限制日志数量，最多保留50条
  if (recentLogs.value.length > 50) {
    recentLogs.value = recentLogs.value.slice(0, 50)
  }
}

const selectedLogLevel = ref('all')

// 计算属性：过滤后的日志
const filteredLogs = computed(() => {
  if (selectedLogLevel.value === 'all') {
    return recentLogs.value
  }
  return recentLogs.value.filter(log => log.level === selectedLogLevel.value)
})

const startCore = async () => {
  try {
    addLog('info', '正在启动核心...')
    await coreStore.startCore()
    // 启动成功后，重新开始状态检测
    if (coreStore.status.running) {
      addLog('info', '核心启动成功')
      console.log('核心启动成功，重新开始状态检测')
      // 重新启动状态定时器
      restartStatusTimer()
    } else {
      addLog('error', '核心启动失败')
    }
  } catch (error) {
    addLog('error', `启动核心失败: ${error}`)
    console.error('启动核心失败:', error)
  }
}

const stopCore = async () => {
  try {
    addLog('info', '正在停止核心...')
    await coreStore.stopCore()
    addLog('info', '核心已停止')
  } catch (error) {
    addLog('error', `停止核心失败: ${error}`)
    console.error('停止核心失败:', error)
  }
}

const reloadConfig = async () => {
  try {
    addLog('info', '正在重载配置...')
    await coreStore.reloadConfig()
    addLog('info', '配置重载成功')
  } catch (error) {
    addLog('error', `重载配置失败: ${error}`)
    console.error('重载配置失败:', error)
  }
}

const updateCore = async () => {
  try {
    addLog('info', '正在更新核心...')
    await coreStore.updateCore()
    addLog('info', '核心更新成功')
  } catch (error) {
    addLog('error', `更新核心失败: ${error}`)
    console.error('更新核心失败:', error)
  }
}

// 开始定时刷新
const startTimers = () => {
  // 立即获取一次状态
  fetchCoreStatus()
  fetchStats()
  
  // 每60秒（1分钟）刷新一次核心状态
  statusTimer = setInterval(() => {
    fetchCoreStatus()
  }, 60000)
  
  // 每30秒刷新一次统计信息（减少频率）
  statsTimer = setInterval(() => {
    fetchStats()
  }, 30000)
}

// 重新启动状态检测定时器
const restartStatusTimer = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
  statusTimer = setInterval(() => {
    fetchCoreStatus()
  }, 60000)
}

// 启动低频状态检测（当核心未运行时）
const startLowFrequencyTimer = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
  // 当核心未运行时，每5分钟检测一次
  statusTimer = setInterval(() => {
    fetchCoreStatus()
  }, 300000)
}

// 获取核心状态，如果失败或未运行则使用低频检测
const fetchCoreStatus = async () => {
  try {
    await coreStore.getCoreStatus()
    // 如果获取成功且正在运行，确保定时器在运行
    if (coreStore.status.running) {
      if (!statusTimer) {
        restartStatusTimer()
      }
      return
    }
  } catch (error) {
    console.error('获取核心状态失败:', error)
  }
  
  // 如果获取失败或未运行，切换到低频检测
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
  // 启动低频检测
  startLowFrequencyTimer()
  console.log('核心未运行，切换到低频状态检测（每5分钟）')
}

// 获取统计信息，如果失败则停止定时器
const fetchStats = async () => {
  try {
    await statsStore.getStats()
  } catch (error) {
    console.error('获取统计信息失败:', error)
    // 如果获取失败，停止统计定时器
    if (statsTimer) {
      clearInterval(statsTimer)
      statsTimer = null
      console.log('统计信息获取失败，停止自动统计检测')
    }
  }
}

// 停止定时刷新
const stopTimers = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
  if (statsTimer) {
    clearInterval(statsTimer)
    statsTimer = null
  }
}

const clearLogs = () => {
  recentLogs.value = []
}

onMounted(() => {
  startTimers()
})

onUnmounted(() => {
  stopTimers()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content p {
  margin: 8px 0;
}

.quick-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.log-controls {
  display: flex;
  align-items: center;
}

.no-logs {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 20px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #666;
  min-width: 60px;
}

.log-level {
  min-width: 40px;
  text-align: center;
  border-radius: 2px;
  font-size: 10px;
  padding: 1px 4px;
}

.log-info {
  background-color: #e1f3d8;
  color: #67c23a;
}

.log-warn {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.log-error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.log-message {
  flex: 1;
}
</style> 