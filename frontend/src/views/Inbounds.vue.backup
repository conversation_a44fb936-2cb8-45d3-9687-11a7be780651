<template>
  <div class="inbounds">
    <div class="page-header">
      <h2>规则管理</h2>
      <div class="header-buttons">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增规则
        </el-button>
        <el-button type="success" @click="generateConfig">
          <el-icon><Setting /></el-icon>
          配置生成
        </el-button>
      </div>
    </div>

    <el-table :data="rules" style="width: 100%" v-loading="loading">
      <el-table-column prop="priority" label="优先级" width="80" sortable />
      <el-table-column prop="name" label="规则名称" min-width="150" />
      <el-table-column prop="enabled" label="状态" width="80">
        <template #default="{ row }">
          <el-switch
            v-model="row.enabled"
            @change="toggleRule(row)"
            :disabled="loading"
          />
        </template>
      </el-table-column>
      <el-table-column label="匹配条件" min-width="200">
        <template #default="{ row }">
          <div class="rule-conditions">
            <el-tag v-if="row.domain?.length" size="small" type="info">
              域名: {{ row.domain.slice(0, 2).join(', ') }}{{ row.domain.length > 2 ? '...' : '' }}
            </el-tag>
            <el-tag v-if="row.geosite?.length" size="small" type="success">
              GeoSite: {{ row.geosite.join(', ') }}
            </el-tag>
            <el-tag v-if="row.geoip?.length" size="small" type="warning">
              GeoIP: {{ row.geoip.join(', ') }}
            </el-tag>
            <el-tag v-if="row.ip_cidr?.length" size="small">
              IP: {{ row.ip_cidr.slice(0, 1).join(', ') }}{{ row.ip_cidr.length > 1 ? '...' : '' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="outbound_type" label="出站类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getOutboundTypeColor(row.outbound_type)">
            {{ getOutboundTypeName(row.outbound_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editRule(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteRule(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingRule ? '编辑规则' : '新增规则'"
      width="800px"
    >
      <el-form :model="ruleForm" label-width="120px">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则名称" required>
              <el-input v-model="ruleForm.name" placeholder="输入规则名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优先级" required>
              <el-input-number v-model="ruleForm.priority" :min="1" :max="1000" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="启用">
              <el-switch v-model="ruleForm.enabled" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 匹配条件 -->
        <el-divider content-position="left">匹配条件</el-divider>

        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="域名规则" name="domain">
            <el-form-item label="域名匹配">
              <el-input
                v-model="domainText"
                type="textarea"
                :rows="3"
                placeholder="每行一个域名，如：&#10;example.com&#10;*.google.com"
                @input="updateDomains"
              />
            </el-form-item>
            <el-form-item label="域名后缀">
              <el-input
                v-model="domainSuffixText"
                type="textarea"
                :rows="2"
                placeholder="每行一个后缀，如：&#10;.com&#10;.cn"
                @input="updateDomainSuffix"
              />
            </el-form-item>
            <el-form-item label="域名关键词">
              <el-input
                v-model="domainKeywordText"
                type="textarea"
                :rows="2"
                placeholder="每行一个关键词"
                @input="updateDomainKeyword"
              />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="地理位置" name="geo">
            <el-form-item label="GeoSite">
              <el-select v-model="ruleForm.geosite" multiple placeholder="选择 GeoSite 规则">
                <el-option label="中国大陆 (cn)" value="cn" />
                <el-option label="Google" value="google" />
                <el-option label="YouTube" value="youtube" />
                <el-option label="Facebook" value="facebook" />
                <el-option label="Twitter" value="twitter" />
                <el-option label="Telegram" value="telegram" />
                <el-option label="Netflix" value="netflix" />
                <el-option label="Disney+" value="disney" />
                <el-option label="Apple" value="apple" />
                <el-option label="Microsoft" value="microsoft" />
              </el-select>
            </el-form-item>
            <el-form-item label="GeoIP">
              <el-select v-model="ruleForm.geoip" multiple placeholder="选择 GeoIP 规则">
                <el-option label="中国大陆 (cn)" value="cn" />
                <el-option label="美国 (us)" value="us" />
                <el-option label="日本 (jp)" value="jp" />
                <el-option label="香港 (hk)" value="hk" />
                <el-option label="台湾 (tw)" value="tw" />
                <el-option label="新加坡 (sg)" value="sg" />
                <el-option label="私有地址 (private)" value="private" />
              </el-select>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="IP/端口" name="network">
            <el-form-item label="IP段">
              <el-input
                v-model="ipCidrText"
                type="textarea"
                :rows="3"
                placeholder="每行一个IP段，如：&#10;***********/24&#10;10.0.0.0/8"
                @input="updateIPCidr"
              />
            </el-form-item>
            <el-form-item label="端口">
              <el-input
                v-model="portText"
                placeholder="端口范围，如：80,443,8000-9000"
                @input="updatePorts"
              />
            </el-form-item>
            <el-form-item label="协议">
              <el-select v-model="ruleForm.protocol" multiple placeholder="选择协议">
                <el-option label="TCP" value="tcp" />
                <el-option label="UDP" value="udp" />
                <el-option label="HTTP" value="http" />
                <el-option label="HTTPS" value="https" />
              </el-select>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="进程" name="process">
            <el-form-item label="进程名">
              <el-input
                v-model="processNameText"
                type="textarea"
                :rows="2"
                placeholder="每行一个进程名，如：&#10;chrome.exe&#10;firefox"
                @input="updateProcessName"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>

        <!-- 出站配置 -->
        <el-divider content-position="left">出站配置</el-divider>

        <el-form-item label="出站类型" required>
          <el-select v-model="ruleForm.outbound_type" placeholder="选择出站类型" @change="onOutboundTypeChange">
            <el-option label="选择器 (Selector)" value="selector" />
            <el-option label="URL测试 (URLTest)" value="urltest" />
            <el-option label="负载均衡 (LoadBalance)" value="loadbalance" />
            <el-option label="故障转移 (Fallback)" value="fallback" />
            <el-option label="直连 (Direct)" value="direct" />
            <el-option label="阻断 (Block)" value="block" />
          </el-select>
        </el-form-item>

        <!-- 节点筛选配置 -->
        <template v-if="['selector', 'urltest', 'loadbalance', 'fallback'].includes(ruleForm.outbound_type)">
          <el-divider content-position="left">节点筛选</el-divider>

          <el-form-item label="订阅分组">
            <el-select v-model="ruleForm.group" placeholder="选择订阅分组（可选）" clearable>
              <el-option label="全部" value="" />
              <el-option
                v-for="subscription in subscriptions"
                :key="subscription.id"
                :label="subscription.name"
                :value="subscription.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="包含名称">
            <el-input
              v-model="includeNamesText"
              type="textarea"
              :rows="3"
              placeholder="每行一个名称，留空表示不限制"
              @input="updateIncludeNames"
            />
          </el-form-item>
          <el-form-item label="排除名称">
            <el-input
              v-model="excludeNamesText"
              type="textarea"
              :rows="3"
              placeholder="每行一个名称，留空表示不排除"
              @input="updateExcludeNames"
            />
          </el-form-item>

          <!-- Selector 自动切换配置 -->
          <template v-if="ruleForm.outbound_type === 'selector'">
            <el-form-item label="自动切换">
              <el-switch v-model="ruleForm.auto_switch" />
              <span style="margin-left: 8px; color: #666; font-size: 12px;">
                启用后将自动选择最快节点
              </span>
            </el-form-item>
          </template>

          <!-- URLTest 和自动切换配置 -->
          <template v-if="ruleForm.outbound_type === 'urltest' || (ruleForm.outbound_type === 'selector' && ruleForm.auto_switch)">
            <el-divider content-position="left">URL测试配置</el-divider>
            <el-form-item label="测速URL" required>
              <el-input v-model="ruleForm.urltest_url" placeholder="如 http://www.gstatic.com/generate_204" />
            </el-form-item>
            <el-form-item label="测速间隔" required>
              <el-input-number v-model="ruleForm.urltest_interval" :min="1" :step="1" style="width: 120px;" />
              <el-select v-model="ruleForm.urltest_interval_unit" style="width: 80px; margin-left: 8px;">
                <el-option label="秒" value="s" />
                <el-option label="分钟" value="m" />
              </el-select>
            </el-form-item>
            <el-form-item label="容忍延迟">
              <el-input-number v-model="ruleForm.urltest_tolerance" :min="0" :step="10" style="width: 120px;" />
              <span style="margin-left: 8px; color: #666;">毫秒</span>
            </el-form-item>
          </template>

          <!-- LoadBalance 特有配置 -->
          <template v-if="ruleForm.outbound_type === 'loadbalance'">
            <el-divider content-position="left">负载均衡配置</el-divider>
            <el-form-item label="负载均衡算法" required>
              <el-select v-model="ruleForm.loadbalance_strategy" placeholder="选择算法">
                <el-option label="轮询 (Round Robin)" value="round_robin" />
                <el-option label="最少连接 (Least Load)" value="least_load" />
                <el-option label="随机 (Random)" value="random" />
                <el-option label="一致性哈希 (Consistent Hash)" value="consistent_hash" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="ruleForm.loadbalance_strategy === 'consistent_hash'" label="哈希键">
              <el-select v-model="ruleForm.loadbalance_hash_key" placeholder="选择哈希键">
                <el-option label="源IP (Source IP)" value="source_ip" />
                <el-option label="目标地址 (Destination)" value="destination" />
                <el-option label="源端口 (Source Port)" value="source_port" />
              </el-select>
            </el-form-item>
          </template>

          <!-- Fallback 特有配置 -->
          <template v-if="ruleForm.outbound_type === 'fallback'">
            <el-divider content-position="left">故障转移配置</el-divider>
            <el-form-item label="健康检查URL">
              <el-input v-model="ruleForm.fallback_url" placeholder="如 http://www.gstatic.com/generate_204" />
            </el-form-item>
            <el-form-item label="检查间隔">
              <el-input-number v-model="ruleForm.fallback_interval_value" :min="1" :step="1" style="width: 120px;" />
              <el-select v-model="ruleForm.fallback_interval_unit" style="width: 80px; margin-left: 8px;">
                <el-option label="秒" value="s" />
                <el-option label="分钟" value="m" />
              </el-select>
            </el-form-item>
          </template>
        </template>

        <!-- Direct 和 Block 类型的说明 -->
        <template v-if="ruleForm.outbound_type === 'direct'">
          <el-alert
            title="直连配置"
            description="匹配此规则的流量将直接连接到目标地址，不经过任何代理服务器。"
            type="info"
            :closable="false"
            style="margin-bottom: 20px;"
          />
        </template>

        <template v-if="ruleForm.outbound_type === 'block'">
          <el-alert
            title="阻断配置"
            description="匹配此规则的流量将被拒绝，可用于屏蔽特定网站或服务。"
            type="warning"
            :closable="false"
            style="margin-bottom: 20px;"
          />
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false; resetForm()">取消</el-button>
          <el-button type="primary" @click="saveRule">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Plus, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { subscriptionsApi } from '@/api/subscriptions'
import { coreApi } from '@/api/core'
import type { Subscription } from '@/store/subscriptions'

// 规则接口定义
interface Rule {
  id: string
  name: string
  priority: number
  enabled: boolean

  // 匹配条件
  inbound?: string[]
  domain?: string[]
  domain_suffix?: string[]
  domain_keyword?: string[]
  domain_regex?: string[]
  geosite?: string[]
  source_geoip?: string[]
  geoip?: string[]
  ip_cidr?: string[]
  source_ip_cidr?: string[]
  port?: string[]
  source_port?: string[]
  process_name?: string[]
  process_path?: string[]
  protocol?: string[]

  // 出站配置
  outbound_type: string
  group?: string
  include_names?: string[]
  exclude_names?: string[]

  // URLTest 配置
  auto_switch?: boolean
  urltest_url?: string
  urltest_interval?: number
  urltest_interval_unit?: string
  urltest_tolerance?: number

  // LoadBalance 配置
  loadbalance_strategy?: string
  loadbalance_hash_key?: string

  // Fallback 配置
  fallback_url?: string
  fallback_interval_value?: number
  fallback_interval_unit?: string

  created_at?: string
  updated_at?: string
}

const loading = ref(false)
const showAddDialog = ref(false)
const editingRule = ref<Rule | null>(null)

const rules = ref<Rule[]>([])
const subscriptions = ref<Subscription[]>([])

const ruleForm = ref({
  name: '',
  priority: 100,
  enabled: true,

  // 匹配条件
  domain: [] as string[],
  domain_suffix: [] as string[],
  domain_keyword: [] as string[],
  geosite: [] as string[],
  geoip: [] as string[],
  ip_cidr: [] as string[],
  port: [] as string[],
  protocol: [] as string[],
  process_name: [] as string[],

  // 出站配置
  outbound_type: 'selector',
  group: '',
  include_names: [] as string[],
  exclude_names: [] as string[],

  // URLTest 配置
  auto_switch: false,
  urltest_url: 'http://www.gstatic.com/generate_204',
  urltest_interval: 60,
  urltest_interval_unit: 's',
  urltest_tolerance: 50,

  // LoadBalance 配置
  loadbalance_strategy: 'round_robin',
  loadbalance_hash_key: 'source_ip',

  // Fallback 配置
  fallback_url: 'http://www.gstatic.com/generate_204',
  fallback_interval_value: 30,
  fallback_interval_unit: 's',
})

// 文本域数据
const domainText = ref('')
const domainSuffixText = ref('')
const domainKeywordText = ref('')
const ipCidrText = ref('')
const portText = ref('')
const processNameText = ref('')
const includeNamesText = ref('')
const excludeNamesText = ref('')
const activeTab = ref('domain')

const updateIncludeNames = () => {
  ruleForm.value.include_names = includeNamesText.value.split('\n').filter(line => line.trim())
}

const updateExcludeNames = () => {
  ruleForm.value.exclude_names = excludeNamesText.value.split('\n').filter(line => line.trim())
}

// 出站类型变化处理
const onOutboundTypeChange = () => {
  // 根据出站类型设置默认值
  switch (ruleForm.value.outbound_type) {
    case 'urltest':
      if (!ruleForm.value.urltest_url) {
        ruleForm.value.urltest_url = 'http://www.gstatic.com/generate_204'
      }
      if (!ruleForm.value.urltest_interval) {
        ruleForm.value.urltest_interval = 60
      }
      break
    case 'loadbalance':
      if (!ruleForm.value.loadbalance_strategy) {
        ruleForm.value.loadbalance_strategy = 'round_robin'
      }
      break
    case 'fallback':
      if (!ruleForm.value.fallback_url) {
        ruleForm.value.fallback_url = 'http://www.gstatic.com/generate_204'
      }
      if (!ruleForm.value.fallback_interval_value) {
        ruleForm.value.fallback_interval_value = 30
      }
      break
    default:
      break
  }
}

const loadRules = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/inbounds')
    const data = await response.json()
    rules.value = data || []
  } catch (error) {
    ElMessage.error('加载规则失败')
  } finally {
    loading.value = false
  }
}

const loadSubscriptions = async () => {
  try {
    const data = await subscriptionsApi.getSubscriptions()
    subscriptions.value = data
  } catch (error) {
    console.error('加载订阅失败:', error)
  }
}

// 辅助方法
const getOutboundTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'selector': '',
    'urltest': 'success',
    'loadbalance': 'warning',
    'fallback': 'info',
    'direct': 'success',
    'block': 'danger'
  }
  return colors[type] || ''
}

const getOutboundTypeName = (type: string) => {
  const names: Record<string, string> = {
    'selector': '选择器',
    'urltest': 'URL测试',
    'loadbalance': '负载均衡',
    'fallback': '故障转移',
    'direct': '直连',
    'block': '阻断'
  }
  return names[type] || type
}

// 文本更新方法
const updateDomains = () => {
  ruleForm.value.domain = domainText.value.split('\n').filter(line => line.trim())
}

const updateDomainSuffix = () => {
  ruleForm.value.domain_suffix = domainSuffixText.value.split('\n').filter(line => line.trim())
}

const updateDomainKeyword = () => {
  ruleForm.value.domain_keyword = domainKeywordText.value.split('\n').filter(line => line.trim())
}

const updateIPCidr = () => {
  ruleForm.value.ip_cidr = ipCidrText.value.split('\n').filter(line => line.trim())
}

const updatePorts = () => {
  ruleForm.value.port = portText.value.split(',').map(p => p.trim()).filter(p => p)
}

const updateProcessName = () => {
  ruleForm.value.process_name = processNameText.value.split('\n').filter(line => line.trim())
}

const generateConfig = async () => {
  try {
    loading.value = true
    
    // 生成配置
    const config = await coreApi.generateConfig()
    console.log('生成的配置:', config)
    
    // 导出配置到文件
    const exportResult = await coreApi.exportConfig()
    console.log('导出结果:', exportResult)
    
    ElMessage.success('配置生成并导出成功')
  } catch (error) {
    console.error('配置生成失败:', error)
    ElMessage.error('配置生成失败')
  } finally {
    loading.value = false
  }
}

const editRule = (rule: Rule) => {
  editingRule.value = rule
  ruleForm.value = {
    name: rule.name,
    priority: rule.priority,
    enabled: rule.enabled,

    // 匹配条件
    domain: rule.domain || [],
    domain_suffix: rule.domain_suffix || [],
    domain_keyword: rule.domain_keyword || [],
    geosite: rule.geosite || [],
    geoip: rule.geoip || [],
    ip_cidr: rule.ip_cidr || [],
    port: rule.port || [],
    protocol: rule.protocol || [],
    process_name: rule.process_name || [],

    // 出站配置
    outbound_type: rule.outbound_type,
    group: rule.group || '',
    include_names: rule.include_names || [],
    exclude_names: rule.exclude_names || [],

    // URLTest 配置
    auto_switch: rule.auto_switch || false,
    urltest_url: rule.urltest_url || 'http://www.gstatic.com/generate_204',
    urltest_interval: rule.urltest_interval || 60,
    urltest_interval_unit: rule.urltest_interval_unit || 's',
    urltest_tolerance: rule.urltest_tolerance || 50,

    // LoadBalance 配置
    loadbalance_strategy: rule.loadbalance_strategy || 'round_robin',
    loadbalance_hash_key: rule.loadbalance_hash_key || 'source_ip',

    // Fallback 配置
    fallback_url: rule.fallback_url || 'http://www.gstatic.com/generate_204',
    fallback_interval_value: rule.fallback_interval_value || 30,
    fallback_interval_unit: rule.fallback_interval_unit || 's',
  }

  // 更新文本域
  domainText.value = (rule.domain || []).join('\n')
  domainSuffixText.value = (rule.domain_suffix || []).join('\n')
  domainKeywordText.value = (rule.domain_keyword || []).join('\n')
  ipCidrText.value = (rule.ip_cidr || []).join('\n')
  portText.value = (rule.port || []).join(',')
  processNameText.value = (rule.process_name || []).join('\n')
  includeNamesText.value = (rule.include_names || []).join('\n')
  excludeNamesText.value = (rule.exclude_names || []).join('\n')

  showAddDialog.value = true
}

const saveRule = async () => {
  try {
    const payload = { ...ruleForm.value }

    const response = editingRule.value
      ? await fetch(`/api/inbounds/${editingRule.value.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        })
      : await fetch('/api/inbounds', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        })

    if (!response.ok) {
      throw new Error('保存失败')
    }

    ElMessage.success(editingRule.value ? '更新成功' : '添加成功')
    showAddDialog.value = false
    editingRule.value = null
    resetForm()
    await loadRules()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const resetForm = () => {
  ruleForm.value = {
    name: '',
    priority: 100,
    enabled: true,

    // 匹配条件
    domain: [],
    domain_suffix: [],
    domain_keyword: [],
    geosite: [],
    geoip: [],
    ip_cidr: [],
    port: [],
    protocol: [],
    process_name: [],

    // 出站配置
    outbound_type: 'selector',
    group: '',
    include_names: [],
    exclude_names: [],

    // URLTest 配置
    auto_switch: false,
    urltest_url: 'http://www.gstatic.com/generate_204',
    urltest_interval: 60,
    urltest_interval_unit: 's',
    urltest_tolerance: 50,

    // LoadBalance 配置
    loadbalance_strategy: 'round_robin',
    loadbalance_hash_key: 'source_ip',

    // Fallback 配置
    fallback_url: 'http://www.gstatic.com/generate_204',
    fallback_interval_value: 30,
    fallback_interval_unit: 's',
  }

  // 清空文本域
  domainText.value = ''
  domainSuffixText.value = ''
  domainKeywordText.value = ''
  ipCidrText.value = ''
  portText.value = ''
  processNameText.value = ''
  includeNamesText.value = ''
  excludeNamesText.value = ''
}

const deleteRule = async (rule: Rule) => {
  try {
    await ElMessageBox.confirm('确定要删除这个规则吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(`/api/inbounds/${rule.id}`, {
      method: 'DELETE'
    })

    if (!response.ok) {
      throw new Error('删除失败')
    }

    ElMessage.success('删除成功')
    await loadRules()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const toggleRule = async (rule: Rule) => {
  try {
    const response = await fetch(`/api/inbounds/${rule.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(rule)
    })

    if (!response.ok) {
      throw new Error('更新失败')
    }

    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    rule.enabled = !rule.enabled
  }
}

onMounted(() => {
  loadRules()
  loadSubscriptions()
})
</script>

<style scoped>
.inbounds {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.rule-conditions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.rule-conditions .el-tag {
  margin-bottom: 4px;
}
</style> 