<template>
  <div class="settings">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="核心管理" name="core">
        <div class="tab-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>核心状态</span>
                <el-tag :type="coreStatus.type">{{ coreStatus.text }}</el-tag>
              </div>
            </template>
            <div class="core-info">
              <p><strong>版本:</strong> {{ coreInfo.version || '未知' }}</p>
              <p><strong>运行时间:</strong> {{ coreInfo.uptime || '0s' }}</p>
              <p><strong>内存使用:</strong> {{ coreInfo.memory || '0MB' }}</p>
              <p><strong>配置文件:</strong> {{ coreInfo.configPath || '未设置' }}</p>
            </div>
            <div class="core-actions">
              <el-button type="primary" @click="startCore" :disabled="coreStatus.running">
                启动核心
              </el-button>
              <el-button type="danger" @click="stopCore" :disabled="!coreStatus.running">
                停止核心
              </el-button>
              <el-button type="warning" @click="reloadConfig" :disabled="!coreStatus.running">
                重载配置
              </el-button>
              <el-button @click="updateCore">
                更新核心
              </el-button>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="系统设置" name="system">
        <div class="tab-content">
          <el-form :model="systemSettings" label-width="120px">
            <el-form-item label="Web UI 端口">
              <el-input-number v-model="systemSettings.webPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="日志级别">
              <el-select v-model="systemSettings.logLevel">
                <el-option label="Debug" value="debug" />
                <el-option label="Info" value="info" />
                <el-option label="Warning" value="warning" />
                <el-option label="Error" value="error" />
              </el-select>
            </el-form-item>
            <el-form-item label="自动启动">
              <el-switch v-model="systemSettings.autoStart" />
            </el-form-item>
            <el-form-item label="配置文件路径">
              <el-input v-model="systemSettings.configPath" placeholder="配置文件路径" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSystemSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="备份恢复" name="backup">
        <div class="tab-content">
          <el-card>
            <template #header>
              <span>配置备份</span>
            </template>
            <div class="backup-actions">
              <el-button type="primary" @click="exportConfig">
                导出配置
              </el-button>
              <el-button @click="importConfig">
                导入配置
              </el-button>
              <el-button type="warning" @click="resetConfig">
                重置配置
              </el-button>
            </div>
          </el-card>

          <el-card style="margin-top: 20px;">
            <template #header>
              <span>备份历史</span>
            </template>
            <el-table :data="backupHistory" style="width: 100%">
              <el-table-column prop="name" label="备份名称" />
              <el-table-column prop="date" label="备份时间" />
              <el-table-column prop="size" label="文件大小" />
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button size="small" @click="restoreBackup(row)">恢复</el-button>
                  <el-button size="small" type="danger" @click="deleteBackup(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCoreStore } from '@/store/core'
import { useSettingsStore } from '@/store/settings'

const coreStore = useCoreStore()
const settingsStore = useSettingsStore()

const activeTab = ref('core')

const coreStatus = ref({
  running: false,
  type: 'danger',
  text: '未运行'
})

const coreInfo = ref({
  version: '',
  uptime: '',
  memory: '',
  configPath: ''
})

const systemSettings = ref({
  webPort: 8080,
  logLevel: 'info',
  autoStart: false,
  configPath: ''
})

const backupHistory = ref([
  { name: 'backup_2024_01_01.json', date: '2024-01-01 12:00:00', size: '1.2KB' },
  { name: 'backup_2023_12_31.json', date: '2023-12-31 12:00:00', size: '1.1KB' }
])

const startCore = async () => {
  try {
    await coreStore.startCore()
    coreStatus.value = { running: true, type: 'success', text: '运行中' }
    ElMessage.success('核心启动成功')
  } catch (error) {
    ElMessage.error('启动失败')
  }
}

const stopCore = async () => {
  try {
    await coreStore.stopCore()
    coreStatus.value = { running: false, type: 'danger', text: '已停止' }
    ElMessage.success('核心已停止')
  } catch (error) {
    ElMessage.error('停止失败')
  }
}

const reloadConfig = async () => {
  try {
    await coreStore.reloadConfig()
    ElMessage.success('配置重载成功')
  } catch (error) {
    ElMessage.error('重载失败')
  }
}

const updateCore = async () => {
  try {
    await coreStore.updateCore()
    ElMessage.success('核心更新成功')
  } catch (error) {
    ElMessage.error('更新失败')
  }
}

const saveSystemSettings = async () => {
  try {
    await settingsStore.saveSettings(systemSettings.value)
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const exportConfig = async () => {
  try {
    await settingsStore.exportConfig()
    ElMessage.success('配置导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const importConfig = async () => {
  try {
    await settingsStore.importConfig()
    ElMessage.success('配置导入成功')
  } catch (error) {
    ElMessage.error('导入失败')
  }
}

const resetConfig = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有配置吗？此操作不可恢复！', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await settingsStore.resetConfig()
    ElMessage.success('配置重置成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败')
    }
  }
}

const restoreBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(`确定要恢复备份 ${backup.name} 吗？`, '确认恢复', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await settingsStore.restoreBackup(backup.name)
    ElMessage.success('备份恢复成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('恢复失败')
    }
  }
}

const deleteBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除备份 ${backup.name} 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await settingsStore.deleteBackup(backup.name)
    ElMessage.success('备份删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

onMounted(async () => {
  await coreStore.getCoreStatus()
  await settingsStore.loadSettings()
})
</script>

<style scoped>
.settings {
  padding: 20px;
}

.tab-content {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.core-info p {
  margin: 8px 0;
}

.core-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.backup-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style> 