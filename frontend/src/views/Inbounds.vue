<template>
  <div class="inbounds-container">
    <div class="header">
      <h2>规则管理</h2>
      <div class="header-buttons">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加规则
        </el-button>
        <el-button type="success" @click="generateConfig">
          <el-icon><Setting /></el-icon>
          配置生成
        </el-button>
      </div>
    </div>

    <el-table :data="inbounds" style="width: 100%" v-loading="loading">
      <el-table-column prop="name" label="名称" width="150" />
      <el-table-column prop="type" label="类型" width="100">
        <template #default="{ row }">
          <el-tag>{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="port" label="端口" width="80" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="group" label="分组" width="120" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editInbound(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteInbound(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingInbound ? '编辑规则' : '添加规则'"
      width="700px"
    >
      <el-form :model="inboundForm" label-width="100px">
        <el-form-item label="名称" required>
          <el-input v-model="inboundForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="类型" required>
          <el-select v-model="inboundForm.type" placeholder="选择类型">
            <el-option label="HTTP" value="http" />
            <el-option label="SOCKS" value="socks" />
            <el-option label="Shadowsocks" value="shadowsocks" />
            <el-option label="VMess" value="vmess" />
            <el-option label="Trojan" value="trojan" />
          </el-select>
        </el-form-item>
        <el-form-item label="端口" required>
          <el-input-number v-model="inboundForm.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="inboundForm.username" placeholder="请输入用户名（可选）" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="inboundForm.password" type="password" placeholder="请输入密码（可选）" />
        </el-form-item>
        <el-form-item label="配置方式" required>
          <el-select v-model="inboundForm.config_type" placeholder="选择配置方式" @change="onConfigTypeChange">
            <el-option label="选择器 (Selector)" value="selector" />
            <el-option label="URL测试 (URLTest)" value="urltest" />
            <el-option label="负载均衡 (LoadBalance)" value="loadbalance" />
            <el-option label="故障转移 (Fallback)" value="fallback" />
            <el-option label="直连 (Direct)" value="direct" />
            <el-option label="阻断 (Block)" value="block" />
          </el-select>
        </el-form-item>

        <!-- Selector 配置 -->
        <template v-if="inboundForm.config_type === 'selector'">
          <el-form-item label="分组">
            <el-select v-model="inboundForm.group" placeholder="选择订阅分组（可选）" clearable>
              <el-option label="全部" value="" />
              <el-option
                v-for="subscription in subscriptions"
                :key="subscription.id"
                :label="subscription.name"
                :value="subscription.name"
              />
            </el-select>
          </el-form-item>

          <!-- Selector 中的自动切换配置 -->
          <el-form-item label="自动切换">
            <el-switch v-model="inboundForm.auto_switch" />
            <span style="margin-left: 8px; color: #666; font-size: 12px;">
              启用后将自动选择最快节点
            </span>
          </el-form-item>

          <template v-if="inboundForm.auto_switch">
            <el-form-item label="测速URL" required>
              <el-input v-model="inboundForm.urltest_url" placeholder="如 http://www.gstatic.com/generate_204" />
            </el-form-item>
            <el-form-item label="测速间隔" required>
              <el-input-number v-model="inboundForm.urltest_interval_value" :min="1" :step="1" style="width: 120px;" />
              <el-select v-model="inboundForm.urltest_interval_unit" style="width: 80px; margin-left: 8px;">
                <el-option label="秒" value="s" />
                <el-option label="分钟" value="m" />
              </el-select>
            </el-form-item>
            <el-form-item label="容忍延迟">
              <el-input-number v-model="inboundForm.urltest_tolerance" :min="0" :step="10" style="width: 120px;" />
              <span style="margin-left: 8px; color: #666;">毫秒</span>
            </el-form-item>
          </template>
        </template>
        <!-- Selector 和 URLTest 共用的节点筛选配置 -->
        <template v-if="['selector', 'urltest', 'loadbalance', 'fallback'].includes(inboundForm.config_type)">
          <el-form-item label="包含名称">
            <el-input
              v-model="includeNamesText"
              type="textarea"
              :rows="3"
              placeholder="每行一个名称，留空表示不限制"
              @input="updateIncludeNames"
            />
          </el-form-item>
          <el-form-item label="排除名称">
            <el-input
              v-model="excludeNamesText"
              type="textarea"
              :rows="3"
              placeholder="每行一个名称，留空表示不排除"
              @input="updateExcludeNames"
            />
          </el-form-item>
        </template>

        <!-- URLTest 特有配置 -->
        <template v-if="inboundForm.config_type === 'urltest'">
          <el-form-item label="测速URL" required>
            <el-input v-model="inboundForm.urltest_url" placeholder="如 http://www.gstatic.com/generate_204" />
          </el-form-item>
          <el-form-item label="测速间隔" required>
            <el-input-number v-model="inboundForm.urltest_interval_value" :min="1" :step="1" style="width: 120px;" />
            <el-select v-model="inboundForm.urltest_interval_unit" style="width: 80px; margin-left: 8px;">
              <el-option label="秒" value="s" />
              <el-option label="分钟" value="m" />
            </el-select>
          </el-form-item>
          <el-form-item label="容忍延迟">
            <el-input-number v-model="inboundForm.urltest_tolerance" :min="0" :step="10" style="width: 120px;" />
            <span style="margin-left: 8px; color: #666;">毫秒</span>
          </el-form-item>
        </template>

        <!-- LoadBalance 特有配置 -->
        <template v-if="inboundForm.config_type === 'loadbalance'">
          <el-form-item label="负载均衡算法" required>
            <el-select v-model="inboundForm.loadbalance_strategy" placeholder="选择算法">
              <el-option label="轮询 (Round Robin)" value="round_robin" />
              <el-option label="最少连接 (Least Load)" value="least_load" />
              <el-option label="随机 (Random)" value="random" />
              <el-option label="一致性哈希 (Consistent Hash)" value="consistent_hash" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="inboundForm.loadbalance_strategy === 'consistent_hash'" label="哈希键">
            <el-select v-model="inboundForm.loadbalance_hash_key" placeholder="选择哈希键">
              <el-option label="源IP (Source IP)" value="source_ip" />
              <el-option label="目标地址 (Destination)" value="destination" />
              <el-option label="源端口 (Source Port)" value="source_port" />
            </el-select>
          </el-form-item>
        </template>

        <!-- Fallback 特有配置 -->
        <template v-if="inboundForm.config_type === 'fallback'">
          <el-form-item label="健康检查URL">
            <el-input v-model="inboundForm.fallback_url" placeholder="如 http://www.gstatic.com/generate_204" />
          </el-form-item>
          <el-form-item label="检查间隔">
            <el-input-number v-model="inboundForm.fallback_interval_value" :min="1" :step="1" style="width: 120px;" />
            <el-select v-model="inboundForm.fallback_interval_unit" style="width: 80px; margin-left: 8px;">
              <el-option label="秒" value="s" />
              <el-option label="分钟" value="m" />
            </el-select>
          </el-form-item>
        </template>

        <!-- Direct 和 Block 类型的说明 -->
        <template v-if="inboundForm.config_type === 'direct'">
          <el-alert
            title="直连配置"
            description="此入站将直接连接到目标地址，不经过任何代理服务器。"
            type="info"
            :closable="false"
            style="margin-bottom: 20px;"
          />
        </template>

        <template v-if="inboundForm.config_type === 'block'">
          <el-alert
            title="阻断配置"
            description="此入站将拒绝所有连接请求，可用于屏蔽特定流量。"
            type="warning"
            :closable="false"
            style="margin-bottom: 20px;"
          />
        </template>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveInbound">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Plus, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useInboundsStore } from '@/store/inbounds'
import { subscriptionsApi } from '@/api/subscriptions'
import { coreApi } from '@/api/core'
import type { Inbound } from '@/store/inbounds'
import type { Subscription } from '@/store/subscriptions'

const inboundsStore = useInboundsStore()

const loading = ref(false)
const showAddDialog = ref(false)
const editingInbound = ref<Inbound | null>(null)

const inbounds = ref<Inbound[]>([])
const subscriptions = ref<Subscription[]>([])

// 表单数据
const inboundForm = ref({
  name: '',
  type: 'http',
  port: 1080,
  username: '',
  password: '',
  config_type: 'selector', // 新增：配置方式
  group: '',
  include_names: [] as string[],
  exclude_names: [] as string[],
  // Selector 中的自动切换配置
  auto_switch: false,
  // URLTest 配置
  urltest_url: 'http://www.gstatic.com/generate_204',
  urltest_interval_value: 60,
  urltest_interval_unit: 's',
  urltest_tolerance: 50,
  // LoadBalance 配置
  loadbalance_strategy: 'round_robin',
  loadbalance_hash_key: 'source_ip',
  // Fallback 配置
  fallback_url: 'http://www.gstatic.com/generate_204',
  fallback_interval_value: 30,
  fallback_interval_unit: 's',
})

const includeNamesText = ref('')
const excludeNamesText = ref('')

// 更新包含名称
const updateIncludeNames = () => {
  if (includeNamesText.value.trim()) {
    inboundForm.value.include_names = includeNamesText.value
      .split('\n')
      .map(name => name.trim())
      .filter(name => name.length > 0)
  } else {
    inboundForm.value.include_names = []
  }
}

// 更新排除名称
const updateExcludeNames = () => {
  if (excludeNamesText.value.trim()) {
    inboundForm.value.exclude_names = excludeNamesText.value
      .split('\n')
      .map(name => name.trim())
      .filter(name => name.length > 0)
  } else {
    inboundForm.value.exclude_names = []
  }
}

// 配置类型变化处理
const onConfigTypeChange = (configType: string) => {
  // 根据配置类型设置默认值
  switch (configType) {
    case 'selector':
      // Selector 类型保持现有逻辑
      break
    case 'urltest':
      // URLTest 类型设置默认测速配置
      if (!inboundForm.value.urltest_url) {
        inboundForm.value.urltest_url = 'http://www.gstatic.com/generate_204'
      }
      if (!inboundForm.value.urltest_interval_value) {
        inboundForm.value.urltest_interval_value = 60
      }
      break
    case 'loadbalance':
      // LoadBalance 类型设置默认算法
      if (!inboundForm.value.loadbalance_strategy) {
        inboundForm.value.loadbalance_strategy = 'round_robin'
      }
      break
    case 'fallback':
      // Fallback 类型设置默认健康检查
      if (!inboundForm.value.fallback_url) {
        inboundForm.value.fallback_url = 'http://www.gstatic.com/generate_204'
      }
      if (!inboundForm.value.fallback_interval_value) {
        inboundForm.value.fallback_interval_value = 30
      }
      break
    case 'direct':
    case 'block':
      // Direct 和 Block 类型不需要额外配置
      break
  }
}

const loadInbounds = async () => {
  loading.value = true
  try {
    const data = await inboundsStore.getInbounds()
    inbounds.value = data
  } catch (error) {
    ElMessage.error('加载入站配置失败')
  } finally {
    loading.value = false
  }
}

const loadSubscriptions = async () => {
  try {
    const data = await subscriptionsApi.getSubscriptions()
    subscriptions.value = data
  } catch (error) {
    console.error('加载订阅失败:', error)
  }
}

const generateConfig = async () => {
  try {
    await coreApi.generateConfig()
    ElMessage.success('配置生成成功')
  } catch (error) {
    ElMessage.error('配置生成失败')
  }
}

const editInbound = (inbound: Inbound) => {
  editingInbound.value = inbound
  inboundForm.value = {
    name: inbound.name,
    type: inbound.type,
    port: inbound.port,
    username: inbound.username || '',
    password: inbound.password || '',
    config_type: inbound.config_type || 'selector', // 新增配置方式字段
    group: inbound.group || '',
    include_names: inbound.include_names || [],
    exclude_names: inbound.exclude_names || [],
    // Selector 中的自动切换配置
    auto_switch: inbound.auto_switch || false,
    // URLTest 配置
    urltest_url: inbound.urltest_url || 'http://www.gstatic.com/generate_204',
    urltest_interval_value: inbound.urltest_interval || 60,
    urltest_interval_unit: inbound.urltest_interval_unit || 's',
    urltest_tolerance: inbound.urltest_tolerance || 50,
    // LoadBalance 配置
    loadbalance_strategy: inbound.loadbalance_strategy || 'round_robin',
    loadbalance_hash_key: inbound.loadbalance_hash_key || 'source_ip',
    // Fallback 配置
    fallback_url: inbound.fallback_url || 'http://www.gstatic.com/generate_204',
    fallback_interval_value: inbound.fallback_interval_value || 30,
    fallback_interval_unit: inbound.fallback_interval_unit || 's',
  }

  // 更新文本域
  includeNamesText.value = (inbound.include_names || []).join('\n')
  excludeNamesText.value = (inbound.exclude_names || []).join('\n')

  showAddDialog.value = true
}

const saveInbound = async () => {
  try {
    // 修正：将 urltest_interval_value 映射为 urltest_interval，urltest_interval_unit 强制类型为 's' | 'm'
    const { urltest_interval_value, urltest_interval_unit, ...rest } = inboundForm.value
    const payload = {
      ...rest,
      urltest_interval: urltest_interval_value,
      urltest_interval_unit: urltest_interval_unit as 's' | 'm',
    }
    if (editingInbound.value) {
      await inboundsStore.updateInbound(editingInbound.value.id, payload)
      ElMessage.success('更新成功')
    } else {
      await inboundsStore.addInbound(payload)
      ElMessage.success('添加成功')
    }
    showAddDialog.value = false
    editingInbound.value = null
    resetForm()
    await loadInbounds()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const resetForm = () => {
    inboundForm.value = {
      name: '',
      type: 'http',
      port: 1080,
      username: '',
      password: '',
      config_type: 'selector', // 默认配置方式
      group: '',
      include_names: [],
      exclude_names: [],
      // Selector 中的自动切换配置
      auto_switch: false,
      // URLTest 配置
      urltest_url: 'http://www.gstatic.com/generate_204',
      urltest_interval_value: 60,
      urltest_interval_unit: 's',
      urltest_tolerance: 50,
      // LoadBalance 配置
      loadbalance_strategy: 'round_robin',
      loadbalance_hash_key: 'source_ip',
      // Fallback 配置
      fallback_url: 'http://www.gstatic.com/generate_204',
      fallback_interval_value: 30,
      fallback_interval_unit: 's',
    }
    includeNamesText.value = ''
    excludeNamesText.value = ''
}

const deleteInbound = async (inbound: Inbound) => {
  try {
    await ElMessageBox.confirm('确定要删除这个入站配置吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await inboundsStore.deleteInbound(inbound.id)
    ElMessage.success('删除成功')
    await loadInbounds()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  loadInbounds()
  loadSubscriptions()
})
</script>

<style scoped>
.inbounds-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>