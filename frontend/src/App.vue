<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="app-aside">
        <Sidebar :is-collapse="isCollapse" />
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="app-header-container">
          <Header :is-collapse="isCollapse" @toggle-sidebar="toggleSidebar" />
        </el-header>

        <!-- 内容区 -->
        <el-main class="app-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Sidebar from '@/components/Sidebar.vue'
import Header from '@/components/Header.vue'

const isCollapse = ref(false)

const sidebarWidth = computed(() => {
  return isCollapse.value ? '64px' : '200px'
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style>
#app {
  height: 100vh;
}

.app-container {
  height: 100%;
}

.app-aside {
  background-color: #304156;
  transition: width 0.3s;
}

.app-header-container {
  padding: 0;
  height: 60px;
}

.app-main {
  background-color: #f0f2f5;
  padding: 0;
}
</style> 