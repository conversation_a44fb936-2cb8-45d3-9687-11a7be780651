import{u as J}from"./core-CZJpwy5H.js";import{d as O,r as b,a as Q,o as R,e as X,f as t,w as o,g as u,h as r,i,t as k,E as l,z as T,l as Y,_ as Z}from"./index-DsfgYd3f.js";import{r as y}from"./request-kx1og6yA.js";import"./core-DwEzVlk6.js";const v={getSettings(){return y.get("/api/settings")},saveSettings(d){return y.put("/api/settings",d)},exportConfig(){return y.post("/api/settings/export")},importConfig(){return y.post("/api/settings/import")},resetConfig(){return y.post("/api/settings/reset")},restoreBackup(d){return y.post(`/api/settings/backup/${d}/restore`)},deleteBackup(d){return y.delete(`/api/settings/backup/${d}`)}},ee=O("settings",()=>{const d=b({webPort:8080,logLevel:"info",autoStart:!1,configPath:""}),n=b(!1);return{settings:d,loading:n,loadSettings:async()=>{try{n.value=!0;const a=await v.getSettings();d.value=a}catch(a){throw console.error("加载设置失败:",a),a}finally{n.value=!1}},saveSettings:async a=>{try{n.value=!0,await v.saveSettings(a),d.value=a}catch(f){throw console.error("保存设置失败:",f),f}finally{n.value=!1}},exportConfig:async()=>{try{n.value=!0,await v.exportConfig()}catch(a){throw console.error("导出配置失败:",a),a}finally{n.value=!1}},importConfig:async()=>{try{n.value=!0,await v.importConfig()}catch(a){throw console.error("导入配置失败:",a),a}finally{n.value=!1}},resetConfig:async()=>{try{n.value=!0,await v.resetConfig()}catch(a){throw console.error("重置配置失败:",a),a}finally{n.value=!1}},restoreBackup:async a=>{try{n.value=!0,await v.restoreBackup(a)}catch(f){throw console.error("恢复备份失败:",f),f}finally{n.value=!1}},deleteBackup:async a=>{try{n.value=!0,await v.deleteBackup(a)}catch(f){throw console.error("删除备份失败:",f),f}finally{n.value=!1}}}}),te={class:"settings"},oe={class:"tab-content"},ae={class:"card-header"},ne={class:"core-info"},le={class:"core-actions"},re={class:"tab-content"},se={class:"tab-content"},ie={class:"backup-actions"},ue=Q({__name:"Settings",setup(d){const n=J(),g=ee(),B=b("core"),_=b({running:!1,type:"danger",text:"未运行"}),w=b({version:"",uptime:"",memory:"",configPath:""}),p=b({webPort:8080,logLevel:"info",autoStart:!1,configPath:""}),h=b([{name:"backup_2024_01_01.json",date:"2024-01-01 12:00:00",size:"1.2KB"},{name:"backup_2023_12_31.json",date:"2023-12-31 12:00:00",size:"1.1KB"}]),z=async()=>{try{await n.startCore(),_.value={running:!0,type:"success",text:"运行中"},l.success("核心启动成功")}catch{l.error("启动失败")}},a=async()=>{try{await n.stopCore(),_.value={running:!1,type:"danger",text:"已停止"},l.success("核心已停止")}catch{l.error("停止失败")}},f=async()=>{try{await n.reloadConfig(),l.success("配置重载成功")}catch{l.error("重载失败")}},U=async()=>{try{await n.updateCore(),l.success("核心更新成功")}catch{l.error("更新失败")}},$=async()=>{try{await g.saveSettings(p.value),l.success("设置保存成功")}catch{l.error("保存失败")}},E=async()=>{try{await g.exportConfig(),l.success("配置导出成功")}catch{l.error("导出失败")}},I=async()=>{try{await g.importConfig(),l.success("配置导入成功")}catch{l.error("导入失败")}},L=async()=>{try{await T.confirm("确定要重置所有配置吗？此操作不可恢复！","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await g.resetConfig(),l.success("配置重置成功")}catch(s){s!=="cancel"&&l.error("重置失败")}},M=async s=>{try{await T.confirm(`确定要恢复备份 ${s.name} 吗？`,"确认恢复",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await g.restoreBackup(s.name),l.success("备份恢复成功")}catch(e){e!=="cancel"&&l.error("恢复失败")}},N=async s=>{try{await T.confirm(`确定要删除备份 ${s.name} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await g.deleteBackup(s.name),l.success("备份删除成功")}catch(e){e!=="cancel"&&l.error("删除失败")}};return R(async()=>{await n.getCoreStatus(),await g.loadSettings()}),(s,e)=>{const j=u("el-tag"),m=u("el-button"),V=u("el-card"),P=u("el-tab-pane"),D=u("el-input-number"),C=u("el-form-item"),S=u("el-option"),K=u("el-select"),W=u("el-switch"),q=u("el-input"),A=u("el-form"),x=u("el-table-column"),H=u("el-table"),F=u("el-tabs");return Y(),X("div",te,[t(F,{modelValue:B.value,"onUpdate:modelValue":e[4]||(e[4]=c=>B.value=c),type:"border-card"},{default:o(()=>[t(P,{label:"核心管理",name:"core"},{default:o(()=>[r("div",oe,[t(V,null,{header:o(()=>[r("div",ae,[e[5]||(e[5]=r("span",null,"核心状态",-1)),t(j,{type:_.value.type},{default:o(()=>[i(k(_.value.text),1)]),_:1},8,["type"])])]),default:o(()=>[r("div",ne,[r("p",null,[e[6]||(e[6]=r("strong",null,"版本:",-1)),i(" "+k(w.value.version||"未知"),1)]),r("p",null,[e[7]||(e[7]=r("strong",null,"运行时间:",-1)),i(" "+k(w.value.uptime||"0s"),1)]),r("p",null,[e[8]||(e[8]=r("strong",null,"内存使用:",-1)),i(" "+k(w.value.memory||"0MB"),1)]),r("p",null,[e[9]||(e[9]=r("strong",null,"配置文件:",-1)),i(" "+k(w.value.configPath||"未设置"),1)])]),r("div",le,[t(m,{type:"primary",onClick:z,disabled:_.value.running},{default:o(()=>e[10]||(e[10]=[i(" 启动核心 ")])),_:1,__:[10]},8,["disabled"]),t(m,{type:"danger",onClick:a,disabled:!_.value.running},{default:o(()=>e[11]||(e[11]=[i(" 停止核心 ")])),_:1,__:[11]},8,["disabled"]),t(m,{type:"warning",onClick:f,disabled:!_.value.running},{default:o(()=>e[12]||(e[12]=[i(" 重载配置 ")])),_:1,__:[12]},8,["disabled"]),t(m,{onClick:U},{default:o(()=>e[13]||(e[13]=[i(" 更新核心 ")])),_:1,__:[13]})])]),_:1})])]),_:1}),t(P,{label:"系统设置",name:"system"},{default:o(()=>[r("div",re,[t(A,{model:p.value,"label-width":"120px"},{default:o(()=>[t(C,{label:"Web UI 端口"},{default:o(()=>[t(D,{modelValue:p.value.webPort,"onUpdate:modelValue":e[0]||(e[0]=c=>p.value.webPort=c),min:1,max:65535},null,8,["modelValue"])]),_:1}),t(C,{label:"日志级别"},{default:o(()=>[t(K,{modelValue:p.value.logLevel,"onUpdate:modelValue":e[1]||(e[1]=c=>p.value.logLevel=c)},{default:o(()=>[t(S,{label:"Debug",value:"debug"}),t(S,{label:"Info",value:"info"}),t(S,{label:"Warning",value:"warning"}),t(S,{label:"Error",value:"error"})]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"自动启动"},{default:o(()=>[t(W,{modelValue:p.value.autoStart,"onUpdate:modelValue":e[2]||(e[2]=c=>p.value.autoStart=c)},null,8,["modelValue"])]),_:1}),t(C,{label:"配置文件路径"},{default:o(()=>[t(q,{modelValue:p.value.configPath,"onUpdate:modelValue":e[3]||(e[3]=c=>p.value.configPath=c),placeholder:"配置文件路径"},null,8,["modelValue"])]),_:1}),t(C,null,{default:o(()=>[t(m,{type:"primary",onClick:$},{default:o(()=>e[14]||(e[14]=[i("保存设置")])),_:1,__:[14]})]),_:1})]),_:1},8,["model"])])]),_:1}),t(P,{label:"备份恢复",name:"backup"},{default:o(()=>[r("div",se,[t(V,null,{header:o(()=>e[15]||(e[15]=[r("span",null,"配置备份",-1)])),default:o(()=>[r("div",ie,[t(m,{type:"primary",onClick:E},{default:o(()=>e[16]||(e[16]=[i(" 导出配置 ")])),_:1,__:[16]}),t(m,{onClick:I},{default:o(()=>e[17]||(e[17]=[i(" 导入配置 ")])),_:1,__:[17]}),t(m,{type:"warning",onClick:L},{default:o(()=>e[18]||(e[18]=[i(" 重置配置 ")])),_:1,__:[18]})])]),_:1}),t(V,{style:{"margin-top":"20px"}},{header:o(()=>e[19]||(e[19]=[r("span",null,"备份历史",-1)])),default:o(()=>[t(H,{data:h.value,style:{width:"100%"}},{default:o(()=>[t(x,{prop:"name",label:"备份名称"}),t(x,{prop:"date",label:"备份时间"}),t(x,{prop:"size",label:"文件大小"}),t(x,{label:"操作",width:"200"},{default:o(({row:c})=>[t(m,{size:"small",onClick:G=>M(c)},{default:o(()=>e[20]||(e[20]=[i("恢复")])),_:2,__:[20]},1032,["onClick"]),t(m,{size:"small",type:"danger",onClick:G=>N(c)},{default:o(()=>e[21]||(e[21]=[i("删除")])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1})]),_:1},8,["modelValue"])])}}}),me=Z(ue,[["__scopeId","data-v-64b77c49"]]);export{me as default};
