#!/bin/bash

# 设置错误时退出
set -e

echo "🚀 Building SingBox UI (Frontend + Backend)..."

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Error: Go is not installed. Please install Go first."
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed. Please install Node.js first."
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ Error: npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Building Frontend..."

# 进入前端目录
cd frontend

# 安装前端依赖
echo "Installing frontend dependencies..."
npm install

# 构建前端
echo "Building frontend..."
if npm run build; then
    echo "✅ Frontend build completed successfully!"
else
    echo "⚠️  Type checking failed, trying build without type check..."
    
    # 尝试直接使用 vite build
    if npx vite build; then
        echo "✅ Frontend build completed (without type checking)!"
    else
        echo "❌ Frontend build failed!"
        exit 1
    fi
fi

# 拷贝前端构建产物到 web/dist
if [ -d "web/dist" ]; then
    rm -rf web/dist
fi
mkdir -p web/dist
cp -r frontend/dist/* web/dist/
echo "✅ Frontend files copied to web/dist"

# 返回根目录
cd ..

echo "✅ Frontend build completed!"

echo "🔧 Building Backend..."

# 下载Go依赖
echo "Downloading Go dependencies..."
go mod tidy

# 构建后端程序
echo "Building backend application..."
go build -ldflags="-s -w" -o singboxui main.go

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo ""
    echo "📁 Generated files:"
    echo "  - Backend executable: ./singboxui"
    echo "  - Frontend static files: ./frontend/dist/"
    echo ""
    echo "🚀 To run the application:"
    echo "  ./singboxui"
    echo ""
    echo "🌐 The frontend will be served from the backend automatically."
    echo "   Access the web interface at: http://localhost:8080"
else
    echo "❌ Build failed!"
    exit 1
fi 