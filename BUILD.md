# SingBox UI 构建指南

本项目支持多种构建方式，包括前后端一起编译。

## 🚀 快速开始

### 方法一：使用 Makefile（推荐）

```bash
# 显示所有可用命令
make help

# 构建前后端
make build

# 仅构建前端
make build-frontend

# 仅构建后端
make build-backend

# 生产环境构建
make build-prod

# 清理构建文件
make clean

# 开发模式运行
make dev
```

### 方法二：使用构建脚本

```bash
# 基础构建脚本
./build.sh

# 高级构建脚本（支持更多选项）
./build-all.sh --help

# 示例用法
./build-all.sh                    # 构建前后端
./build-all.sh -f                 # 仅构建前端
./build-all.sh -b                 # 仅构建后端
./build-all.sh -c -p              # 清理并生产构建
./build-all.sh --skip-tests       # 跳过测试
```

### 方法三：手动构建

```bash
# 1. 构建前端
cd frontend
npm install
npm run build
cd ..

# 2. 构建后端
go mod tidy
go build -ldflags="-s -w" -o singboxui main.go
```

## 📋 构建选项

### Makefile 命令

| 命令 | 描述 |
|------|------|
| `make help` | 显示帮助信息 |
| `make build` | 构建前后端 |
| `make build-frontend` | 仅构建前端 |
| `make build-backend` | 仅构建后端 |
| `make build-prod` | 生产环境构建 |
| `make clean` | 清理构建文件 |
| `make test` | 运行测试 |
| `make dev` | 开发模式运行 |
| `make install-deps` | 安装依赖 |
| `make quick-build` | 快速构建（跳过依赖安装） |
| `make check-deps` | 检查依赖 |
| `make version` | 显示版本信息 |
| `make format` | 格式化代码 |
| `make lint` | 代码检查 |
| `make all` | 完整构建流程 |

### 构建脚本选项

| 选项 | 描述 |
|------|------|
| `-f, --frontend-only` | 仅构建前端 |
| `-b, --backend-only` | 仅构建后端 |
| `-c, --clean` | 清理构建 |
| `-p, --production` | 生产模式 |
| `-s, --skip-tests` | 跳过测试 |
| `-h, --help` | 显示帮助 |

## 🔧 环境要求

### 必需依赖

- **Go**: 1.21 或更高版本
- **Node.js**: 18 或更高版本
- **npm**: 9 或更高版本

### 检查依赖

```bash
# 使用 Makefile
make check-deps

# 使用构建脚本
./build-all.sh --help
```

## 📁 构建输出

构建完成后，会生成以下文件：

```
singboxui/
├── singboxui          # 后端可执行文件
└── frontend/
    └── dist/          # 前端静态文件
        ├── index.html
        ├── assets/
        └── ...
```

## 🚀 运行应用

构建完成后，运行应用：

```bash
# 运行后端（会自动提供前端静态文件）
./singboxui

# 访问 Web 界面
# http://localhost:8080
```

## 🐳 Docker 支持

如果需要 Docker 支持，可以创建 Dockerfile：

```dockerfile
# 多阶段构建
FROM node:18-alpine AS frontend-builder
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ ./
RUN npm run build

FROM golang:1.21-alpine AS backend-builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -ldflags="-s -w" -o singboxui main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist
COPY --from=backend-builder /app/singboxui .
EXPOSE 8080
CMD ["./singboxui"]
```

## 🔍 故障排除

### 常见问题

1. **Go 未安装**
   ```bash
   # macOS
   brew install go
   
   # Ubuntu/Debian
   sudo apt-get install golang-go
   ```

2. **Node.js 未安装**
   ```bash
   # macOS
   brew install node
   
   # Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. **权限问题**
   ```bash
   chmod +x build.sh build-all.sh
   ```

4. **依赖问题**
   ```bash
   # 清理并重新安装
   make clean
   make install-deps
   make build
   ```

### 调试模式

```bash
# 启用详细输出
make build VERBOSE=1

# 或使用构建脚本
./build-all.sh --verbose
```

## 📝 开发工作流

### 日常开发

```bash
# 1. 启动开发模式
make dev

# 2. 在另一个终端进行开发
# 前端: http://localhost:5173
# 后端: http://localhost:8080
```

### 提交前检查

```bash
# 1. 格式化代码
make format

# 2. 代码检查
make lint

# 3. 运行测试
make test

# 4. 构建验证
make build
```

## 🎯 生产部署

### 推荐的生产构建

```bash
# 完整生产构建
make build-prod

# 或使用构建脚本
./build-all.sh -c -p
```

### 部署检查清单

- [ ] 运行 `make build-prod`
- [ ] 测试 `./singboxui` 启动
- [ ] 验证 Web 界面访问
- [ ] 检查日志输出
- [ ] 配置反向代理（如需要）
- [ ] 设置防火墙规则
- [ ] 配置 SSL 证书

---

更多信息请参考项目 README.md 文件。 