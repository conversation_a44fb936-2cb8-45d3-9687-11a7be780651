package storage

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"singboxui/internal/models"

	_ "github.com/mattn/go-sqlite3"
)

type SQLiteStorage struct {
	db *sql.DB
}

func NewSQLiteStorage(dbPath string) (*SQLiteStorage, error) {
	// 设置数据库连接参数，包括超时
	db, err := sql.Open("sqlite3", dbPath+"?_timeout=5000&_journal_mode=WAL&_synchronous=NORMAL")
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(1) // SQLite 只支持一个写连接
	db.SetMaxIdleConns(1)
	db.SetConnMaxLifetime(time.Hour)

	storage := &SQLiteStorage{db: db}
	if err := storage.initTables(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to initialize tables: %w", err)
	}

	return storage, nil
}

func (s *SQLiteStorage) Close() error {
	return s.db.Close()
}

func (s *SQLiteStorage) initTables() error {
	// 创建节点表
	createNodesTable := `
	CREATE TABLE IF NOT EXISTS nodes (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		type TEXT NOT NULL,
		address TEXT NOT NULL,
		port INTEGER NOT NULL,
		uuid TEXT,
		password TEXT,
		security TEXT,
		network TEXT,
		outbound_id TEXT,
		group_name TEXT,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL,
		tls_enabled BOOLEAN,
		tls_server_name TEXT,
		tls_insecure BOOLEAN,
		transport_type TEXT,
		grpc_service_name TEXT,
		grpc_idle_timeout INTEGER,
		grpc_ping_timeout INTEGER,
		grpc_permit_without_stream BOOLEAN,
		path TEXT,
		obfs_param TEXT,
		peer TEXT
	);`

	if _, err := s.db.Exec(createNodesTable); err != nil {
		return fmt.Errorf("failed to create nodes table: %w", err)
	}

	// 创建订阅表
	createSubscriptionsTable := `
	CREATE TABLE IF NOT EXISTS subscriptions (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		url TEXT NOT NULL,
		enabled BOOLEAN DEFAULT 1,
		node_count INTEGER DEFAULT 0,
		last_update TEXT,
		update_interval INTEGER DEFAULT 0,
		description TEXT,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL
	);`

	if _, err := s.db.Exec(createSubscriptionsTable); err != nil {
		return fmt.Errorf("failed to create subscriptions table: %w", err)
	}

	// 创建入站表
	createInboundsTable := `
	CREATE TABLE IF NOT EXISTS inbounds (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		type TEXT NOT NULL,
		port INTEGER NOT NULL,
		username TEXT,
		password TEXT,
		config_type TEXT DEFAULT 'selector',
		group_name TEXT,
		include_names TEXT,
		exclude_names TEXT,
		auto_switch BOOLEAN,
		switch_delay INTEGER,
		switch_delay_unit TEXT,
		urltest_url TEXT,
		urltest_interval INTEGER,
		urltest_interval_unit TEXT,
		urltest_tolerance INTEGER,
		loadbalance_strategy TEXT,
		loadbalance_hash_key TEXT,
		fallback_url TEXT,
		fallback_interval_value INTEGER,
		fallback_interval_unit TEXT,
		settings TEXT,
		sniffing TEXT,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL
	);`

	if _, err := s.db.Exec(createInboundsTable); err != nil {
		return fmt.Errorf("failed to create inbounds table: %w", err)
	}

	// 添加新字段的迁移（如果表已存在）
	s.migrateInboundsTable()

	// 创建规则表
	createRulesTable := `
	CREATE TABLE IF NOT EXISTS rules (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		priority INTEGER NOT NULL DEFAULT 100,
		enabled BOOLEAN NOT NULL DEFAULT 1,

		-- 匹配条件
		inbound TEXT,
		domain TEXT,
		domain_suffix TEXT,
		domain_keyword TEXT,
		domain_regex TEXT,
		geosite TEXT,
		source_geoip TEXT,
		geoip TEXT,
		ip_cidr TEXT,
		source_ip_cidr TEXT,
		port TEXT,
		source_port TEXT,
		process_name TEXT,
		process_path TEXT,
		protocol TEXT,

		-- 出站配置
		outbound_type TEXT NOT NULL,
		group_name TEXT,
		include_names TEXT,
		exclude_names TEXT,

		-- URLTest 配置
		auto_switch BOOLEAN DEFAULT 0,
		urltest_url TEXT,
		urltest_interval INTEGER,
		urltest_interval_unit TEXT,
		urltest_tolerance INTEGER,

		-- LoadBalance 配置
		loadbalance_strategy TEXT,
		loadbalance_hash_key TEXT,

		-- Fallback 配置
		fallback_url TEXT,
		fallback_interval_value INTEGER,
		fallback_interval_unit TEXT,

		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL
	);`

	if _, err := s.db.Exec(createRulesTable); err != nil {
		return fmt.Errorf("failed to create rules table: %w", err)
	}

	return nil
}

// migrateInboundsTable 迁移入站表结构
func (s *SQLiteStorage) migrateInboundsTable() {
	// 检查并添加新字段
	migrations := []string{
		"ALTER TABLE inbounds ADD COLUMN config_type TEXT DEFAULT 'selector'",
		"ALTER TABLE inbounds ADD COLUMN urltest_tolerance INTEGER",
		"ALTER TABLE inbounds ADD COLUMN loadbalance_strategy TEXT",
		"ALTER TABLE inbounds ADD COLUMN loadbalance_hash_key TEXT",
		"ALTER TABLE inbounds ADD COLUMN fallback_url TEXT",
		"ALTER TABLE inbounds ADD COLUMN fallback_interval_value INTEGER",
		"ALTER TABLE inbounds ADD COLUMN fallback_interval_unit TEXT",
	}

	for _, migration := range migrations {
		// 忽略错误，因为字段可能已经存在
		s.db.Exec(migration)
	}
}

// 节点管理方法
func (s *SQLiteStorage) GetNodes() ([]models.Node, error) {
	rows, err := s.db.Query(`
		SELECT id, name, type, address, port, uuid, password, security, network, outbound_id, group_name, created_at, updated_at,
		  tls_enabled, tls_server_name, tls_insecure, transport_type, grpc_service_name, grpc_idle_timeout, grpc_ping_timeout, grpc_permit_without_stream, path, obfs_param, peer
		FROM nodes
		ORDER BY created_at DESC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query nodes: %w", err)
	}
	defer rows.Close()

	var nodes []models.Node
	for rows.Next() {
		var node models.Node
		var createdAt, updatedAt string
		var tlsEnabled sql.NullInt64
		var tlsServerName sql.NullString
		var tlsInsecure sql.NullInt64
		var transportType sql.NullString
		var grpcServiceName sql.NullString
		var grpcIdleTimeout sql.NullString
		var grpcPingTimeout sql.NullString
		var grpcPermitWithoutStream sql.NullInt64
		var path sql.NullString
		var obfsParam sql.NullString
		var peer sql.NullString
		err := rows.Scan(
			&node.ID,
			&node.Name,
			&node.Type,
			&node.Address,
			&node.Port,
			&node.UUID,
			&node.Password,
			&node.Security,
			&node.Network,
			&node.OutboundID,
			&node.Group,
			&createdAt,
			&updatedAt,
			&tlsEnabled,
			&tlsServerName,
			&tlsInsecure,
			&transportType,
			&grpcServiceName,
			&grpcIdleTimeout,
			&grpcPingTimeout,
			&grpcPermitWithoutStream,
			&path,
			&obfsParam,
			&peer,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan node: %w", err)
		}

		// 解析时间
		if node.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt); err != nil {
			node.CreatedAt = time.Now()
		}
		if node.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt); err != nil {
			node.UpdatedAt = time.Now()
		}

		// 解析其他字段
		if tlsEnabled.Valid {
			node.TlsEnabled = ptrInt(int(tlsEnabled.Int64))
		} else {
			node.TlsEnabled = nil
		}
		if tlsServerName.Valid {
			node.TlsServerName = tlsServerName.String
		} else {
			node.TlsServerName = ""
		}
		if tlsInsecure.Valid {
			node.TlsInsecure = ptrInt(int(tlsInsecure.Int64))
		} else {
			node.TlsInsecure = nil
		}
		if transportType.Valid {
			node.TransportType = transportType.String
		} else {
			node.TransportType = ""
		}
		if grpcServiceName.Valid {
			node.GrpcServiceName = grpcServiceName.String
		} else {
			node.GrpcServiceName = ""
		}
		if grpcIdleTimeout.Valid {
			node.GrpcIdleTimeout = grpcIdleTimeout.String
		} else {
			node.GrpcIdleTimeout = ""
		}
		if grpcPingTimeout.Valid {
			node.GrpcPingTimeout = grpcPingTimeout.String
		} else {
			node.GrpcPingTimeout = ""
		}
		if grpcPermitWithoutStream.Valid {
			node.GrpcPermitWithoutStream = ptrInt(int(grpcPermitWithoutStream.Int64))
		} else {
			node.GrpcPermitWithoutStream = nil
		}
		if path.Valid {
			node.Path = path.String
		} else {
			node.Path = ""
		}
		if obfsParam.Valid {
			node.ObfsParam = obfsParam.String
		} else {
			node.ObfsParam = ""
		}
		if peer.Valid {
			node.Peer = peer.String
		} else {
			node.Peer = ""
		}

		nodes = append(nodes, node)
	}

	return nodes, nil
}

func (s *SQLiteStorage) GetNode(id string) (*models.Node, error) {
	var node models.Node
	var createdAt, updatedAt string
	var tlsEnabled sql.NullInt64
	var tlsServerName sql.NullString
	var tlsInsecure sql.NullInt64
	var transportType sql.NullString
	var grpcServiceName sql.NullString
	var grpcIdleTimeout sql.NullString
	var grpcPingTimeout sql.NullString
	var grpcPermitWithoutStream sql.NullInt64
	var path sql.NullString
	var obfsParam sql.NullString
	var peer sql.NullString

	err := s.db.QueryRow(`
		SELECT id, name, type, address, port, uuid, password, security, network, outbound_id, group_name, created_at, updated_at,
		  tls_enabled, tls_server_name, tls_insecure, transport_type, grpc_service_name, grpc_idle_timeout, grpc_ping_timeout, grpc_permit_without_stream, path, obfs_param, peer
		FROM nodes
		WHERE id = ?
	`, id).Scan(
		&node.ID,
		&node.Name,
		&node.Type,
		&node.Address,
		&node.Port,
		&node.UUID,
		&node.Password,
		&node.Security,
		&node.Network,
		&node.OutboundID,
		&node.Group,
		&createdAt,
		&updatedAt,
		&tlsEnabled,
		&tlsServerName,
		&tlsInsecure,
		&transportType,
		&grpcServiceName,
		&grpcIdleTimeout,
		&grpcPingTimeout,
		&grpcPermitWithoutStream,
		&path,
		&obfsParam,
		&peer,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("node not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get node: %w", err)
	}

	// 解析时间
	if node.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt); err != nil {
		node.CreatedAt = time.Now()
	}
	if node.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt); err != nil {
		node.UpdatedAt = time.Now()
	}

	// 解析其他字段
	if tlsEnabled.Valid {
		node.TlsEnabled = ptrInt(int(tlsEnabled.Int64))
	} else {
		node.TlsEnabled = nil
	}
	if tlsServerName.Valid {
		node.TlsServerName = tlsServerName.String
	} else {
		node.TlsServerName = ""
	}
	if tlsInsecure.Valid {
		node.TlsInsecure = ptrInt(int(tlsInsecure.Int64))
	} else {
		node.TlsInsecure = nil
	}
	if transportType.Valid {
		node.TransportType = transportType.String
	} else {
		node.TransportType = ""
	}
	if grpcServiceName.Valid {
		node.GrpcServiceName = grpcServiceName.String
	} else {
		node.GrpcServiceName = ""
	}
	if grpcIdleTimeout.Valid {
		node.GrpcIdleTimeout = grpcIdleTimeout.String
	} else {
		node.GrpcIdleTimeout = ""
	}
	if grpcPingTimeout.Valid {
		node.GrpcPingTimeout = grpcPingTimeout.String
	} else {
		node.GrpcPingTimeout = ""
	}
	if grpcPermitWithoutStream.Valid {
		node.GrpcPermitWithoutStream = ptrInt(int(grpcPermitWithoutStream.Int64))
	} else {
		node.GrpcPermitWithoutStream = nil
	}
	if path.Valid {
		node.Path = path.String
	} else {
		node.Path = ""
	}
	if obfsParam.Valid {
		node.ObfsParam = obfsParam.String
	} else {
		node.ObfsParam = ""
	}
	if peer.Valid {
		node.Peer = peer.String
	} else {
		node.Peer = ""
	}

	return &node, nil
}

func (s *SQLiteStorage) CreateNode(node *models.Node) error {
	now := time.Now()
	node.CreatedAt = now
	node.UpdatedAt = now

	_, err := s.db.Exec(`
		INSERT INTO nodes (id, name, type, address, port, uuid, password, security, network, outbound_id, group_name, created_at, updated_at,
		  tls_enabled, tls_server_name, tls_insecure, transport_type, grpc_service_name, grpc_idle_timeout, grpc_ping_timeout, grpc_permit_without_stream, path, obfs_param, peer)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`,
		node.ID,
		node.Name,
		node.Type,
		node.Address,
		node.Port,
		node.UUID,
		node.Password,
		node.Security,
		node.Network,
		node.OutboundID,
		node.Group,
		node.CreatedAt.Format("2006-01-02 15:04:05"),
		node.UpdatedAt.Format("2006-01-02 15:04:05"),
		node.TlsEnabled,
		node.TlsServerName,
		node.TlsInsecure,
		node.TransportType,
		node.GrpcServiceName,
		node.GrpcIdleTimeout,
		node.GrpcPingTimeout,
		node.GrpcPermitWithoutStream,
		node.Path,
		node.ObfsParam,
		node.Peer,
	)

	if err != nil {
		return fmt.Errorf("failed to create node: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) UpdateNode(node *models.Node) error {
	node.UpdatedAt = time.Now()

	_, err := s.db.Exec(`
		UPDATE nodes
		SET name = ?, type = ?, address = ?, port = ?, uuid = ?, password = ?, security = ?, network = ?, outbound_id = ?, group_name = ?, updated_at = ?,
		  tls_enabled = ?, tls_server_name = ?, tls_insecure = ?, transport_type = ?, grpc_service_name = ?, grpc_idle_timeout = ?, grpc_ping_timeout = ?, grpc_permit_without_stream = ?, path = ?, obfs_param = ?, peer = ?
		WHERE id = ?
	`,
		node.Name,
		node.Type,
		node.Address,
		node.Port,
		node.UUID,
		node.Password,
		node.Security,
		node.Network,
		node.OutboundID,
		node.Group,
		node.UpdatedAt.Format("2006-01-02 15:04:05"),
		node.TlsEnabled,
		node.TlsServerName,
		node.TlsInsecure,
		node.TransportType,
		node.GrpcServiceName,
		node.GrpcIdleTimeout,
		node.GrpcPingTimeout,
		node.GrpcPermitWithoutStream,
		node.Path,
		node.ObfsParam,
		node.Peer,
		node.ID,
	)

	if err != nil {
		return fmt.Errorf("failed to update node: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) DeleteNode(id string) error {
	result, err := s.db.Exec("DELETE FROM nodes WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete node: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("node not found: %s", id)
	}

	return nil
}

// DeleteNodesByGroup 删除指定分组的节点
func (s *SQLiteStorage) DeleteNodesByGroup(group string) error {
	_, err := s.db.Exec("DELETE FROM nodes WHERE group_name = ?", group)
	if err != nil {
		return fmt.Errorf("failed to delete nodes by group: %w", err)
	}

	// 如果没有删除任何节点，也不报错，因为可能该分组本来就没有节点
	return nil
}

func (s *SQLiteStorage) ImportNodes(nodes []models.Node) error {
	tx, err := s.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	now := time.Now()
	for i := range nodes {
		nodes[i].CreatedAt = now
		nodes[i].UpdatedAt = now

		_, err := tx.Exec(`
			INSERT OR REPLACE INTO nodes (id, name, type, address, port, uuid, password, security, network, outbound_id, group_name, created_at, updated_at,
			  tls_enabled, tls_server_name, tls_insecure, transport_type, grpc_service_name, grpc_idle_timeout, grpc_ping_timeout, grpc_permit_without_stream, path, obfs_param, peer)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`,
			nodes[i].ID,
			nodes[i].Name,
			nodes[i].Type,
			nodes[i].Address,
			nodes[i].Port,
			nodes[i].UUID,
			nodes[i].Password,
			nodes[i].Security,
			nodes[i].Network,
			nodes[i].OutboundID,
			nodes[i].Group,
			nodes[i].CreatedAt.Format("2006-01-02 15:04:05"),
			nodes[i].UpdatedAt.Format("2006-01-02 15:04:05"),
			nodes[i].TlsEnabled,
			nodes[i].TlsServerName,
			nodes[i].TlsInsecure,
			nodes[i].TransportType,
			nodes[i].GrpcServiceName,
			nodes[i].GrpcIdleTimeout,
			nodes[i].GrpcPingTimeout,
			nodes[i].GrpcPermitWithoutStream,
			nodes[i].Path,
			nodes[i].ObfsParam,
			nodes[i].Peer,
		)

		if err != nil {
			return fmt.Errorf("failed to import node %s: %w", nodes[i].ID, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// 订阅管理方法
func (s *SQLiteStorage) GetSubscriptions() ([]models.Subscription, error) {
	rows, err := s.db.Query(`
		SELECT id, name, url, enabled, node_count, last_update, update_interval, description, created_at, updated_at
		FROM subscriptions
		ORDER BY created_at DESC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query subscriptions: %w", err)
	}
	defer rows.Close()

	var subscriptions []models.Subscription
	for rows.Next() {
		var sub models.Subscription
		var createdAt, updatedAt, lastUpdate sql.NullString
		err := rows.Scan(
			&sub.ID,
			&sub.Name,
			&sub.URL,
			&sub.Enabled,
			&sub.NodeCount,
			&lastUpdate,
			&sub.UpdateInterval,
			&sub.Description,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan subscription: %w", err)
		}

		// 解析时间
		if createdAt.Valid {
			if sub.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt.String); err != nil {
				sub.CreatedAt = time.Now()
			}
		}
		if updatedAt.Valid {
			if sub.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt.String); err != nil {
				sub.UpdatedAt = time.Now()
			}
		}
		if lastUpdate.Valid {
			sub.LastUpdate = lastUpdate.String
		}

		subscriptions = append(subscriptions, sub)
	}

	return subscriptions, nil
}

func (s *SQLiteStorage) GetSubscription(id string) (*models.Subscription, error) {
	var sub models.Subscription
	var createdAt, updatedAt, lastUpdate sql.NullString

	err := s.db.QueryRow(`
		SELECT id, name, url, enabled, node_count, last_update, update_interval, description, created_at, updated_at
		FROM subscriptions
		WHERE id = ?
	`, id).Scan(
		&sub.ID,
		&sub.Name,
		&sub.URL,
		&sub.Enabled,
		&sub.NodeCount,
		&lastUpdate,
		&sub.UpdateInterval,
		&sub.Description,
		&createdAt,
		&updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("subscription not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	// 解析时间
	if createdAt.Valid {
		if sub.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt.String); err != nil {
			sub.CreatedAt = time.Now()
		}
	}
	if updatedAt.Valid {
		if sub.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt.String); err != nil {
			sub.UpdatedAt = time.Now()
		}
	}
	if lastUpdate.Valid {
		sub.LastUpdate = lastUpdate.String
	}

	return &sub, nil
}

func (s *SQLiteStorage) CreateSubscription(sub *models.Subscription) error {
	now := time.Now()
	sub.CreatedAt = now
	sub.UpdatedAt = now

	_, err := s.db.Exec(`
		INSERT INTO subscriptions (id, name, url, enabled, node_count, last_update, update_interval, description, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`,
		sub.ID,
		sub.Name,
		sub.URL,
		sub.Enabled,
		sub.NodeCount,
		sub.LastUpdate,
		sub.UpdateInterval,
		sub.Description,
		sub.CreatedAt.Format("2006-01-02 15:04:05"),
		sub.UpdatedAt.Format("2006-01-02 15:04:05"),
	)

	if err != nil {
		return fmt.Errorf("failed to create subscription: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) UpdateSubscription(sub *models.Subscription) error {
	sub.UpdatedAt = time.Now()

	_, err := s.db.Exec(`
		UPDATE subscriptions
		SET name = ?, url = ?, enabled = ?, node_count = ?, last_update = ?, update_interval = ?, description = ?, updated_at = ?
		WHERE id = ?
	`,
		sub.Name,
		sub.URL,
		sub.Enabled,
		sub.NodeCount,
		sub.LastUpdate,
		sub.UpdateInterval,
		sub.Description,
		sub.UpdatedAt.Format("2006-01-02 15:04:05"),
		sub.ID,
	)

	if err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) DeleteSubscription(id string) error {
	result, err := s.db.Exec("DELETE FROM subscriptions WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete subscription: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("subscription not found: %s", id)
	}

	return nil
}

// 入站管理方法
func (s *SQLiteStorage) GetInbounds() ([]models.Inbound, error) {
	rows, err := s.db.Query(`
		SELECT id, name, type, port, username, password, config_type, group_name, include_names, exclude_names,
			   auto_switch, switch_delay, switch_delay_unit, urltest_url, urltest_interval, urltest_interval_unit, urltest_tolerance,
			   loadbalance_strategy, loadbalance_hash_key, fallback_url, fallback_interval_value, fallback_interval_unit,
			   settings, sniffing, created_at, updated_at
		FROM inbounds
		ORDER BY created_at ASC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query inbounds: %w", err)
	}
	defer rows.Close()

	var inbounds []models.Inbound
	for rows.Next() {
		var inbound models.Inbound
		var includeNamesStr, excludeNamesStr, settingsStr, sniffingStr, createdAt, updatedAt string
		var urltestTolerance, fallbackIntervalValue sql.NullInt64
		var configType, loadbalanceStrategy, loadbalanceHashKey, fallbackURL, fallbackIntervalUnit sql.NullString

		err := rows.Scan(
			&inbound.ID,
			&inbound.Name,
			&inbound.Type,
			&inbound.Port,
			&inbound.Username,
			&inbound.Password,
			&configType,
			&inbound.Group,
			&includeNamesStr,
			&excludeNamesStr,
			&inbound.AutoSwitch,
			&inbound.SwitchDelay,
			&inbound.SwitchDelayUnit,
			&inbound.UrlTestUrl,
			&inbound.UrlTestInterval,
			&inbound.UrlTestIntervalUnit,
			&urltestTolerance,
			&loadbalanceStrategy,
			&loadbalanceHashKey,
			&fallbackURL,
			&fallbackIntervalValue,
			&fallbackIntervalUnit,
			&settingsStr,
			&sniffingStr,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan inbound: %w", err)
		}

		// 处理 NULL 值
		if configType.Valid {
			inbound.ConfigType = configType.String
		} else {
			inbound.ConfigType = "selector" // 默认值
		}

		if urltestTolerance.Valid {
			inbound.UrltestTolerance = int(urltestTolerance.Int64)
		}

		if loadbalanceStrategy.Valid {
			inbound.LoadbalanceStrategy = loadbalanceStrategy.String
		}

		if loadbalanceHashKey.Valid {
			inbound.LoadbalanceHashKey = loadbalanceHashKey.String
		}

		if fallbackURL.Valid {
			inbound.FallbackURL = fallbackURL.String
		}

		if fallbackIntervalValue.Valid {
			inbound.FallbackIntervalValue = int(fallbackIntervalValue.Int64)
		}

		if fallbackIntervalUnit.Valid {
			inbound.FallbackIntervalUnit = fallbackIntervalUnit.String
		}

		// 解析包含名称
		if includeNamesStr != "" {
			if err := json.Unmarshal([]byte(includeNamesStr), &inbound.IncludeNames); err != nil {
				return nil, fmt.Errorf("failed to unmarshal include_names: %w", err)
			}
		}

		// 解析排除名称
		if excludeNamesStr != "" {
			if err := json.Unmarshal([]byte(excludeNamesStr), &inbound.ExcludeNames); err != nil {
				return nil, fmt.Errorf("failed to unmarshal exclude_names: %w", err)
			}
		}

		// 解析设置
		if settingsStr != "" {
			if err := json.Unmarshal([]byte(settingsStr), &inbound.Settings); err != nil {
				return nil, fmt.Errorf("failed to unmarshal settings: %w", err)
			}
		}

		// 解析嗅探配置
		if sniffingStr != "" {
			if err := json.Unmarshal([]byte(sniffingStr), &inbound.Sniffing); err != nil {
				return nil, fmt.Errorf("failed to unmarshal sniffing: %w", err)
			}
		}

		// 解析时间
		if inbound.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt); err != nil {
			inbound.CreatedAt = time.Now()
		}
		if inbound.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt); err != nil {
			inbound.UpdatedAt = time.Now()
		}

		inbounds = append(inbounds, inbound)
	}

	return inbounds, nil
}

func (s *SQLiteStorage) GetInbound(id string) (*models.Inbound, error) {
	var inbound models.Inbound
	var includeNamesStr, excludeNamesStr, settingsStr, sniffingStr, createdAt, updatedAt string

	err := s.db.QueryRow(`
		SELECT id, name, type, port, username, password, group_name, include_names, exclude_names,
			   auto_switch, switch_delay, switch_delay_unit, urltest_url, urltest_interval, urltest_interval_unit,
			   settings, sniffing, created_at, updated_at
		FROM inbounds
		WHERE id = ?
	`, id).Scan(
		&inbound.ID,
		&inbound.Name,
		&inbound.Type,
		&inbound.Port,
		&inbound.Username,
		&inbound.Password,
		&inbound.Group,
		&includeNamesStr,
		&excludeNamesStr,
		&inbound.AutoSwitch,
		&inbound.SwitchDelay,
		&inbound.SwitchDelayUnit,
		&inbound.UrlTestUrl,
		&inbound.UrlTestInterval,
		&inbound.UrlTestIntervalUnit,
		&settingsStr,
		&sniffingStr,
		&createdAt,
		&updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("inbound not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get inbound: %w", err)
	}

	// 解析包含名称
	if includeNamesStr != "" {
		if err := json.Unmarshal([]byte(includeNamesStr), &inbound.IncludeNames); err != nil {
			return nil, fmt.Errorf("failed to unmarshal include_names: %w", err)
		}
	}

	// 解析排除名称
	if excludeNamesStr != "" {
		if err := json.Unmarshal([]byte(excludeNamesStr), &inbound.ExcludeNames); err != nil {
			return nil, fmt.Errorf("failed to unmarshal exclude_names: %w", err)
		}
	}

	// 解析设置
	if settingsStr != "" {
		if err := json.Unmarshal([]byte(settingsStr), &inbound.Settings); err != nil {
			return nil, fmt.Errorf("failed to unmarshal settings: %w", err)
		}
	}

	// 解析嗅探配置
	if sniffingStr != "" {
		if err := json.Unmarshal([]byte(sniffingStr), &inbound.Sniffing); err != nil {
			return nil, fmt.Errorf("failed to unmarshal sniffing: %w", err)
		}
	}

	// 解析时间
	if inbound.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt); err != nil {
		inbound.CreatedAt = time.Now()
	}
	if inbound.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt); err != nil {
		inbound.UpdatedAt = time.Now()
	}

	return &inbound, nil
}

func (s *SQLiteStorage) CreateInbound(inbound *models.Inbound) error {
	now := time.Now()
	inbound.CreatedAt = now
	inbound.UpdatedAt = now

	// 序列化包含名称
	includeNamesStr := ""
	if len(inbound.IncludeNames) > 0 {
		includeNamesBytes, err := json.Marshal(inbound.IncludeNames)
		if err != nil {
			return fmt.Errorf("failed to marshal include_names: %w", err)
		}
		includeNamesStr = string(includeNamesBytes)
	}

	// 序列化排除名称
	excludeNamesStr := ""
	if len(inbound.ExcludeNames) > 0 {
		excludeNamesBytes, err := json.Marshal(inbound.ExcludeNames)
		if err != nil {
			return fmt.Errorf("failed to marshal exclude_names: %w", err)
		}
		excludeNamesStr = string(excludeNamesBytes)
	}

	// 序列化设置
	settingsStr := ""
	if inbound.Settings != nil {
		settingsBytes, err := json.Marshal(inbound.Settings)
		if err != nil {
			return fmt.Errorf("failed to marshal settings: %w", err)
		}
		settingsStr = string(settingsBytes)
	}

	// 序列化嗅探配置
	sniffingStr := ""
	if inbound.Sniffing != nil {
		sniffingBytes, err := json.Marshal(inbound.Sniffing)
		if err != nil {
			return fmt.Errorf("failed to marshal sniffing: %w", err)
		}
		sniffingStr = string(sniffingBytes)
	}

	_, err := s.db.Exec(`
		INSERT INTO inbounds (id, name, type, port, username, password, config_type, group_name, include_names, exclude_names,
			auto_switch, switch_delay, switch_delay_unit, urltest_url, urltest_interval, urltest_interval_unit, urltest_tolerance,
			loadbalance_strategy, loadbalance_hash_key, fallback_url, fallback_interval_value, fallback_interval_unit,
			settings, sniffing, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`,
		inbound.ID,
		inbound.Name,
		inbound.Type,
		inbound.Port,
		inbound.Username,
		inbound.Password,
		inbound.ConfigType,
		inbound.Group,
		includeNamesStr,
		excludeNamesStr,
		inbound.AutoSwitch,
		inbound.SwitchDelay,
		inbound.SwitchDelayUnit,
		inbound.UrlTestUrl,
		inbound.UrlTestInterval,
		inbound.UrlTestIntervalUnit,
		inbound.UrltestTolerance,
		inbound.LoadbalanceStrategy,
		inbound.LoadbalanceHashKey,
		inbound.FallbackURL,
		inbound.FallbackIntervalValue,
		inbound.FallbackIntervalUnit,
		settingsStr,
		sniffingStr,
		inbound.CreatedAt.Format("2006-01-02 15:04:05"),
		inbound.UpdatedAt.Format("2006-01-02 15:04:05"),
	)

	if err != nil {
		return fmt.Errorf("failed to create inbound: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) UpdateInbound(inbound *models.Inbound) error {
	inbound.UpdatedAt = time.Now()

	// 序列化包含名称
	includeNamesStr := ""
	if len(inbound.IncludeNames) > 0 {
		includeNamesBytes, err := json.Marshal(inbound.IncludeNames)
		if err != nil {
			return fmt.Errorf("failed to marshal include_names: %w", err)
		}
		includeNamesStr = string(includeNamesBytes)
	}

	// 序列化排除名称
	excludeNamesStr := ""
	if len(inbound.ExcludeNames) > 0 {
		excludeNamesBytes, err := json.Marshal(inbound.ExcludeNames)
		if err != nil {
			return fmt.Errorf("failed to marshal exclude_names: %w", err)
		}
		excludeNamesStr = string(excludeNamesBytes)
	}

	// 序列化设置
	settingsStr := ""
	if inbound.Settings != nil {
		settingsBytes, err := json.Marshal(inbound.Settings)
		if err != nil {
			return fmt.Errorf("failed to marshal settings: %w", err)
		}
		settingsStr = string(settingsBytes)
	}

	// 序列化嗅探配置
	sniffingStr := ""
	if inbound.Sniffing != nil {
		sniffingBytes, err := json.Marshal(inbound.Sniffing)
		if err != nil {
			return fmt.Errorf("failed to marshal sniffing: %w", err)
		}
		sniffingStr = string(sniffingBytes)
	}

	_, err := s.db.Exec(`
		UPDATE inbounds
		SET name = ?, type = ?, port = ?, username = ?, password = ?, config_type = ?, group_name = ?, include_names = ?, exclude_names = ?,
			auto_switch = ?, switch_delay = ?, switch_delay_unit = ?, urltest_url = ?, urltest_interval = ?, urltest_interval_unit = ?, urltest_tolerance = ?,
			loadbalance_strategy = ?, loadbalance_hash_key = ?, fallback_url = ?, fallback_interval_value = ?, fallback_interval_unit = ?,
			settings = ?, sniffing = ?, updated_at = ?
		WHERE id = ?
	`,
		inbound.Name,
		inbound.Type,
		inbound.Port,
		inbound.Username,
		inbound.Password,
		inbound.ConfigType,
		inbound.Group,
		includeNamesStr,
		excludeNamesStr,
		inbound.AutoSwitch,
		inbound.SwitchDelay,
		inbound.SwitchDelayUnit,
		inbound.UrlTestUrl,
		inbound.UrlTestInterval,
		inbound.UrlTestIntervalUnit,
		inbound.UrltestTolerance,
		inbound.LoadbalanceStrategy,
		inbound.LoadbalanceHashKey,
		inbound.FallbackURL,
		inbound.FallbackIntervalValue,
		inbound.FallbackIntervalUnit,
		settingsStr,
		sniffingStr,
		inbound.UpdatedAt.Format("2006-01-02 15:04:05"),
		inbound.ID,
	)

	if err != nil {
		return fmt.Errorf("failed to update inbound: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) DeleteInbound(id string) error {
	result, err := s.db.Exec("DELETE FROM inbounds WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete inbound: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("inbound not found: %s", id)
	}

	return nil
}

// 出站管理方法

func (s *SQLiteStorage) GetConfig() (*models.Config, error) {
	return &models.Config{}, nil
}

func (s *SQLiteStorage) SaveConfig(config *models.Config) error {
	return fmt.Errorf("not implemented")
}

// GetNodesBySubscription 按订阅分组获取节点
func (s *SQLiteStorage) GetNodesBySubscription() (map[string][]models.Node, error) {
	rows, err := s.db.Query(`
		SELECT id, name, type, address, port, uuid, password, security, network, outbound_id, group_name, created_at, updated_at,
		       tls_enabled, tls_server_name, tls_insecure, transport_type, grpc_service_name, grpc_idle_timeout, grpc_ping_timeout, grpc_permit_without_stream, path, obfs_param, peer
		FROM nodes
		ORDER BY created_at DESC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query nodes: %w", err)
	}
	defer rows.Close()

	nodesByGroup := make(map[string][]models.Node)

	for rows.Next() {
		var node models.Node
		var createdAt, updatedAt string
		var tlsEnabled sql.NullInt64
		var tlsServerName sql.NullString
		var tlsInsecure sql.NullInt64
		var transportType sql.NullString
		var grpcServiceName sql.NullString
		var grpcIdleTimeout sql.NullString
		var grpcPingTimeout sql.NullString
		var grpcPermitWithoutStream sql.NullInt64
		var path sql.NullString
		var obfsParam sql.NullString
		var peer sql.NullString
		var groupName sql.NullString
		err := rows.Scan(
			&node.ID,
			&node.Name,
			&node.Type,
			&node.Address,
			&node.Port,
			&node.UUID,
			&node.Password,
			&node.Security,
			&node.Network,
			&node.OutboundID,
			&groupName,
			&createdAt,
			&updatedAt,
			&tlsEnabled,
			&tlsServerName,
			&tlsInsecure,
			&transportType,
			&grpcServiceName,
			&grpcIdleTimeout,
			&grpcPingTimeout,
			&grpcPermitWithoutStream,
			&path,
			&obfsParam,
			&peer,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan node: %w", err)
		}

		// 解析时间
		if node.CreatedAt, err = time.Parse("2006-01-02 15:04:05", createdAt); err != nil {
			node.CreatedAt = time.Now()
		}
		if node.UpdatedAt, err = time.Parse("2006-01-02 15:04:05", updatedAt); err != nil {
			node.UpdatedAt = time.Now()
		}

		// 解析其他字段
		if tlsEnabled.Valid {
			node.TlsEnabled = ptrInt(int(tlsEnabled.Int64))
		} else {
			node.TlsEnabled = nil
		}
		if tlsServerName.Valid {
			node.TlsServerName = tlsServerName.String
		} else {
			node.TlsServerName = ""
		}
		if tlsInsecure.Valid {
			node.TlsInsecure = ptrInt(int(tlsInsecure.Int64))
		} else {
			node.TlsInsecure = nil
		}
		if transportType.Valid {
			node.TransportType = transportType.String
		} else {
			node.TransportType = ""
		}
		if grpcServiceName.Valid {
			node.GrpcServiceName = grpcServiceName.String
		} else {
			node.GrpcServiceName = ""
		}
		if grpcIdleTimeout.Valid {
			node.GrpcIdleTimeout = grpcIdleTimeout.String
		} else {
			node.GrpcIdleTimeout = ""
		}
		if grpcPingTimeout.Valid {
			node.GrpcPingTimeout = grpcPingTimeout.String
		} else {
			node.GrpcPingTimeout = ""
		}
		if grpcPermitWithoutStream.Valid {
			node.GrpcPermitWithoutStream = ptrInt(int(grpcPermitWithoutStream.Int64))
		} else {
			node.GrpcPermitWithoutStream = nil
		}
		if path.Valid {
			node.Path = path.String
		} else {
			node.Path = ""
		}
		if obfsParam.Valid {
			node.ObfsParam = obfsParam.String
		} else {
			node.ObfsParam = ""
		}
		if peer.Valid {
			node.Peer = peer.String
		} else {
			node.Peer = ""
		}

		// 修正 group 字段，避免 NULL 导致 500 错误
		if groupName.Valid && groupName.String != "" {
			node.Group = groupName.String
		} else {
			node.Group = "默认分组"
		}

		// grpc 类型节点 transport 字段格式化
		if node.TransportType == "grpc" || node.Network == "grpc" {
			if node.GrpcServiceName == "" {
				node.GrpcServiceName = "default_service"
			}
			if node.GrpcIdleTimeout == "" {
				node.GrpcIdleTimeout = "60s"
			}
			if node.GrpcPingTimeout == "" {
				node.GrpcPingTimeout = "20s"
			}
			if node.GrpcPermitWithoutStream == nil {
				val := 0
				node.GrpcPermitWithoutStream = &val
			}
			// path 字段等于 service_name
			node.Path = node.GrpcServiceName
			// transport 字段按模板生成，key顺序与样本一致
			permit := false
			if *node.GrpcPermitWithoutStream == 1 {
				permit = true
			}
			node.Transport = map[string]interface{}{
				"type":                  "grpc",
				"service_name":          node.GrpcServiceName,
				"idle_timeout":          node.GrpcIdleTimeout,
				"ping_timeout":          node.GrpcPingTimeout,
				"permit_without_stream": permit,
			}
		}
		// peer 字段不再使用

		nodesByGroup[node.Group] = append(nodesByGroup[node.Group], node)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate nodes: %w", err)
	}

	return nodesByGroup, nil
}

// DeleteAllNodes 删除所有节点
func (s *SQLiteStorage) DeleteAllNodes() error {
	_, err := s.db.Exec("DELETE FROM nodes")
	if err != nil {
		return fmt.Errorf("failed to delete all nodes: %w", err)
	}
	return nil
}

// 规则管理方法
func (s *SQLiteStorage) GetRules() ([]models.Rule, error) {
	rows, err := s.db.Query(`
		SELECT id, name, priority, enabled,
			   inbound, domain, domain_suffix, domain_keyword, domain_regex,
			   geosite, source_geoip, geoip, ip_cidr, source_ip_cidr,
			   port, source_port, process_name, process_path, protocol,
			   outbound_type, group_name, include_names, exclude_names,
			   auto_switch, urltest_url, urltest_interval, urltest_interval_unit, urltest_tolerance,
			   loadbalance_strategy, loadbalance_hash_key,
			   fallback_url, fallback_interval_value, fallback_interval_unit,
			   created_at, updated_at
		FROM rules
		ORDER BY priority ASC, created_at DESC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query rules: %w", err)
	}
	defer rows.Close()

	var rules []models.Rule
	for rows.Next() {
		var rule models.Rule
		var inboundStr, domainStr, domainSuffixStr, domainKeywordStr, domainRegexStr string
		var geositeStr, sourceGeoipStr, geoipStr, ipCidrStr, sourceIpCidrStr string
		var portStr, sourcePortStr, processNameStr, processPathStr, protocolStr string
		var groupName, includeNamesStr, excludeNamesStr string
		var urltestUrl, urltestIntervalUnit, loadbalanceStrategy, loadbalanceHashKey string
		var fallbackUrl, fallbackIntervalUnit string
		var urltestInterval, urltestTolerance, fallbackIntervalValue sql.NullInt64
		var autoSwitch sql.NullBool
		var createdAt, updatedAt string

		err := rows.Scan(
			&rule.ID, &rule.Name, &rule.Priority, &rule.Enabled,
			&inboundStr, &domainStr, &domainSuffixStr, &domainKeywordStr, &domainRegexStr,
			&geositeStr, &sourceGeoipStr, &geoipStr, &ipCidrStr, &sourceIpCidrStr,
			&portStr, &sourcePortStr, &processNameStr, &processPathStr, &protocolStr,
			&rule.OutboundType, &groupName, &includeNamesStr, &excludeNamesStr,
			&autoSwitch, &urltestUrl, &urltestInterval, &urltestIntervalUnit, &urltestTolerance,
			&loadbalanceStrategy, &loadbalanceHashKey,
			&fallbackUrl, &fallbackIntervalValue, &fallbackIntervalUnit,
			&createdAt, &updatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan rule: %w", err)
		}

		// 解析 JSON 字符串数组
		if inboundStr != "" {
			json.Unmarshal([]byte(inboundStr), &rule.Inbound)
		}
		if domainStr != "" {
			json.Unmarshal([]byte(domainStr), &rule.Domain)
		}
		if domainSuffixStr != "" {
			json.Unmarshal([]byte(domainSuffixStr), &rule.DomainSuffix)
		}
		if domainKeywordStr != "" {
			json.Unmarshal([]byte(domainKeywordStr), &rule.DomainKeyword)
		}
		if domainRegexStr != "" {
			json.Unmarshal([]byte(domainRegexStr), &rule.DomainRegex)
		}
		if geositeStr != "" {
			json.Unmarshal([]byte(geositeStr), &rule.Geosite)
		}
		if sourceGeoipStr != "" {
			json.Unmarshal([]byte(sourceGeoipStr), &rule.SourceGeoIP)
		}
		if geoipStr != "" {
			json.Unmarshal([]byte(geoipStr), &rule.Geoip)
		}
		if ipCidrStr != "" {
			json.Unmarshal([]byte(ipCidrStr), &rule.IPCidr)
		}
		if sourceIpCidrStr != "" {
			json.Unmarshal([]byte(sourceIpCidrStr), &rule.SourceIPCidr)
		}
		if portStr != "" {
			json.Unmarshal([]byte(portStr), &rule.Port)
		}
		if sourcePortStr != "" {
			json.Unmarshal([]byte(sourcePortStr), &rule.SourcePort)
		}
		if processNameStr != "" {
			json.Unmarshal([]byte(processNameStr), &rule.ProcessName)
		}
		if processPathStr != "" {
			json.Unmarshal([]byte(processPathStr), &rule.ProcessPath)
		}
		if protocolStr != "" {
			json.Unmarshal([]byte(protocolStr), &rule.Protocol)
		}
		if includeNamesStr != "" {
			json.Unmarshal([]byte(includeNamesStr), &rule.IncludeNames)
		}
		if excludeNamesStr != "" {
			json.Unmarshal([]byte(excludeNamesStr), &rule.ExcludeNames)
		}

		// 处理可选字段
		rule.Group = groupName
		if autoSwitch.Valid {
			rule.AutoSwitch = autoSwitch.Bool
		}
		rule.UrlTestUrl = urltestUrl
		if urltestInterval.Valid {
			rule.UrlTestInterval = int(urltestInterval.Int64)
		}
		rule.UrlTestIntervalUnit = urltestIntervalUnit
		if urltestTolerance.Valid {
			rule.UrltestTolerance = int(urltestTolerance.Int64)
		}
		rule.LoadbalanceStrategy = loadbalanceStrategy
		rule.LoadbalanceHashKey = loadbalanceHashKey
		rule.FallbackURL = fallbackUrl
		if fallbackIntervalValue.Valid {
			rule.FallbackIntervalValue = int(fallbackIntervalValue.Int64)
		}
		rule.FallbackIntervalUnit = fallbackIntervalUnit

		// 解析时间
		rule.CreatedAt, _ = time.Parse("2006-01-02 15:04:05", createdAt)
		rule.UpdatedAt, _ = time.Parse("2006-01-02 15:04:05", updatedAt)

		rules = append(rules, rule)
	}

	return rules, nil
}

func (s *SQLiteStorage) CreateRule(rule *models.Rule) error {
	// 序列化数组字段
	inboundStr, _ := json.Marshal(rule.Inbound)
	domainStr, _ := json.Marshal(rule.Domain)
	domainSuffixStr, _ := json.Marshal(rule.DomainSuffix)
	domainKeywordStr, _ := json.Marshal(rule.DomainKeyword)
	domainRegexStr, _ := json.Marshal(rule.DomainRegex)
	geositeStr, _ := json.Marshal(rule.Geosite)
	sourceGeoipStr, _ := json.Marshal(rule.SourceGeoIP)
	geoipStr, _ := json.Marshal(rule.Geoip)
	ipCidrStr, _ := json.Marshal(rule.IPCidr)
	sourceIpCidrStr, _ := json.Marshal(rule.SourceIPCidr)
	portStr, _ := json.Marshal(rule.Port)
	sourcePortStr, _ := json.Marshal(rule.SourcePort)
	processNameStr, _ := json.Marshal(rule.ProcessName)
	processPathStr, _ := json.Marshal(rule.ProcessPath)
	protocolStr, _ := json.Marshal(rule.Protocol)
	includeNamesStr, _ := json.Marshal(rule.IncludeNames)
	excludeNamesStr, _ := json.Marshal(rule.ExcludeNames)

	_, err := s.db.Exec(`
		INSERT INTO rules (
			id, name, priority, enabled,
			inbound, domain, domain_suffix, domain_keyword, domain_regex,
			geosite, source_geoip, geoip, ip_cidr, source_ip_cidr,
			port, source_port, process_name, process_path, protocol,
			outbound_type, group_name, include_names, exclude_names,
			auto_switch, urltest_url, urltest_interval, urltest_interval_unit, urltest_tolerance,
			loadbalance_strategy, loadbalance_hash_key,
			fallback_url, fallback_interval_value, fallback_interval_unit,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`,
		rule.ID, rule.Name, rule.Priority, rule.Enabled,
		string(inboundStr), string(domainStr), string(domainSuffixStr), string(domainKeywordStr), string(domainRegexStr),
		string(geositeStr), string(sourceGeoipStr), string(geoipStr), string(ipCidrStr), string(sourceIpCidrStr),
		string(portStr), string(sourcePortStr), string(processNameStr), string(processPathStr), string(protocolStr),
		rule.OutboundType, rule.Group, string(includeNamesStr), string(excludeNamesStr),
		rule.AutoSwitch, rule.UrlTestUrl, rule.UrlTestInterval, rule.UrlTestIntervalUnit, rule.UrltestTolerance,
		rule.LoadbalanceStrategy, rule.LoadbalanceHashKey,
		rule.FallbackURL, rule.FallbackIntervalValue, rule.FallbackIntervalUnit,
		rule.CreatedAt.Format("2006-01-02 15:04:05"), rule.UpdatedAt.Format("2006-01-02 15:04:05"),
	)

	if err != nil {
		return fmt.Errorf("failed to create rule: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) UpdateRule(rule *models.Rule) error {
	// 序列化数组字段
	inboundStr, _ := json.Marshal(rule.Inbound)
	domainStr, _ := json.Marshal(rule.Domain)
	domainSuffixStr, _ := json.Marshal(rule.DomainSuffix)
	domainKeywordStr, _ := json.Marshal(rule.DomainKeyword)
	domainRegexStr, _ := json.Marshal(rule.DomainRegex)
	geositeStr, _ := json.Marshal(rule.Geosite)
	sourceGeoipStr, _ := json.Marshal(rule.SourceGeoIP)
	geoipStr, _ := json.Marshal(rule.Geoip)
	ipCidrStr, _ := json.Marshal(rule.IPCidr)
	sourceIpCidrStr, _ := json.Marshal(rule.SourceIPCidr)
	portStr, _ := json.Marshal(rule.Port)
	sourcePortStr, _ := json.Marshal(rule.SourcePort)
	processNameStr, _ := json.Marshal(rule.ProcessName)
	processPathStr, _ := json.Marshal(rule.ProcessPath)
	protocolStr, _ := json.Marshal(rule.Protocol)
	includeNamesStr, _ := json.Marshal(rule.IncludeNames)
	excludeNamesStr, _ := json.Marshal(rule.ExcludeNames)

	_, err := s.db.Exec(`
		UPDATE rules SET
			name = ?, priority = ?, enabled = ?,
			inbound = ?, domain = ?, domain_suffix = ?, domain_keyword = ?, domain_regex = ?,
			geosite = ?, source_geoip = ?, geoip = ?, ip_cidr = ?, source_ip_cidr = ?,
			port = ?, source_port = ?, process_name = ?, process_path = ?, protocol = ?,
			outbound_type = ?, group_name = ?, include_names = ?, exclude_names = ?,
			auto_switch = ?, urltest_url = ?, urltest_interval = ?, urltest_interval_unit = ?, urltest_tolerance = ?,
			loadbalance_strategy = ?, loadbalance_hash_key = ?,
			fallback_url = ?, fallback_interval_value = ?, fallback_interval_unit = ?,
			updated_at = ?
		WHERE id = ?
	`,
		rule.Name, rule.Priority, rule.Enabled,
		string(inboundStr), string(domainStr), string(domainSuffixStr), string(domainKeywordStr), string(domainRegexStr),
		string(geositeStr), string(sourceGeoipStr), string(geoipStr), string(ipCidrStr), string(sourceIpCidrStr),
		string(portStr), string(sourcePortStr), string(processNameStr), string(processPathStr), string(protocolStr),
		rule.OutboundType, rule.Group, string(includeNamesStr), string(excludeNamesStr),
		rule.AutoSwitch, rule.UrlTestUrl, rule.UrlTestInterval, rule.UrlTestIntervalUnit, rule.UrltestTolerance,
		rule.LoadbalanceStrategy, rule.LoadbalanceHashKey,
		rule.FallbackURL, rule.FallbackIntervalValue, rule.FallbackIntervalUnit,
		rule.UpdatedAt.Format("2006-01-02 15:04:05"),
		rule.ID,
	)

	if err != nil {
		return fmt.Errorf("failed to update rule: %w", err)
	}

	return nil
}

func (s *SQLiteStorage) DeleteRule(id string) error {
	result, err := s.db.Exec("DELETE FROM rules WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("rule not found: %s", id)
	}

	return nil
}

func (s *SQLiteStorage) GetRule(id string) (*models.Rule, error) {
	row := s.db.QueryRow(`
		SELECT id, name, priority, enabled,
			   inbound, domain, domain_suffix, domain_keyword, domain_regex,
			   geosite, source_geoip, geoip, ip_cidr, source_ip_cidr,
			   port, source_port, process_name, process_path, protocol,
			   outbound_type, group_name, include_names, exclude_names,
			   auto_switch, urltest_url, urltest_interval, urltest_interval_unit, urltest_tolerance,
			   loadbalance_strategy, loadbalance_hash_key,
			   fallback_url, fallback_interval_value, fallback_interval_unit,
			   created_at, updated_at
		FROM rules WHERE id = ?
	`, id)

	var rule models.Rule
	var inboundStr, domainStr, domainSuffixStr, domainKeywordStr, domainRegexStr string
	var geositeStr, sourceGeoipStr, geoipStr, ipCidrStr, sourceIpCidrStr string
	var portStr, sourcePortStr, processNameStr, processPathStr, protocolStr string
	var groupName, includeNamesStr, excludeNamesStr string
	var urltestUrl, urltestIntervalUnit, loadbalanceStrategy, loadbalanceHashKey string
	var fallbackUrl, fallbackIntervalUnit string
	var urltestInterval, urltestTolerance, fallbackIntervalValue sql.NullInt64
	var autoSwitch sql.NullBool
	var createdAt, updatedAt string

	err := row.Scan(
		&rule.ID, &rule.Name, &rule.Priority, &rule.Enabled,
		&inboundStr, &domainStr, &domainSuffixStr, &domainKeywordStr, &domainRegexStr,
		&geositeStr, &sourceGeoipStr, &geoipStr, &ipCidrStr, &sourceIpCidrStr,
		&portStr, &sourcePortStr, &processNameStr, &processPathStr, &protocolStr,
		&rule.OutboundType, &groupName, &includeNamesStr, &excludeNamesStr,
		&autoSwitch, &urltestUrl, &urltestInterval, &urltestIntervalUnit, &urltestTolerance,
		&loadbalanceStrategy, &loadbalanceHashKey,
		&fallbackUrl, &fallbackIntervalValue, &fallbackIntervalUnit,
		&createdAt, &updatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("rule not found: %s", id)
		}
		return nil, fmt.Errorf("failed to scan rule: %w", err)
	}

	// 解析 JSON 字符串数组（与 GetRules 相同的逻辑）
	if inboundStr != "" {
		json.Unmarshal([]byte(inboundStr), &rule.Inbound)
	}
	if domainStr != "" {
		json.Unmarshal([]byte(domainStr), &rule.Domain)
	}
	// ... 其他字段解析逻辑相同

	// 处理可选字段
	rule.Group = groupName
	if autoSwitch.Valid {
		rule.AutoSwitch = autoSwitch.Bool
	}
	// ... 其他字段处理逻辑相同

	// 解析时间
	rule.CreatedAt, _ = time.Parse("2006-01-02 15:04:05", createdAt)
	rule.UpdatedAt, _ = time.Parse("2006-01-02 15:04:05", updatedAt)

	return &rule, nil
}

// 工具函数
func ptrInt(v int) *int { return &v }
