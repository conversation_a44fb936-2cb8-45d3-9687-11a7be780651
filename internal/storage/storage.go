package storage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"singboxui/internal/models"
)

// Storage 数据存储接口
type Storage interface {
	// 入站管理
	GetInbounds() ([]models.Inbound, error)
	GetInbound(id string) (*models.Inbound, error)
	CreateInbound(inbound *models.Inbound) error
	UpdateInbound(inbound *models.Inbound) error
	DeleteInbound(id string) error

	// 节点管理
	GetNodes() ([]models.Node, error)
	GetNodesBySubscription() (map[string][]models.Node, error)
	GetNode(id string) (*models.Node, error)
	CreateNode(node *models.Node) error
	UpdateNode(node *models.Node) error
	DeleteNode(id string) error
	DeleteNodesByGroup(group string) error
	ImportNodes(nodes []models.Node) error
	DeleteAllNodes() error

	// 订阅管理
	GetSubscriptions() ([]models.Subscription, error)
	GetSubscription(id string) (*models.Subscription, error)
	CreateSubscription(sub *models.Subscription) error
	UpdateSubscription(sub *models.Subscription) error
	DeleteSubscription(id string) error

	// 规则管理
	GetRules() ([]models.Rule, error)
	GetRule(id string) (*models.Rule, error)
	CreateRule(rule *models.Rule) error
	UpdateRule(rule *models.Rule) error
	DeleteRule(id string) error

	// 配置管理
	GetConfig() (*models.Config, error)
	SaveConfig(config *models.Config) error
}

// JSONStorage JSON 文件存储实现
type JSONStorage struct {
	filePath string
	mu       sync.RWMutex
	cache    *models.Config
}

// NewJSONStorage 创建 JSON 存储实例
func NewJSONStorage(filePath string) (*JSONStorage, error) {
	storage := &JSONStorage{
		filePath: filePath,
		cache:    &models.Config{},
	}

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory: %w", err)
	}

	// 如果文件不存在，创建默认配置
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		if err := storage.createDefaultConfig(); err != nil {
			return nil, fmt.Errorf("failed to create default config: %w", err)
		}
	}

	// 加载现有配置
	if err := storage.load(); err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	return storage, nil
}

// createDefaultConfig 创建默认配置
func (s *JSONStorage) createDefaultConfig() error {
	now := time.Now()

	// 创建默认的 proxy 入站
	defaultInbound := models.Inbound{
		ID:        "proxy",
		Name:      "Default Proxy",
		Type:      "socks",
		Port:      1080,
		Username:  "",
		Password:  "",
		Settings:  map[string]interface{}{},
		CreatedAt: now,
		UpdatedAt: now,
	}

	s.cache = &models.Config{
		Inbounds:      []models.Inbound{defaultInbound},
		Nodes:         []models.Node{},
		Subscriptions: []models.Subscription{},
	}

	return s.save()
}

// load 从文件加载配置
func (s *JSONStorage) load() error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	data, err := os.ReadFile(s.filePath)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, s.cache)
}

// save 保存配置到文件
func (s *JSONStorage) save() error {
	data, err := json.MarshalIndent(s.cache, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(s.filePath, data, 0644)
}

// GetInbounds 获取所有入站
func (s *JSONStorage) GetInbounds() ([]models.Inbound, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.cache.Inbounds, nil
}

// GetInbound 获取指定入站
func (s *JSONStorage) GetInbound(id string) (*models.Inbound, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for _, inbound := range s.cache.Inbounds {
		if inbound.ID == id {
			return &inbound, nil
		}
	}
	return nil, fmt.Errorf("inbound not found: %s", id)
}

// CreateInbound 创建入站
func (s *JSONStorage) CreateInbound(inbound *models.Inbound) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查是否已存在
	for _, existing := range s.cache.Inbounds {
		if existing.ID == inbound.ID {
			return fmt.Errorf("inbound already exists: %s", inbound.ID)
		}
	}

	inbound.CreatedAt = time.Now()
	inbound.UpdatedAt = time.Now()
	s.cache.Inbounds = append(s.cache.Inbounds, *inbound)

	return s.save()
}

// UpdateInbound 更新入站
func (s *JSONStorage) UpdateInbound(inbound *models.Inbound) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, existing := range s.cache.Inbounds {
		if existing.ID == inbound.ID {
			inbound.UpdatedAt = time.Now()
			s.cache.Inbounds[i] = *inbound
			return s.save()
		}
	}
	return fmt.Errorf("inbound not found: %s", inbound.ID)
}

// DeleteInbound 删除入站
func (s *JSONStorage) DeleteInbound(id string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, inbound := range s.cache.Inbounds {
		if inbound.ID == id {
			// 不允许删除默认的 proxy 入站
			if id == "proxy" {
				return fmt.Errorf("cannot delete default proxy inbound")
			}
			s.cache.Inbounds = append(s.cache.Inbounds[:i], s.cache.Inbounds[i+1:]...)
			return s.save()
		}
	}
	return fmt.Errorf("inbound not found: %s", id)
}

// GetNodes 获取所有节点
func (s *JSONStorage) GetNodes() ([]models.Node, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.cache.Nodes, nil
}

// GetNodesBySubscription 按订阅分组获取节点
func (s *JSONStorage) GetNodesBySubscription() (map[string][]models.Node, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	nodesByGroup := make(map[string][]models.Node)

	for _, node := range s.cache.Nodes {
		group := node.Group
		if group == "" {
			group = "默认分组"
		}
		nodesByGroup[group] = append(nodesByGroup[group], node)
	}

	return nodesByGroup, nil
}

// GetNode 获取指定节点
func (s *JSONStorage) GetNode(id string) (*models.Node, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for _, node := range s.cache.Nodes {
		if node.ID == id {
			return &node, nil
		}
	}
	return nil, fmt.Errorf("node not found: %s", id)
}

// CreateNode 创建节点
func (s *JSONStorage) CreateNode(node *models.Node) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查是否已存在
	for _, existing := range s.cache.Nodes {
		if existing.ID == node.ID {
			return fmt.Errorf("node already exists: %s", node.ID)
		}
	}

	node.CreatedAt = time.Now()
	node.UpdatedAt = time.Now()
	s.cache.Nodes = append(s.cache.Nodes, *node)

	return s.save()
}

// UpdateNode 更新节点
func (s *JSONStorage) UpdateNode(node *models.Node) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, existing := range s.cache.Nodes {
		if existing.ID == node.ID {
			node.UpdatedAt = time.Now()
			s.cache.Nodes[i] = *node
			return s.save()
		}
	}
	return fmt.Errorf("node not found: %s", node.ID)
}

// DeleteNode 删除节点
func (s *JSONStorage) DeleteNode(id string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, node := range s.cache.Nodes {
		if node.ID == id {
			s.cache.Nodes = append(s.cache.Nodes[:i], s.cache.Nodes[i+1:]...)
			return s.save()
		}
	}
	return fmt.Errorf("node not found: %s", id)
}

// DeleteNodesByGroup 删除指定分组的节点
func (s *JSONStorage) DeleteNodesByGroup(group string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	var remainingNodes []models.Node
	for _, node := range s.cache.Nodes {
		if node.Group != group {
			remainingNodes = append(remainingNodes, node)
		}
	}

	s.cache.Nodes = remainingNodes
	return s.save()
}

// ImportNodes 批量导入节点
func (s *JSONStorage) ImportNodes(nodes []models.Node) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	for i := range nodes {
		nodes[i].CreatedAt = now
		nodes[i].UpdatedAt = now
	}

	s.cache.Nodes = append(s.cache.Nodes, nodes...)
	return s.save()
}

// GetSubscriptions 获取所有订阅
func (s *JSONStorage) GetSubscriptions() ([]models.Subscription, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.cache.Subscriptions, nil
}

// GetSubscription 获取指定订阅
func (s *JSONStorage) GetSubscription(id string) (*models.Subscription, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for _, sub := range s.cache.Subscriptions {
		if sub.ID == id {
			return &sub, nil
		}
	}
	return nil, fmt.Errorf("subscription not found: %s", id)
}

// CreateSubscription 创建订阅
func (s *JSONStorage) CreateSubscription(sub *models.Subscription) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查是否已存在
	for _, existing := range s.cache.Subscriptions {
		if existing.ID == sub.ID {
			return fmt.Errorf("subscription already exists: %s", sub.ID)
		}
	}

	sub.CreatedAt = time.Now()
	sub.UpdatedAt = time.Now()
	s.cache.Subscriptions = append(s.cache.Subscriptions, *sub)

	return s.save()
}

// UpdateSubscription 更新订阅
func (s *JSONStorage) UpdateSubscription(sub *models.Subscription) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, existing := range s.cache.Subscriptions {
		if existing.ID == sub.ID {
			sub.UpdatedAt = time.Now()
			s.cache.Subscriptions[i] = *sub
			return s.save()
		}
	}
	return fmt.Errorf("subscription not found: %s", sub.ID)
}

// DeleteSubscription 删除订阅
func (s *JSONStorage) DeleteSubscription(id string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for i, sub := range s.cache.Subscriptions {
		if sub.ID == id {
			s.cache.Subscriptions = append(s.cache.Subscriptions[:i], s.cache.Subscriptions[i+1:]...)
			return s.save()
		}
	}
	return fmt.Errorf("subscription not found: %s", id)
}

// GetConfig 获取完整配置
func (s *JSONStorage) GetConfig() (*models.Config, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.cache, nil
}

// SaveConfig 保存完整配置
func (s *JSONStorage) SaveConfig(config *models.Config) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.cache = config
	return s.save()
}

// DeleteAllNodes 删除所有节点
func (s *JSONStorage) DeleteAllNodes() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.cache.Nodes = []models.Node{}
	return s.save()
}

// 规则管理方法（JSONStorage 暂时返回空实现）
func (s *JSONStorage) GetRules() ([]models.Rule, error) {
	return []models.Rule{}, nil
}

func (s *JSONStorage) GetRule(id string) (*models.Rule, error) {
	return nil, fmt.Errorf("rule not found: %s", id)
}

func (s *JSONStorage) CreateRule(rule *models.Rule) error {
	return fmt.Errorf("rule management not implemented in JSONStorage")
}

func (s *JSONStorage) UpdateRule(rule *models.Rule) error {
	return fmt.Errorf("rule management not implemented in JSONStorage")
}

func (s *JSONStorage) DeleteRule(id string) error {
	return fmt.Errorf("rule management not implemented in JSONStorage")
}
