package models

import (
	"time"
)

// Rule 路由规则配置
type Rule struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Priority int    `json:"priority"` // 规则优先级
	Enabled  bool   `json:"enabled"`  // 是否启用

	// 匹配条件
	Inbound       []string `json:"inbound,omitempty"`        // 入站标签
	Domain        []string `json:"domain,omitempty"`         // 域名匹配
	DomainSuffix  []string `json:"domain_suffix,omitempty"`  // 域名后缀
	DomainKeyword []string `json:"domain_keyword,omitempty"` // 域名关键词
	DomainRegex   []string `json:"domain_regex,omitempty"`   // 域名正则
	Geosite       []string `json:"geosite,omitempty"`        // GeoSite
	SourceGeoIP   []string `json:"source_geoip,omitempty"`   // 源IP地理位置
	Geoip         []string `json:"geoip,omitempty"`          // 目标IP地理位置
	IPCidr        []string `json:"ip_cidr,omitempty"`        // IP段
	SourceIPCidr  []string `json:"source_ip_cidr,omitempty"` // 源IP段
	Port          []string `json:"port,omitempty"`           // 端口
	SourcePort    []string `json:"source_port,omitempty"`    // 源端口
	ProcessName   []string `json:"process_name,omitempty"`   // 进程名
	ProcessPath   []string `json:"process_path,omitempty"`   // 进程路径
	Protocol      []string `json:"protocol,omitempty"`       // 协议

	// 出站配置
	OutboundType string   `json:"outbound_type"`           // 出站类型: selector, urltest, loadbalance, fallback, direct, block
	Group        string   `json:"group,omitempty"`         // 节点分组
	IncludeNames []string `json:"include_names,omitempty"` // 包含节点名称
	ExcludeNames []string `json:"exclude_names,omitempty"` // 排除节点名称

	// URLTest 配置
	AutoSwitch          bool   `json:"auto_switch,omitempty"`
	UrlTestUrl          string `json:"urltest_url,omitempty"`
	UrlTestInterval     int    `json:"urltest_interval,omitempty"`
	UrlTestIntervalUnit string `json:"urltest_interval_unit,omitempty"`
	UrltestTolerance    int    `json:"urltest_tolerance,omitempty"`

	// LoadBalance 配置
	LoadbalanceStrategy string `json:"loadbalance_strategy,omitempty"`
	LoadbalanceHashKey  string `json:"loadbalance_hash_key,omitempty"`

	// Fallback 配置
	FallbackURL           string `json:"fallback_url,omitempty"`
	FallbackIntervalValue int    `json:"fallback_interval_value,omitempty"`
	FallbackIntervalUnit  string `json:"fallback_interval_unit,omitempty"`

	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	Extra     map[string]interface{} `json:"extra,omitempty"`
}

// Inbound 入站配置
type Inbound struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Type         string                 `json:"type"` // proxy, vmess, vless, trojan
	Port         int                    `json:"port"`
	Username     string                 `json:"username,omitempty"`      // 用户名
	Password     string                 `json:"password,omitempty"`      // 密码
	Group        string                 `json:"group,omitempty"`         // 分组
	IncludeNames []string               `json:"include_names,omitempty"` // 包含名称
	ExcludeNames []string               `json:"exclude_names,omitempty"` // 排除名称
	Settings     map[string]interface{} `json:"settings"`
	Sniffing     *Sniffing              `json:"sniffing,omitempty"`

	// 配置方式
	ConfigType string `json:"config_type,omitempty"` // 配置方式: selector, urltest, loadbalance, fallback, direct, block

	// URLTest 配置 (保留原有字段兼容性)
	AutoSwitch          bool   `json:"auto_switch,omitempty"`
	SwitchDelay         int    `json:"switch_delay,omitempty"`
	SwitchDelayUnit     string `json:"switch_delay_unit,omitempty"`
	UrlTestUrl          string `json:"urltest_url,omitempty"`
	UrlTestInterval     int    `json:"urltest_interval,omitempty"`
	UrlTestIntervalUnit string `json:"urltest_interval_unit,omitempty"`
	UrlTestIntervalStr  string `json:"urltest_interval_str,omitempty"`
	UrltestTolerance    int    `json:"urltest_tolerance,omitempty"` // 容忍延迟

	// LoadBalance 配置
	LoadbalanceStrategy string `json:"loadbalance_strategy,omitempty"` // 负载均衡算法
	LoadbalanceHashKey  string `json:"loadbalance_hash_key,omitempty"` // 哈希键

	// Fallback 配置
	FallbackURL           string    `json:"fallback_url,omitempty"`            // 健康检查URL
	FallbackIntervalValue int       `json:"fallback_interval_value,omitempty"` // 检查间隔值
	FallbackIntervalUnit  string    `json:"fallback_interval_unit,omitempty"`  // 检查间隔单位
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

// Node 节点信息
type Node struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	Type       string    `json:"type"` // vmess, vless, trojan, ss
	Address    string    `json:"address"`
	Port       int       `json:"port"`
	UUID       string    `json:"uuid,omitempty"`
	Password   string    `json:"password,omitempty"`
	Security   string    `json:"security,omitempty"`
	Network    string    `json:"network,omitempty"`
	OutboundID string    `json:"outbound_id,omitempty"`
	Group      string    `json:"group,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	// 新增拆分字段
	TlsEnabled              *int   `json:"tls_enabled,omitempty"`
	TlsServerName           string `json:"tls_server_name,omitempty"`
	TlsInsecure             *int   `json:"tls_insecure,omitempty"`
	TransportType           string `json:"transport_type,omitempty"`
	GrpcServiceName         string `json:"grpc_service_name,omitempty"`
	GrpcIdleTimeout         string `json:"grpc_idle_timeout,omitempty"`
	GrpcPingTimeout         string `json:"grpc_ping_timeout,omitempty"`
	GrpcPermitWithoutStream *int   `json:"grpc_permit_without_stream,omitempty"`
	// 兼容旧逻辑
	Tls       map[string]interface{} `json:"tls,omitempty"`
	Transport map[string]interface{} `json:"transport,omitempty"`
	// 新增字段
	Path      string `json:"path,omitempty"`
	ObfsParam string `json:"obfs_param,omitempty"`
	Peer      string `json:"peer,omitempty"`
	// VLESS 流控字段
	Flow string `json:"flow,omitempty"`
	// Shadowsocks 插件字段
	Plugin     string `json:"plugin,omitempty"`
	PluginOpts string `json:"plugin_opts,omitempty"`
}

// Subscription 订阅信息
type Subscription struct {
	ID             string    `json:"id"`
	Name           string    `json:"name"`
	URL            string    `json:"url"`
	NodeCount      int       `json:"nodeCount"`
	LastUpdate     string    `json:"lastUpdate"`
	UpdateInterval int       `json:"updateInterval"`
	Enabled        bool      `json:"enabled"`
	Description    string    `json:"description"`
	Nodes          []Node    `json:"nodes,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// Sniffing 流量探测配置
type Sniffing struct {
	Enabled      bool     `json:"enabled"`
	DestOverride []string `json:"dest_override"`
}

// Mux 多路复用配置
type Mux struct {
	Enabled     bool `json:"enabled"`
	Concurrency int  `json:"concurrency"`
}

// Config 系统配置
type Config struct {
	Inbounds      []Inbound      `json:"inbounds"`
	Nodes         []Node         `json:"nodes"`
	Subscriptions []Subscription `json:"subscriptions"`
}

// SingBoxConfig Sing-box 配置结构
type SingBoxConfig struct {
	Log       *LogConfig               `json:"log,omitempty"`
	DNS       *DNSConfig               `json:"dns,omitempty"`
	Inbounds  []InboundConfig          `json:"inbounds"`
	Outbounds []map[string]interface{} `json:"outbounds"`
	Route     *RouteConfig             `json:"route,omitempty"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level string `json:"level"`
}

// DNSConfig DNS 配置
type DNSConfig struct {
	Servers  []DNSServer `json:"servers,omitempty"`
	Rules    []DNSRule   `json:"rules,omitempty"`
	Final    string      `json:"final,omitempty"`
	Strategy string      `json:"strategy,omitempty"`
}

// DNSServer DNS 服务器配置
type DNSServer struct {
	Tag     string `json:"tag,omitempty"`
	Address string `json:"address"`
	Detour  string `json:"detour,omitempty"`
}

// DNSRule DNS 规则配置
type DNSRule struct {
	Domain []string `json:"domain,omitempty"`
	Server string   `json:"server,omitempty"`
}

// InboundConfig Sing-box 入站配置
type InboundConfig struct {
	Type       string                 `json:"type"`
	Tag        string                 `json:"tag"`
	ListenPort int                    `json:"listen_port,omitempty"`
	Users      interface{}            `json:"users,omitempty"`
	Listen     string                 `json:"listen,omitempty"`
	Settings   map[string]interface{} `json:"settings,omitempty"`
	Sniffing   *Sniffing              `json:"sniffing,omitempty"`
	Extra      map[string]interface{} `json:"-"`
}

// OutboundConfig Sing-box 出站配置
type OutboundConfig struct {
	Type     string                 `json:"type"`
	Tag      string                 `json:"tag"`
	Settings map[string]interface{} `json:"settings,omitempty"`
	Mux      *Mux                   `json:"mux,omitempty"`
	Extra    map[string]interface{} `json:"-"`
}

// RouteConfig Sing-box 路由配置
type RouteConfig struct {
	Rules               []RuleConfig `json:"rules"`
	Final               string       `json:"final"`
	AutoDetectInterface bool         `json:"auto_detect_interface"`
}

// RuleConfig Sing-box 路由规则配置
type RuleConfig struct {
	// 匹配条件
	Inbound       []string `json:"inbound,omitempty"`
	Domain        []string `json:"domain,omitempty"`
	DomainSuffix  []string `json:"domain_suffix,omitempty"`
	DomainKeyword []string `json:"domain_keyword,omitempty"`
	DomainRegex   []string `json:"domain_regex,omitempty"`
	Geosite       []string `json:"geosite,omitempty"`
	SourceGeoIP   []string `json:"source_geoip,omitempty"`
	Geoip         []string `json:"geoip,omitempty"`
	IPCidr        []string `json:"ip_cidr,omitempty"`
	SourceIPCidr  []string `json:"source_ip_cidr,omitempty"`
	Port          []string `json:"port,omitempty"`
	SourcePort    []string `json:"source_port,omitempty"`
	ProcessName   []string `json:"process_name,omitempty"`
	ProcessPath   []string `json:"process_path,omitempty"`
	Protocol      []string `json:"protocol,omitempty"`

	// 出站
	Outbound string `json:"outbound"`
}
