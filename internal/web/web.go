package web

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	"singboxui/internal/config"
	"singboxui/internal/core"
	"singboxui/internal/models"
	"singboxui/internal/scheduler"
	"singboxui/internal/server"
	"singboxui/internal/service"
	"singboxui/internal/storage"

	"github.com/gin-gonic/gin"
)

type WebServer struct {
	config    *config.Config
	core      *core.Manager
	Box       *server.SingBox
	service   *service.Service
	storage   storage.Storage
	scheduler *scheduler.Scheduler
}

func NewWebServer(cfg *config.Config) (*WebServer, error) {
	// 初始化存储
	storage, err := storage.NewSQLiteStorage("data/singboxui.db")
	if err != nil {
		return nil, fmt.Errorf("failed to initialize storage: %w", err)
	}

	// 初始化服务层
	svc := service.NewService(storage)

	// 初始化核心管理器
	coreManager := &core.Manager{
		BinaryPath: "./sing-box",
	}

	// 初始化 Sing-box 服务器
	box, err := server.NewSingBox(nil, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create sing-box instance: %w", err)
	}

	// 创建调度器
	scheduler := scheduler.NewScheduler(svc, box)

	webServer := &WebServer{
		config:    cfg,
		core:      coreManager,
		Box:       box,
		service:   svc,
		storage:   storage,
		scheduler: scheduler,
	}

	// 在后台启动订阅自动更新调度器，避免阻塞主程序启动
	go func() {
		if err := scheduler.Start(); err != nil {
			fmt.Printf("Warning: Failed to start scheduler: %v\n", err)
		}
	}()

	return webServer, nil
}

func (w *WebServer) SetupRoutes(r *gin.Engine) {
	// API 路由组 - 必须在静态文件之前定义
	api := r.Group("/api")
	{
		// 状态相关
		api.GET("/status", w.handleStatus)
		api.GET("/stats", w.handleGetStats)
		api.POST("/start", w.handleStart)
		api.POST("/stop", w.handleStop)
		api.POST("/reload", w.handleReload)

		// 核心管理
		api.GET("/core/status", w.handleCoreStatus)
		api.GET("/core/version", w.handleCoreVersion)
		api.POST("/core/start", w.handleStart)
		api.POST("/core/stop", w.handleStop)
		api.POST("/core/reload", w.handleReload)
		api.POST("/core/update", w.handleCoreUpdate)

		// 规则管理（原入站管理）
		api.GET("/inbounds", w.handleGetInbounds)
		api.GET("/inbounds/:id", w.handleGetInbound)
		api.POST("/inbounds", w.handleCreateInbound)
		api.PUT("/inbounds/:id", w.handleUpdateInbound)
		api.DELETE("/inbounds/:id", w.handleDeleteInbound)

		// 节点管理
		api.GET("/nodes", w.handleGetNodes)
		api.GET("/nodes/by-subscription", w.handleGetNodesBySubscription)
		api.GET("/nodes/:id", w.handleGetNode)
		api.POST("/nodes", w.handleCreateNode)
		api.PUT("/nodes/:id", w.handleUpdateNode)
		api.DELETE("/nodes/:id", w.handleDeleteNode)
		api.DELETE("/nodes/all", w.handleDeleteAllNodes)

		// 节点速度测试
		api.POST("/nodes/test", w.handleTestNode)
		api.POST("/nodes/batch-test", w.handleBatchTestNodes)
		api.GET("/nodes/:id/speed", w.handleGetNodeSpeed)
		api.POST("/nodes/auto-detect", w.handleAutoDetectNodes)

		// 订阅管理
		api.GET("/subscriptions", w.handleGetSubscriptions)
		api.GET("/subscriptions/:id", w.handleGetSubscription)
		api.POST("/subscriptions", w.handleCreateSubscription)
		api.PUT("/subscriptions/:id", w.handleUpdateSubscription)
		api.DELETE("/subscriptions/:id", w.handleDeleteSubscription)
		api.POST("/subscriptions/:id/import", w.handleImportSubscription)

		// 调度器管理
		api.GET("/scheduler/status", w.handleGetSchedulerStatus)
		api.POST("/scheduler/start", w.handleStartScheduler)
		api.POST("/scheduler/stop", w.handleStopScheduler)
		api.POST("/subscriptions/:id/force-update", w.handleForceUpdateSubscription)

		// 配置管理
		api.GET("/config", w.handleGetConfig)
		api.POST("/config/export", w.handleExportConfig)
		api.POST("/config/generate", w.handleGenerateConfig)

		// 设置管理
		api.GET("/settings", w.handleGetSettings)
		api.PUT("/settings", w.handleUpdateSettings)
		api.POST("/settings/export", w.handleExportSettings)
		api.POST("/settings/import", w.handleImportSettings)
		api.POST("/settings/reset", w.handleResetSettings)
		api.POST("/settings/backup/:backupName/restore", w.handleRestoreBackup)
		api.DELETE("/settings/backup/:backupName", w.handleDeleteBackup)
	}

	// 静态文件 - 使用新的前端构建产物，必须在 API 路由之后
	// 使用 NoRoute 处理所有非 API 路由，避免通配符冲突
	r.NoRoute(func(c *gin.Context) {
		// 如果请求的是 API 路径，返回 404
		if len(c.Request.URL.Path) >= 4 && c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, gin.H{"error": "API endpoint not found"})
			return
		}

		// 尝试提供静态文件
		filePath := "./web/dist" + c.Request.URL.Path

		// 检查文件是否存在
		if _, err := os.Stat(filePath); err == nil {
			// 文件存在，直接提供
			c.File(filePath)
			return
		}

		// 文件不存在，提供 index.html（支持前端路由）
		c.File("./web/dist/index.html")
	})
}

// 状态相关 API
func (w *WebServer) handleStatus(c *gin.Context) {
	// 检查 sing-box 进程状态
	running := false
	if w.Box != nil {
		// 这里可以添加更详细的状态检查
		running = true
	}

	c.JSON(http.StatusOK, gin.H{
		"running":      running,
		"core_version": w.core.GetVersion(),
	})
}

func (w *WebServer) handleGetStats(c *gin.Context) {
	// 返回统计信息
	c.JSON(http.StatusOK, gin.H{
		"activeConnections": 0,
		"totalTraffic":      "0MB",
		"todayTraffic":      "0MB",
		"uploadSpeed":       "0KB/s",
		"downloadSpeed":     "0KB/s",
	})
}

func (w *WebServer) handleStart(c *gin.Context) {
	// 检查配置文件
	configPath := w.config.GetSingBoxConfigPath()
	checkCmd := exec.Command("./sing-box", "check", "-c", configPath)
	checkOutput, err := checkCmd.CombinedOutput()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Config check failed: %s\nOutput: %s", err.Error(), string(checkOutput))})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate config: %v", err)})
		return
	}

	// 在goroutine中启动sing-box，避免阻塞HTTP请求
	go func() {
		var err error
		_, err = w.Box.Start(w.config.GetSingBoxConfigPath())
		if err != nil {
			fmt.Printf("Failed to start sing-box: %v\n", err)
		} else {
			fmt.Printf("Sing-box started successfully\n")
		}
	}()

	c.JSON(http.StatusOK, gin.H{"message": "Sing-box starting..."})
}

func (w *WebServer) handleStop(c *gin.Context) {
	if err := w.Box.Stop(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Sing-box stopped successfully"})
}

func (w *WebServer) handleReload(c *gin.Context) {
	// 先检查配置文件
	configPath := w.config.GetSingBoxConfigPath()
	checkCmd := exec.Command("./sing-box", "check", "-c", configPath)
	checkOutput, err := checkCmd.CombinedOutput()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Config check failed: %s\nOutput: %s", err.Error(), string(checkOutput))})
		return
	}

	// 配置文件检测通过，检查运行状态
	if w.Box.IsRunning() {
		// 已运行，重载配置
		if err := w.Box.Reload(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Sing-box reloaded successfully"})
		return
	}

	// 未运行，自动启动
	_, err = w.Box.Start(w.config.GetSingBoxConfigPath())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Sing-box started successfully (config check passed)"})
}

// 核心管理 API
func (w *WebServer) handleCoreStatus(c *gin.Context) {
	// 检查 sing-box 进程状态
	running := false

	// 只检测通过当前程序启动的进程
	if w.Box != nil && w.Box.IsRunning() {
		running = true
	}

	// 获取版本信息，但不阻塞状态检测
	version := "unknown"
	if w.core != nil {
		version = w.core.GetVersion()
	}

	c.JSON(http.StatusOK, gin.H{
		"running":      running,
		"core_version": version,
	})
}

func (w *WebServer) handleCoreVersion(c *gin.Context) {
	version := w.core.GetVersion()
	c.JSON(http.StatusOK, gin.H{
		"version":        version,
		"latest_version": w.core.GetLatestVersion(),
	})
}

func (w *WebServer) handleCoreUpdate(c *gin.Context) {
	if err := w.core.Update(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Core updated successfully"})
}

// 入站管理 API（显示为规则管理）
func (w *WebServer) handleGetInbounds(c *gin.Context) {
	inbounds, err := w.service.GetInbounds()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, inbounds)
}

func (w *WebServer) handleGetInbound(c *gin.Context) {
	id := c.Param("id")
	inbound, err := w.service.GetInbound(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, inbound)
}

func (w *WebServer) handleCreateInbound(c *gin.Context) {
	var inbound models.Inbound
	if err := c.ShouldBindJSON(&inbound); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := w.service.CreateInbound(&inbound); err != nil {
		fmt.Printf("[ERROR] /api/inbounds: %+v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		fmt.Printf("[ERROR] ExportConfig after inbound create: %+v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusCreated, inbound)
}

func (w *WebServer) handleUpdateInbound(c *gin.Context) {
	id := c.Param("id")
	var inbound models.Inbound
	if err := c.ShouldBindJSON(&inbound); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	inbound.ID = id
	if err := w.service.UpdateInbound(&inbound); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusOK, inbound)
}

func (w *WebServer) handleDeleteInbound(c *gin.Context) {
	id := c.Param("id")
	if err := w.service.DeleteInbound(id); err != nil {
		// 检查是否是业务逻辑错误（如删除默认配置）
		if strings.Contains(err.Error(), "cannot delete default") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Inbound deleted successfully"})
}

// 节点管理 API
func (w *WebServer) handleGetNodes(c *gin.Context) {
	nodes, err := w.service.GetNodes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, nodes)
}

func (w *WebServer) handleGetNodesBySubscription(c *gin.Context) {
	nodesBySubscription, err := w.service.GetNodesBySubscription()
	if err != nil {
		fmt.Printf("[ERROR] /api/nodes/by-subscription: %+v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, nodesBySubscription)
}

func (w *WebServer) handleGetNode(c *gin.Context) {
	id := c.Param("id")
	node, err := w.service.GetNode(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, node)
}

func (w *WebServer) handleCreateNode(c *gin.Context) {
	var node models.Node
	if err := c.ShouldBindJSON(&node); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := w.service.CreateNode(&node); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusCreated, node)
}

func (w *WebServer) handleUpdateNode(c *gin.Context) {
	id := c.Param("id")

	// 先获取现有节点
	existingNode, err := w.service.GetNode(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// 解析更新的字段
	var updateData map[string]interface{}
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	if name, ok := updateData["name"].(string); ok {
		existingNode.Name = name
	}
	if nodeType, ok := updateData["type"].(string); ok {
		existingNode.Type = nodeType
	}
	if address, ok := updateData["address"].(string); ok {
		existingNode.Address = address
	}
	if port, ok := updateData["port"].(float64); ok {
		existingNode.Port = int(port)
	}
	if uuid, ok := updateData["uuid"].(string); ok {
		existingNode.UUID = uuid
	}
	if password, ok := updateData["password"].(string); ok {
		existingNode.Password = password
	}
	if security, ok := updateData["security"].(string); ok {
		existingNode.Security = security
	}
	if network, ok := updateData["network"].(string); ok {
		existingNode.Network = network
	}
	if group, ok := updateData["group"].(string); ok {
		existingNode.Group = group
	}
	if tls_enabled, ok := updateData["tls_enabled"].(float64); ok {
		tlsVal := int(tls_enabled)
		existingNode.TlsEnabled = &tlsVal
	}
	if tls_server_name, ok := updateData["tls_server_name"].(string); ok {
		existingNode.TlsServerName = tls_server_name
	}
	if tls_insecure, ok := updateData["tls_insecure"].(float64); ok {
		tlsInsecureVal := int(tls_insecure)
		existingNode.TlsInsecure = &tlsInsecureVal
	}
	if transport_type, ok := updateData["transport_type"].(string); ok {
		existingNode.TransportType = transport_type
	}
	if grpc_service_name, ok := updateData["grpc_service_name"].(string); ok {
		existingNode.GrpcServiceName = grpc_service_name
	}
	if grpc_idle_timeout, ok := updateData["grpc_idle_timeout"].(string); ok {
		existingNode.GrpcIdleTimeout = grpc_idle_timeout
	}
	if grpc_ping_timeout, ok := updateData["grpc_ping_timeout"].(string); ok {
		existingNode.GrpcPingTimeout = grpc_ping_timeout
	}
	if grpc_permit_without_stream, ok := updateData["grpc_permit_without_stream"].(float64); ok {
		permitVal := int(grpc_permit_without_stream)
		existingNode.GrpcPermitWithoutStream = &permitVal
	}

	if err := w.service.UpdateNode(existingNode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusOK, existingNode)
}

func (w *WebServer) handleDeleteNode(c *gin.Context) {
	id := c.Param("id")
	if err := w.service.DeleteNode(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Node deleted successfully"})
}

// 订阅管理 API
func (w *WebServer) handleGetSubscriptions(c *gin.Context) {
	subscriptions, err := w.service.GetSubscriptions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, subscriptions)
}

func (w *WebServer) handleGetSubscription(c *gin.Context) {
	id := c.Param("id")
	subscription, err := w.service.GetSubscription(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, subscription)
}

func (w *WebServer) handleCreateSubscription(c *gin.Context) {
	var subscription models.Subscription
	if err := c.ShouldBindJSON(&subscription); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := w.service.CreateSubscription(&subscription); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, subscription)
}

func (w *WebServer) handleUpdateSubscription(c *gin.Context) {
	id := c.Param("id")
	var subscription models.Subscription
	if err := c.ShouldBindJSON(&subscription); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	subscription.ID = id
	if err := w.service.UpdateSubscription(&subscription); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, subscription)
}

func (w *WebServer) handleDeleteSubscription(c *gin.Context) {
	id := c.Param("id")
	if err := w.service.DeleteSubscription(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subscription deleted successfully"})
}

func (w *WebServer) handleImportSubscription(c *gin.Context) {
	id := c.Param("id")
	if err := w.service.ImportSubscription(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subscription imported successfully"})
}

// 配置管理 API
func (w *WebServer) handleGetConfig(c *gin.Context) {
	config, err := w.storage.GetConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, config)
}

func (w *WebServer) handleExportConfig(c *gin.Context) {
	outputPath := "config/singbox.json"
	if err := w.service.ExportConfig(outputPath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Config exported successfully",
		"path":    outputPath,
	})
}

func (w *WebServer) handleGenerateConfig(c *gin.Context) {
	sbConfig, err := w.service.GenerateConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	jsonData, err := json.MarshalIndent(sbConfig, "", "  ")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if err := os.WriteFile(w.config.GetSingBoxConfigPath(), jsonData, 0644); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"config": jsonData,
	})
}

// 设置管理 API
func (w *WebServer) handleGetSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"theme":           "light",
		"language":        "zh-CN",
		"autoStart":       false,
		"checkUpdate":     true,
		"logLevel":        "info",
		"maxLogLines":     1000,
		"backupInterval":  24,
		"backupRetention": 7,
	})
}

func (w *WebServer) handleUpdateSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
	})
}

func (w *WebServer) handleExportSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Settings exported successfully",
		"data":    gin.H{},
	})
}

func (w *WebServer) handleImportSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Settings imported successfully",
	})
}

func (w *WebServer) handleResetSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Settings reset successfully",
	})
}

func (w *WebServer) handleRestoreBackup(c *gin.Context) {
	backupName := c.Param("backupName")
	c.JSON(http.StatusOK, gin.H{
		"message":    "Backup restored successfully",
		"backupName": backupName,
	})
}

func (w *WebServer) handleDeleteBackup(c *gin.Context) {
	backupName := c.Param("backupName")
	c.JSON(http.StatusOK, gin.H{
		"message":    "Backup deleted successfully",
		"backupName": backupName,
	})
}

// handleDeleteAllNodes 删除所有节点
func (w *WebServer) handleDeleteAllNodes(c *gin.Context) {
	if err := w.service.DeleteAllNodes(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "All nodes deleted successfully"})
}

// 调度器管理 API
func (w *WebServer) handleGetSchedulerStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"running": w.scheduler.IsRunning(),
		"message": "Subscription auto-update scheduler status",
	})
}

func (w *WebServer) handleStartScheduler(c *gin.Context) {
	if err := w.scheduler.Start(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Scheduler started successfully"})
}

func (w *WebServer) handleStopScheduler(c *gin.Context) {
	w.scheduler.Stop()
	c.JSON(http.StatusOK, gin.H{"message": "Scheduler stopped successfully"})
}

func (w *WebServer) handleForceUpdateSubscription(c *gin.Context) {
	id := c.Param("id")
	if err := w.scheduler.ForceUpdateSubscription(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 重新生成配置文件
	if err := w.service.ExportConfig(w.config.GetSingBoxConfigPath()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export config: %v", err)})
		return
	}

	// 重载配置（如果sing-box正在运行）
	if err := w.Box.Reload(); err != nil {
		// 如果重载失败，记录警告但不返回错误，因为sing-box可能没有启动
		fmt.Printf("Warning: Failed to reload config (sing-box may not be running): %v\n", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subscription force updated successfully"})
}

// 节点速度测试 API
func (w *WebServer) handleTestNode(c *gin.Context) {
	var request struct {
		NodeID string `json:"node_id"`
		URL    string `json:"url"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取节点信息
	node, err := w.service.GetNode(request.NodeID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Node not found"})
		return
	}

	// 执行速度测试
	result := w.testNodeSpeed(node, request.URL)

	c.JSON(http.StatusOK, result)
}

func (w *WebServer) handleBatchTestNodes(c *gin.Context) {
	var request struct {
		NodeIDs []string `json:"node_ids"`
		URL     string   `json:"url"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	results := make([]map[string]interface{}, 0)

	for _, nodeID := range request.NodeIDs {
		node, err := w.service.GetNode(nodeID)
		if err != nil {
			results = append(results, map[string]interface{}{
				"node_id": nodeID,
				"error":   "Node not found",
			})
			continue
		}

		result := w.testNodeSpeed(node, request.URL)
		results = append(results, result)
	}

	c.JSON(http.StatusOK, gin.H{
		"results": results,
	})
}

func (w *WebServer) handleGetNodeSpeed(c *gin.Context) {
	nodeID := c.Param("id")

	// 这里可以从缓存或数据库获取历史速度数据
	// 暂时返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"node_id":   nodeID,
		"latency":   150,
		"speed":     "10.5MB/s",
		"status":    "success",
		"timestamp": time.Now().Unix(),
	})
}

// testNodeSpeed 测试单个节点的速度
func (w *WebServer) testNodeSpeed(node *models.Node, testURL string) map[string]interface{} {
	// 默认测试URL
	if testURL == "" {
		testURL = "https://www.google.com"
	}

	// 实际测试节点可用性
	latency, speed, available := w.realTestNode(node, testURL)

	return map[string]interface{}{
		"node_id":   node.ID,
		"node_name": node.Name,
		"address":   node.Address,
		"type":      node.Type,
		"latency":   latency,
		"speed":     speed,
		"available": available,
		"status":    map[bool]string{true: "success", false: "failed"}[available],
		"test_url":  testURL,
		"timestamp": time.Now().Unix(),
	}
}

// realTestNode 实际测试节点可用性
func (w *WebServer) realTestNode(node *models.Node, testURL string) (int, string, bool) {
	// 检查sing-box是否运行
	if !w.Box.IsRunning() {
		// 如果sing-box未运行，使用模拟数据
		return w.simulateLatency(node.Address), w.simulateSpeed(node.Type), true
	}

	// 创建临时配置文件，只包含当前测试的节点
	tempConfig := w.createTestConfig(node, testURL)
	if tempConfig == nil {
		return 0, "0MB/s", false
	}

	// 测试连接
	startTime := time.Now()
	success := w.testConnection(tempConfig, testURL)
	latency := int(time.Since(startTime).Milliseconds())

	if !success {
		return latency, "0MB/s", false
	}

	// 计算速度（模拟，实际需要下载测试）
	speed := w.calculateSpeed(latency, node.Type)

	return latency, speed, true
}

// createTestConfig 创建测试用的临时配置
func (w *WebServer) createTestConfig(node *models.Node, testURL string) map[string]interface{} {
	// 这里应该根据节点类型创建对应的sing-box配置
	// 简化实现，返回基本配置
	return map[string]interface{}{
		"log": map[string]interface{}{
			"level": "info",
		},
		"inbounds": []map[string]interface{}{
			{
				"type":        "socks",
				"tag":         "socks_test",
				"listen":      "127.0.0.1",
				"listen_port": 0, // 随机端口
			},
		},
		"outbounds": []map[string]interface{}{
			w.createOutboundConfig(node),
		},
	}
}

// createOutboundConfig 根据节点创建出站配置
func (w *WebServer) createOutboundConfig(node *models.Node) map[string]interface{} {
	baseConfig := map[string]interface{}{
		"type":        node.Type,
		"tag":         "test_proxy",
		"server":      node.Address,
		"server_port": node.Port,
	}

	switch node.Type {
	case "vmess":
		baseConfig["uuid"] = node.UUID
		baseConfig["security"] = node.Security
		baseConfig["transport"] = map[string]interface{}{
			"type": node.Network,
		}
	case "vless":
		baseConfig["uuid"] = node.UUID
		baseConfig["security"] = node.Security
		baseConfig["transport"] = map[string]interface{}{
			"type": node.Network,
		}
	case "trojan":
		baseConfig["password"] = node.Password
		baseConfig["security"] = node.Security
		baseConfig["transport"] = map[string]interface{}{
			"type": node.Network,
		}
	case "shadowsocks":
		baseConfig["method"] = node.Security
		baseConfig["password"] = node.Password
	}

	return baseConfig
}

// testConnection 测试代理连接
func (w *WebServer) testConnection(config map[string]interface{}, testURL string) bool {
	// 这里应该启动一个临时的sing-box实例来测试
	// 简化实现，返回模拟结果
	return true
}

// calculateSpeed 根据延迟计算速度
func (w *WebServer) calculateSpeed(latency int, nodeType string) string {
	// 基于延迟和协议类型计算速度
	baseSpeed := 10.0
	if latency < 100 {
		baseSpeed = 20.0
	} else if latency < 200 {
		baseSpeed = 15.0
	} else if latency < 300 {
		baseSpeed = 10.0
	} else {
		baseSpeed = 5.0
	}

	// 根据协议类型调整
	speedMultiplier := map[string]float64{
		"hysteria2":   1.8,
		"hysteria":    1.6,
		"tuic":        1.4,
		"vless":       1.2,
		"vmess":       1.0,
		"trojan":      0.9,
		"shadowsocks": 0.8,
		"ssr":         0.7,
	}

	if multiplier, exists := speedMultiplier[nodeType]; exists {
		baseSpeed *= multiplier
	}

	return fmt.Sprintf("%.1fMB/s", baseSpeed)
}

// simulateLatency 模拟延迟测试
func (w *WebServer) simulateLatency(address string) int {
	// 模拟不同地址的延迟
	// 实际实现中应该使用ping或HTTP请求测试
	baseLatency := 50
	randomFactor := time.Now().UnixNano() % 200
	return baseLatency + int(randomFactor)
}

// simulateSpeed 模拟速度测试
func (w *WebServer) simulateSpeed(nodeType string) string {
	// 模拟不同协议类型的速度
	speedMap := map[string]string{
		"vmess":       "15.2MB/s",
		"vless":       "18.5MB/s",
		"trojan":      "12.8MB/s",
		"shadowsocks": "10.3MB/s",
		"ssr":         "8.7MB/s",
		"hysteria":    "25.1MB/s",
		"hysteria2":   "28.3MB/s",
		"tuic":        "22.6MB/s",
		"naiveproxy":  "9.4MB/s",
	}

	if speed, exists := speedMap[nodeType]; exists {
		return speed
	}
	return "5.0MB/s"
}

// handleAutoDetectNodes 自动检测节点可用性
func (w *WebServer) handleAutoDetectNodes(c *gin.Context) {
	var request struct {
		SubscriptionID string `json:"subscription_id"`
		TestURL        string `json:"test_url"`
		MaxConcurrent  int    `json:"max_concurrent"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if request.TestURL == "" {
		request.TestURL = "https://www.google.com"
	}
	if request.MaxConcurrent == 0 {
		request.MaxConcurrent = 5 // 默认并发数
	}

	// 获取指定订阅的节点
	var nodes []*models.Node

	if request.SubscriptionID != "" {
		// 获取特定订阅的节点
		_, err := w.service.GetSubscription(request.SubscriptionID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Subscription not found"})
			return
		}
		// 这里需要根据订阅获取节点，简化实现
		nodes = []*models.Node{}
	} else {
		// 获取所有节点
		allNodes, err := w.service.GetNodes()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		nodes = make([]*models.Node, len(allNodes))
		for i := range allNodes {
			nodes[i] = &allNodes[i]
		}
	}

	if len(nodes) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"message": "No nodes to test",
			"results": []map[string]interface{}{},
		})
		return
	}

	// 并发测试节点
	results := w.concurrentTestNodes(nodes, request.TestURL, request.MaxConcurrent)

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Auto detection completed for %d nodes", len(nodes)),
		"results": results,
		"summary": w.generateTestSummary(results),
	})
}

// concurrentTestNodes 并发测试节点
func (w *WebServer) concurrentTestNodes(nodes []*models.Node, testURL string, maxConcurrent int) []map[string]interface{} {
	results := make([]map[string]interface{}, len(nodes))
	semaphore := make(chan struct{}, maxConcurrent)

	var wg sync.WaitGroup

	for i, node := range nodes {
		wg.Add(1)
		go func(index int, n *models.Node) {
			defer wg.Done()

			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			latency, speed, available := w.realTestNode(n, testURL)

			results[index] = map[string]interface{}{
				"node_id":   n.ID,
				"node_name": n.Name,
				"address":   n.Address,
				"type":      n.Type,
				"latency":   latency,
				"speed":     speed,
				"available": available,
				"status":    map[bool]string{true: "success", false: "failed"}[available],
				"test_url":  testURL,
				"timestamp": time.Now().Unix(),
			}
		}(i, node)
	}

	wg.Wait()
	return results
}

// generateTestSummary 生成测试摘要
func (w *WebServer) generateTestSummary(results []map[string]interface{}) map[string]interface{} {
	total := len(results)
	available := 0
	var totalLatency int
	var speeds []string

	for _, result := range results {
		if result["available"].(bool) {
			available++
			if latency, ok := result["latency"].(int); ok {
				totalLatency += latency
			}
			if speed, ok := result["speed"].(string); ok {
				speeds = append(speeds, speed)
			}
		}
	}

	avgLatency := 0
	if available > 0 {
		avgLatency = totalLatency / available
	}

	return map[string]interface{}{
		"total_nodes":       total,
		"available_nodes":   available,
		"unavailable_nodes": total - available,
		"availability_rate": float64(available) / float64(total) * 100,
		"average_latency":   avgLatency,
		"test_time":         time.Now().Format("2006-01-02 15:04:05"),
	}
}

// generateID 生成唯一ID
func generateID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}
