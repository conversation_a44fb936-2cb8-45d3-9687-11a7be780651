package scheduler

import (
	"fmt"
	"log"
	"sync"
	"time"

	"singboxui/internal/models"
	"singboxui/internal/server"
	"singboxui/internal/service"
)

// Scheduler 定时任务调度器
type Scheduler struct {
	service   *service.Service
	box       *server.SingBox
	timers    map[string]*time.Timer
	mu        sync.RWMutex
	stopChan  chan struct{}
	isRunning bool
}

// NewScheduler 创建新的调度器
func NewScheduler(service *service.Service, box *server.SingBox) *Scheduler {
	return &Scheduler{
		service:   service,
		box:       box,
		timers:    make(map[string]*time.Timer),
		stopChan:  make(chan struct{}),
		isRunning: false,
	}
}

// Start 启动调度器
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("scheduler is already running")
	}

	s.isRunning = true
	log.Println("Starting subscription auto-update scheduler...")

	// 启动订阅更新检查
	go s.startSubscriptionUpdater()

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.stopChan)

	// 停止所有定时器
	for _, timer := range s.timers {
		timer.Stop()
	}
	s.timers = make(map[string]*time.Timer)

	log.Println("Subscription scheduler stopped")
}

// startSubscriptionUpdater 启动订阅更新器
func (s *Scheduler) startSubscriptionUpdater() {
	// 添加启动延迟，避免阻塞主程序启动
	time.Sleep(10 * time.Second)

	ticker := time.NewTicker(1 * time.Hour) // 每小时检查一次
	defer ticker.Stop()

	// 启动时进行一次初始检查，但使用较短的超时
	go func() {
		time.Sleep(30 * time.Second) // 延迟30秒进行首次检查
		s.checkAndUpdateSubscriptions()
	}()

	for {
		select {
		case <-ticker.C:
			s.checkAndUpdateSubscriptions()
		case <-s.stopChan:
			return
		}
	}
}

// checkAndUpdateSubscriptions 检查并更新订阅
func (s *Scheduler) checkAndUpdateSubscriptions() {
	subscriptions, err := s.service.GetSubscriptions()
	if err != nil {
		log.Printf("Failed to get subscriptions: %v", err)
		return
	}

	now := time.Now()

	for _, sub := range subscriptions {
		if !sub.Enabled {
			continue
		}

		// 检查是否需要更新
		if s.shouldUpdateSubscription(sub, now) {
			go s.updateSubscription(sub)
		}
	}
}

// shouldUpdateSubscription 检查订阅是否需要更新
func (s *Scheduler) shouldUpdateSubscription(sub models.Subscription, now time.Time) bool {
	if sub.LastUpdate == "" {
		return true
	}

	lastUpdate, err := time.Parse("2006-01-02 15:04:05", sub.LastUpdate)
	if err != nil {
		log.Printf("Failed to parse last update time for subscription %s: %v", sub.Name, err)
		return true
	}

	// 计算距离上次更新的时间
	hoursSinceLastUpdate := now.Sub(lastUpdate).Hours()

	// 如果超过更新间隔，则需要更新
	return hoursSinceLastUpdate >= float64(sub.UpdateInterval)
}

// updateSubscription 更新单个订阅
func (s *Scheduler) updateSubscription(sub models.Subscription) {
	log.Printf("Updating subscription: %s", sub.Name)

	err := s.service.ImportSubscription(sub.ID)
	if err != nil {
		log.Printf("Failed to update subscription %s: %v", sub.Name, err)
		return
	}

	log.Printf("Successfully updated subscription: %s", sub.Name)

	// 订阅更新成功后，生成配置文件
	log.Printf("Generating configuration file after subscription update: %s", sub.Name)
	if err := s.service.ExportConfig("config/singbox.json"); err != nil {
		log.Printf("Failed to generate config file after subscription update %s: %v", sub.Name, err)
		return
	}

	log.Printf("Configuration file generated successfully for subscription: %s", sub.Name)

	// 配置文件生成成功后，重载配置
	log.Printf("Reloading configuration after subscription update: %s", sub.Name)
	if err := s.reloadConfiguration(); err != nil {
		log.Printf("Failed to reload configuration after subscription update %s: %v", sub.Name, err)
		return
	}

	log.Printf("Configuration reloaded successfully for subscription: %s", sub.Name)
}

// ForceUpdateSubscription 强制更新指定订阅
func (s *Scheduler) ForceUpdateSubscription(subscriptionID string) error {
	sub, err := s.service.GetSubscription(subscriptionID)
	if err != nil {
		return fmt.Errorf("subscription not found: %w", err)
	}

	log.Printf("Force updating subscription: %s", sub.Name)

	// 更新订阅
	if err := s.service.ImportSubscription(sub.ID); err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	log.Printf("Successfully force updated subscription: %s", sub.Name)

	// 订阅更新成功后，生成配置文件
	log.Printf("Generating configuration file after force update: %s", sub.Name)
	if err := s.service.ExportConfig("config/singbox.json"); err != nil {
		return fmt.Errorf("failed to generate config file after force update: %w", err)
	}

	log.Printf("Configuration file generated successfully for force update: %s", sub.Name)

	// 配置文件生成成功后，重载配置
	log.Printf("Reloading configuration after force update: %s", sub.Name)
	if err := s.reloadConfiguration(); err != nil {
		return fmt.Errorf("failed to reload configuration after force update: %w", err)
	}

	log.Printf("Configuration reloaded successfully for force update: %s", sub.Name)

	return nil
}

// GetNextUpdateTime 获取订阅的下次更新时间
func (s *Scheduler) GetNextUpdateTime(sub models.Subscription) (time.Time, error) {
	if sub.LastUpdate == "" {
		return time.Now(), nil
	}

	lastUpdate, err := time.Parse("2006-01-02 15:04:05", sub.LastUpdate)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse last update time: %w", err)
	}

	return lastUpdate.Add(time.Duration(sub.UpdateInterval) * time.Hour), nil
}

// IsRunning 检查调度器是否正在运行
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// reloadConfiguration 重载配置
func (s *Scheduler) reloadConfiguration() error {
	if s.box == nil {
		return fmt.Errorf("sing-box instance not available")
	}

	// 检查 sing-box 是否在运行
	if !s.box.IsRunning() {
		log.Printf("Sing-box is not running, skipping configuration reload")
		return nil
	}

	// 重载配置
	if err := s.box.Reload(); err != nil {
		return fmt.Errorf("failed to reload configuration: %w", err)
	}

	return nil
}
