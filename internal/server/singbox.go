package server

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"singboxui/internal/config"
	"singboxui/internal/core"
)

type SingBox struct {
	cmd     *exec.Cmd
	config  *config.Config
	ctx     context.Context
	cancel  context.CancelFunc
	mu      sync.Mutex
	core    *core.Manager
	pid     int    // 记录 sing-box 进程 PID
	pidFile string // PID 文件路径
}

func NewSingBox(ctx context.Context, cfg *config.Config) (*SingBox, error) {
	// 初始化核心管理器
	coreManager := &core.Manager{
		BinaryPath: "./sing-box",
	}

	// 检查 sing-box 二进制文件是否存在，但不自动下载
	if _, err := os.Stat(coreManager.BinaryPath); os.IsNotExist(err) {
		fmt.Println("sing-box binary not found. Please download it manually or use the web interface to update.")
	}

	pidFile := "./singbox.pid"

	// 启动时检查 PID 文件
	if _, err := os.Stat(pidFile); err == nil {
		if data, err := ioutil.ReadFile(pidFile); err == nil {
			if pid, err := strconv.Atoi(string(data)); err == nil && pid > 0 {
				// 尝试 kill 旧进程
				if process, err := os.FindProcess(pid); err == nil {
					process.Kill()
				}
			}
		}
		os.Remove(pidFile)
	}

	box := &SingBox{
		config:  cfg,
		core:    coreManager,
		pidFile: pidFile,
	}
	return box, nil
}

func (s *SingBox) Start(configPath string) (int, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	fmt.Printf("=== SingBox Start() called ===\n")

	// 检查 sing-box 二进制文件是否存在
	if _, err := os.Stat(s.core.BinaryPath); os.IsNotExist(err) {
		fmt.Printf("ERROR: sing-box binary not found at %s\n", s.core.BinaryPath)
		return 0, fmt.Errorf("sing-box binary not found. Please download it first using the web interface")
	}
	fmt.Printf("✓ sing-box binary found at %s\n", s.core.BinaryPath)

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("ERROR: sing-box config file not found at %s\n", configPath)
		return 0, fmt.Errorf("sing-box config file not found: %s", configPath)
	}
	fmt.Printf("✓ sing-box config file found at %s\n", configPath)

	// 检查是否已经在运行
	if s.cmd != nil && s.cmd.Process != nil {
		// 检查进程是否真的还在运行
		if err := s.cmd.Process.Signal(os.Signal(nil)); err == nil {
			fmt.Printf("ERROR: sing-box is already running\n")
			return 0, fmt.Errorf("sing-box is already running")
		}
		// 进程不存在，清理状态
		s.cmd = nil
	}

	// 创建新的context，当主程序退出时，这个context会被取消
	if s.cancel != nil {
		s.cancel()
	}
	s.ctx, s.cancel = context.WithCancel(context.Background())

	// 打印当前工作目录
	currentDir, _ := os.Getwd()
	fmt.Printf("Current working directory: %s\n", currentDir)

	// 使用 CommandContext 创建命令，当 context 被取消时，子进程会自动被杀死
	s.cmd = exec.CommandContext(s.ctx, "./sing-box", "run", "-c", configPath)
	s.cmd.Stdout = os.Stdout
	s.cmd.Stderr = os.Stderr
	// 设置进程组，便于后续 kill 整个进程组
	s.cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}

	fmt.Printf("Starting sing-box with config: %s\n", configPath)

	// 启动进程
	err := s.cmd.Start()
	if err != nil {
		fmt.Printf("ERROR: Failed to start sing-box: %v\n", err)
		s.cmd = nil
		return 0, fmt.Errorf("failed to start sing-box: %w", err)
	}
	fmt.Printf("✓ Sing-box started with PID: %d\n", s.cmd.Process.Pid)
	s.pid = s.cmd.Process.Pid // 记录 PID

	// 写入 PID 文件
	ioutil.WriteFile(s.pidFile, []byte(strconv.Itoa(s.pid)), 0644)

	// 在后台等待进程结束
	go s.waitForProcess()

	return s.cmd.Process.Pid, nil
}

// waitForProcess 安全地等待进程结束
func (s *SingBox) waitForProcess() {
	// 获取进程ID，这样即使cmd被清理也能跟踪进程
	var pid int
	s.mu.Lock()
	if s.cmd != nil && s.cmd.Process != nil {
		pid = s.cmd.Process.Pid
	} else {
		s.mu.Unlock()
		return
	}
	s.mu.Unlock()

	// 等待进程结束
	process, err := os.FindProcess(pid)
	if err != nil {
		fmt.Printf("Failed to find process %d: %v\n", pid, err)
		return
	}

	// 等待进程结束
	_, err = process.Wait()
	if err != nil {
		fmt.Printf("Sing-box process %d exited with error: %v\n", pid, err)
	} else {
		fmt.Printf("Sing-box process %d exited normally\n", pid)
	}

	// 清理状态
	s.mu.Lock()
	s.cmd = nil
	s.mu.Unlock()
}

func (s *SingBox) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	fmt.Printf("=== SingBox Stop() called ===\n")

	// 取消 context，这会自动杀死子进程
	if s.cancel != nil {
		s.cancel()
	}

	// 如果进程还在运行，强制杀死整个进程组
	if s.cmd != nil && s.cmd.Process != nil {
		pgid, err := syscall.Getpgid(s.cmd.Process.Pid)
		if err == nil {
			// 负号表示 kill 进程组
			syscall.Kill(-pgid, syscall.SIGKILL)
			fmt.Printf("Sing-box process group %d killed.\n", pgid)
		} else {
			fmt.Printf("Failed to get process group ID for PID %d: %v\n", s.cmd.Process.Pid, err)
		}
	}

	// 停止后清理 PID
	s.pid = 0
	// 停止后清理 PID 文件
	os.Remove(s.pidFile)
	return nil
}

func (s *SingBox) Reload() error {
	// sing-box 支持 SIGHUP 热重载
	s.mu.Lock()
	defer s.mu.Unlock()

	// 首先尝试通过当前程序启动的进程
	if s.cmd != nil && s.cmd.Process != nil {
		if err := s.cmd.Process.Signal(syscall.SIGHUP); err == nil {
			return nil
		}
		// 如果发送信号失败，继续尝试其他方法
	}

	// 如果没有通过当前程序启动的进程，尝试查找系统中的 sing-box 进程
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "pgrep", "-f", "sing-box")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("sing-box is not running")
	}

	// 解析 PID
	pids := strings.Split(strings.TrimSpace(string(output)), "\n")
	if len(pids) == 0 || pids[0] == "" {
		return fmt.Errorf("sing-box is not running")
	}

	// 向第一个找到的进程发送 SIGHUP 信号
	pid := strings.TrimSpace(pids[0])
	pidInt, err := strconv.Atoi(pid)
	if err != nil {
		return fmt.Errorf("invalid PID: %s", pid)
	}

	process, err := os.FindProcess(pidInt)
	if err != nil {
		return fmt.Errorf("failed to find process: %w", err)
	}

	return process.Signal(syscall.SIGHUP)
}

func (s *SingBox) IsRunning() bool {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.pid > 0 {
		// 检查该 PID 是否存活
		process, err := os.FindProcess(s.pid)
		if err == nil {
			// 发送 0 信号，不会杀死进程，但能检测是否存在
			if err := process.Signal(syscall.Signal(0)); err == nil {
				return true
			}
		}
		// 进程不存在，清理状态
		s.pid = 0
		s.cmd = nil
	}
	return false
}

func (s *SingBox) Close() {
	s.Stop() // 传入0表示停止所有sing-box进程
}
