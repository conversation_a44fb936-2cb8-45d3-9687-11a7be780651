package server

import (
	"encoding/json"
	"fmt"
	"os"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WebServer struct {
	box *SingBox
}

func StartWebServer(port int, box *SingBox) error {
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("web/templates/*")

	// API路由
	api := r.Group("/api")
	{
		api.GET("/status", getStatus(box))
		api.GET("/config", getConfig(box))
		api.POST("/config", updateConfig(box))
		api.POST("/reload", reloadConfig(box))
		api.GET("/outbounds", getOutbounds(box))
		api.POST("/outbounds", addOutbound(box))
		api.DELETE("/outbounds/:id", deleteOutbound(box))
		api.GET("/core/version", getCoreVersion(box))
		api.POST("/core/update", updateCore(box))
	}

	// 页面路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "SingBox UI",
		})
	})

	addr := fmt.Sprintf(":%d", port)
	fmt.Printf("Web server starting on http://localhost%s\n", addr)
	return r.Run(addr)
}

func getStatus(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 仅判断进程是否存在
		status := "stopped"
		if box != nil && box.cmd != nil && box.cmd.Process != nil {
			status = "running"
		}
		c.JSON(http.StatusOK, gin.H{
			"status": status,
			"uptime": "-", // 可扩展
		})
	}
}

func getConfig(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		configPath := box.config.GetSingBoxConfigPath()
		configData, err := os.ReadFile(configPath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		var config map[string]interface{}
		if err := json.Unmarshal(configData, &config); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, config)
	}
}

func updateConfig(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		var config map[string]interface{}
		if err := c.ShouldBindJSON(&config); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		configData, err := json.MarshalIndent(config, "", "  ")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 保存配置文件
		configPath := box.config.GetSingBoxConfigPath()
		if err := os.WriteFile(configPath, configData, 0644); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 重新加载配置
		if err := box.Reload(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Configuration updated and sing-box reloaded successfully"})
	}
}

func reloadConfig(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := box.Reload(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Sing-box reloaded successfully"})
	}
}

func getOutbounds(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		configPath := box.config.GetSingBoxConfigPath()
		configData, err := os.ReadFile(configPath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		var config map[string]interface{}
		if err := json.Unmarshal(configData, &config); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		outbounds, ok := config["outbounds"].([]interface{})
		if !ok {
			outbounds = []interface{}{}
		}

		c.JSON(http.StatusOK, outbounds)
	}
}

func addOutbound(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		var outbound map[string]interface{}
		if err := c.ShouldBindJSON(&outbound); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// 读取当前配置
		configPath := box.config.GetSingBoxConfigPath()
		configData, err := os.ReadFile(configPath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		var config map[string]interface{}
		if err := json.Unmarshal(configData, &config); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 添加新的出站配置
		outbounds, ok := config["outbounds"].([]interface{})
		if !ok {
			outbounds = []interface{}{}
		}
		outbounds = append(outbounds, outbound)
		config["outbounds"] = outbounds

		// 保存配置
		newConfigData, err := json.MarshalIndent(config, "", "  ")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if err := os.WriteFile(configPath, newConfigData, 0644); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 重新加载配置
		if err := box.Reload(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Outbound added successfully"})
	}
}

func deleteOutbound(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := strconv.Atoi(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid outbound ID"})
			return
		}

		// 读取当前配置
		configPath := box.config.GetSingBoxConfigPath()
		configData, err := os.ReadFile(configPath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		var config map[string]interface{}
		if err := json.Unmarshal(configData, &config); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 删除指定的出站配置
		outbounds, ok := config["outbounds"].([]interface{})
		if !ok || id >= len(outbounds) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Outbound not found"})
			return
		}

		outbounds = append(outbounds[:id], outbounds[id+1:]...)
		config["outbounds"] = outbounds

		// 保存配置
		newConfigData, err := json.MarshalIndent(config, "", "  ")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if err := os.WriteFile(configPath, newConfigData, 0644); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 重新加载配置
		if err := box.Reload(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Outbound deleted successfully"})
	}
}

func getCoreVersion(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		versionInfo := box.core.GetVersionInfo()
		c.JSON(http.StatusOK, versionInfo)
	}
}

func updateCore(box *SingBox) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := box.core.EnsureBinary(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		
		c.JSON(http.StatusOK, gin.H{"message": "Core updated successfully"})
	}
} 