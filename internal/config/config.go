package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

type Config struct {
	Server ServerConfig `mapstructure:"server"`
	SingBox SingBoxConfig `mapstructure:"singbox"`
	DataDir string `mapstructure:"data_dir"`
}

type ServerConfig struct {
	Port int `mapstructure:"port"`
}

type SingBoxConfig struct {
	ConfigPath string `mapstructure:"config_path"`
	BinaryPath string `mapstructure:"binary_path"`
}

func Load(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置默认值
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("data_dir", "./data")
	viper.SetDefault("singbox.config_path", "./config/singbox.json")
	viper.SetDefault("singbox.binary_path", "./sing-box")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 确保数据目录存在
	if err := os.MkdirAll(config.DataDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	return &config, nil
}

func (c *Config) GetSingBoxConfigPath() string {
	if filepath.IsAbs(c.SingBox.ConfigPath) {
		return c.SingBox.ConfigPath
	}
	return filepath.Join(".", c.SingBox.ConfigPath)
} 