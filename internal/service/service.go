package service

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"singboxui/internal/models"
	"singboxui/internal/storage"
)

// Service 业务服务层
type Service struct {
	storage storage.Storage
}

// NewService 创建服务实例
func NewService(storage storage.Storage) *Service {
	return &Service{
		storage: storage,
	}
}

// InboundService 入站服务
func (s *Service) GetInbounds() ([]models.Inbound, error) {
	return s.storage.GetInbounds()
}

func (s *Service) GetInbound(id string) (*models.Inbound, error) {
	return s.storage.GetInbound(id)
}

func (s *Service) CreateInbound(inbound *models.Inbound) error {
	// 字段校验
	if inbound.Name == "" {
		return fmt.Errorf("name不能为空")
	}
	if inbound.Type == "" {
		return fmt.Errorf("type不能为空")
	}
	if inbound.Port <= 0 {
		return fmt.Errorf("port必须大于0")
	}

	// 生成唯一ID
	if inbound.ID == "" {
		inbound.ID = generateID()
	}

	// 标签可以为空，不设置默认值

	return s.storage.CreateInbound(inbound)
}

func (s *Service) UpdateInbound(inbound *models.Inbound) error {
	// 字段校验
	if inbound.Name == "" {
		return fmt.Errorf("name不能为空")
	}
	if inbound.Type == "" {
		return fmt.Errorf("type不能为空")
	}
	if inbound.Port <= 0 {
		return fmt.Errorf("port必须大于0")
	}

	return s.storage.UpdateInbound(inbound)
}

func (s *Service) DeleteInbound(id string) error {
	return s.storage.DeleteInbound(id)
}

// NodeService 节点服务
func (s *Service) GetNodes() ([]models.Node, error) {
	return s.storage.GetNodes()
}

func (s *Service) GetNodesBySubscription() (map[string][]models.Node, error) {
	return s.storage.GetNodesBySubscription()
}

func (s *Service) GetNode(id string) (*models.Node, error) {
	return s.storage.GetNode(id)
}

func (s *Service) CreateNode(node *models.Node) error {
	// 生成唯一ID
	if node.ID == "" {
		node.ID = generateID()
	}

	// 设置出站ID（暂时不创建实际的出站配置）
	node.OutboundID = node.ID + "_outbound"

	// 暂时跳过出站创建，因为 SQLite 存储还没有实现出站管理
	// if err := s.createOutboundForNode(node); err != nil {
	// 	return fmt.Errorf("failed to create outbound for node: %w", err)
	// }

	return s.storage.CreateNode(node)
}

func (s *Service) UpdateNode(node *models.Node) error {
	return s.storage.UpdateNode(node)
}

func (s *Service) DeleteNode(id string) error {
	return s.storage.DeleteNode(id)
}

// SubscriptionService 订阅服务
func (s *Service) GetSubscriptions() ([]models.Subscription, error) {
	return s.storage.GetSubscriptions()
}

func (s *Service) GetSubscription(id string) (*models.Subscription, error) {
	return s.storage.GetSubscription(id)
}

func (s *Service) CreateSubscription(sub *models.Subscription) error {
	// 生成唯一ID
	if sub.ID == "" {
		sub.ID = generateID()
	}

	// 设置默认值
	if sub.NodeCount == 0 {
		sub.NodeCount = 0
	}
	if sub.LastUpdate == "" {
		sub.LastUpdate = time.Now().Format("2006-01-02 15:04:05")
	}
	if sub.UpdateInterval == 0 {
		sub.UpdateInterval = 24
	}
	if !sub.Enabled {
		sub.Enabled = true
	}

	return s.storage.CreateSubscription(sub)
}

func (s *Service) UpdateSubscription(sub *models.Subscription) error {
	return s.storage.UpdateSubscription(sub)
}

func (s *Service) DeleteSubscription(id string) error {
	return s.storage.DeleteSubscription(id)
}

// ImportSubscription 导入订阅
func (s *Service) ImportSubscription(subID string) error {
	sub, err := s.storage.GetSubscription(subID)
	if err != nil {
		return fmt.Errorf("subscription not found: %w", err)
	}

	// 下载订阅内容
	content, err := s.downloadSubscription(sub.URL)
	if err != nil {
		return fmt.Errorf("failed to download subscription: %w", err)
	}

	// 解析节点
	nodes, err := s.parseSubscription(content)
	if err != nil {
		return fmt.Errorf("failed to parse subscription: %w", err)
	}

	// 如果解析成功，先删除该订阅的旧节点
	if err := s.storage.DeleteNodesByGroup(sub.Name); err != nil {
		// 如果删除失败，记录错误但不中断导入过程
		fmt.Printf("Warning: failed to delete old nodes for subscription %s: %v\n", sub.Name, err)
	}

	// 为所有节点设置订阅名称作为分组
	for i := range nodes {
		nodes[i].Group = sub.Name
	}

	// 导入新节点
	if err := s.storage.ImportNodes(nodes); err != nil {
		return fmt.Errorf("failed to import nodes: %w", err)
	}

	// 更新订阅信息
	sub.LastUpdate = time.Now().Format("2006-01-02 15:04:05")
	sub.NodeCount = len(nodes)
	sub.Nodes = nodes
	return s.storage.UpdateSubscription(sub)
}

// ConfigService 配置服务
func (s *Service) GenerateConfig() (*models.SingBoxConfig, error) {
	fmt.Println("GenerateConfig: Starting config generation...")

	// 获取入站配置
	fmt.Println("GenerateConfig: Getting inbounds...")
	inbounds, err := s.storage.GetInbounds()
	if err != nil {
		fmt.Printf("GenerateConfig: Failed to get inbounds: %v\n", err)
		return nil, fmt.Errorf("failed to get inbounds: %w", err)
	}
	fmt.Printf("GenerateConfig: Got %d inbounds\n", len(inbounds))

	// 获取所有节点
	fmt.Println("GenerateConfig: Getting nodes...")
	nodes, err := s.storage.GetNodes()
	if err != nil {
		fmt.Printf("GenerateConfig: Failed to get nodes: %v\n", err)
		return nil, fmt.Errorf("failed to get nodes: %w", err)
	}
	fmt.Printf("GenerateConfig: Got %d nodes\n", len(nodes))

	// 创建sing-box配置
	sbConfig := &models.SingBoxConfig{
		Log: &models.LogConfig{
			Level: "info",
		},
		DNS: &models.DNSConfig{
			Servers: []models.DNSServer{
				{
					Tag:     "dns_proxy",
					Address: "*******",
				},
				{
					Tag:     "dns_direct",
					Address: "*********",
				},
			},
			Rules: []models.DNSRule{
				{
					Domain: []string{"geosite:cn"},
					Server: "dns_direct",
				},
			},
			Final:    "dns_proxy",
			Strategy: "prefer_ipv4",
		},
		Inbounds:  make([]models.InboundConfig, 0),
		Outbounds: make([]map[string]interface{}, 0),
		Route: &models.RouteConfig{
			Rules:               make([]models.RuleConfig, 0),
			Final:               "direct",
			AutoDetectInterface: true,
		},
	}

	// 转换入站配置
	for _, inbound := range inbounds {
		// 根据分组、包含名称、排除名称过滤节点
		filteredNodes := s.filterNodesByInbound(nodes, inbound)

		if len(filteredNodes) == 0 {
			continue
		}

		inboundTag := fmt.Sprintf("%s_%d", inbound.Type, inbound.Port)

		// 新增：生成 InboundConfig 并加入 sbConfig.Inbounds
		inboundConfig := models.InboundConfig{
			Type:       inbound.Type,
			Tag:        inboundTag,
			Listen:     "127.0.0.1", // 添加监听地址
			ListenPort: inbound.Port,
			Settings:   inbound.Settings,
			Sniffing:   inbound.Sniffing,
		}

		// 添加用户认证配置（如果有用户名和密码）
		if inbound.Username != "" && inbound.Password != "" {
			users := []map[string]interface{}{
				{
					"username": inbound.Username,
					"password": inbound.Password,
				},
			}
			inboundConfig.Users = users
		}
		sbConfig.Inbounds = append(sbConfig.Inbounds, inboundConfig)

		var nodeTags []string

		// 为每个节点创建代理出站
		for _, node := range filteredNodes {
			proxyOutbound := s.createProxyOutbound(node, inboundTag)
			sbConfig.Outbounds = append(sbConfig.Outbounds, proxyOutbound)
			nodeTags = append(nodeTags, proxyOutbound["tag"].(string))
		}

		// 根据配置类型生成不同的出站配置
		var finalOutboundTag string

		switch inbound.ConfigType {
		case "urltest":
			// URLTest 配置
			finalOutboundTag = s.createURLTestOutbound(sbConfig, inboundTag, nodeTags, inbound)
		case "loadbalance":
			// LoadBalance 配置
			finalOutboundTag = s.createLoadBalanceOutbound(sbConfig, inboundTag, nodeTags, inbound)
		case "fallback":
			// Fallback 配置
			finalOutboundTag = s.createFallbackOutbound(sbConfig, inboundTag, nodeTags, inbound)
		case "direct":
			// Direct 配置
			finalOutboundTag = s.createDirectOutbound(sbConfig, inboundTag)
		case "block":
			// Block 配置
			finalOutboundTag = s.createBlockOutbound(sbConfig, inboundTag)
		default:
			// Selector 配置（默认，保持兼容性）
			finalOutboundTag = s.createSelectorOutbound(sbConfig, inboundTag, nodeTags, inbound)
		}

		// 路由规则指向最终出站
		if finalOutboundTag != "" {
			routeRule := models.RuleConfig{
				Inbound:  []string{inboundTag},
				Outbound: finalOutboundTag,
			}
			sbConfig.Route.Rules = append(sbConfig.Route.Rules, routeRule)
		}
	}

	// 生成规则配置
	fmt.Println("GenerateConfig: Generating rules...")
	if err := s.generateRulesConfig(sbConfig); err != nil {
		fmt.Printf("GenerateConfig: Failed to generate rules: %v\n", err)
		return nil, fmt.Errorf("failed to generate rules: %w", err)
	}
	fmt.Printf("GenerateConfig: Generated %d rules\n", len(sbConfig.Route.Rules))

	// 添加默认的direct出站
	directOutbound := map[string]interface{}{
		"type": "direct",
		"tag":  "direct",
	}
	sbConfig.Outbounds = append(sbConfig.Outbounds, directOutbound)

	return sbConfig, nil
}

// filterNodesByInbound 根据入站配置过滤节点
func (s *Service) filterNodesByInbound(nodes []models.Node, inbound models.Inbound) []models.Node {
	var filteredNodes []models.Node

	for _, node := range nodes {
		// 检查分组匹配
		if inbound.Group != "" && node.Group != inbound.Group {
			continue
		}

		// 检查包含名称
		if len(inbound.IncludeNames) > 0 {
			matched := false
			for _, includeName := range inbound.IncludeNames {
				if strings.Contains(strings.ToLower(node.Name), strings.ToLower(includeName)) {
					matched = true
					break
				}
			}
			if !matched {
				continue
			}
		}

		// 检查排除名称
		if len(inbound.ExcludeNames) > 0 {
			excluded := false
			for _, excludeName := range inbound.ExcludeNames {
				if strings.Contains(strings.ToLower(node.Name), strings.ToLower(excludeName)) {
					excluded = true
					break
				}
			}
			if excluded {
				continue
			}
		}

		filteredNodes = append(filteredNodes, node)
	}

	return filteredNodes
}

// createProxyOutbound 为节点创建代理出站配置
func (s *Service) createProxyOutbound(node models.Node, inboundTag string) map[string]interface{} {
	proxyOutbound := map[string]interface{}{
		"type": node.Type,
		"tag":  inboundTag + "_" + node.ID + "_proxy",
	}

	// 添加通用配置
	proxyOutbound["server"] = node.Address
	proxyOutbound["server_port"] = node.Port

	switch node.Type {
	case "trojan":
		proxyOutbound["password"] = node.Password

		// TLS 配置 - Trojan 默认启用 TLS
		tlsObj := map[string]interface{}{
			"enabled": true,
		}
		if node.TlsServerName != "" {
			tlsObj["server_name"] = node.TlsServerName
		}
		if node.TlsInsecure != nil {
			tlsObj["insecure"] = *node.TlsInsecure == 1
		} else {
			tlsObj["insecure"] = false // 默认安全连接
		}
		proxyOutbound["tls"] = tlsObj

		// 传输层配置
		s.configureTrojanTransport(proxyOutbound, node)

	case "vmess":
		proxyOutbound["uuid"] = node.UUID

		// VMess 安全配置
		if node.Security != "" {
			proxyOutbound["security"] = node.Security
		} else {
			proxyOutbound["security"] = "auto" // 默认自动选择
		}

		// 添加 alter_id（VMess 必需字段）
		proxyOutbound["alter_id"] = 0 // 现代 VMess 通常使用 0

		// 传输层配置
		s.configureVMessTransport(proxyOutbound, node)

	case "vless":
		proxyOutbound["uuid"] = node.UUID

		// VLESS 使用 encryption 而不是 security
		proxyOutbound["encryption"] = "none" // VLESS 默认不加密

		// 流控配置（如果需要）
		if node.Flow != "" {
			proxyOutbound["flow"] = node.Flow
		}

		// 传输层配置
		s.configureVLessTransport(proxyOutbound, node)

	case "shadowsocks":
		proxyOutbound["password"] = node.Password
		proxyOutbound["method"] = node.Security

		// Shadowsocks 特定配置
		if node.Plugin != "" {
			proxyOutbound["plugin"] = node.Plugin
			if node.PluginOpts != "" {
				proxyOutbound["plugin_opts"] = node.PluginOpts
			}
		}
	}

	return proxyOutbound
}

// configureTrojanTransport 配置 Trojan 传输层
func (s *Service) configureTrojanTransport(proxyOutbound map[string]interface{}, node models.Node) {
	if node.TransportType == "grpc" || node.Network == "grpc" {
		grpcObj := map[string]interface{}{
			"type": "grpc",
		}

		// 设置 service_name，如果没有则使用默认值
		if node.GrpcServiceName != "" {
			grpcObj["service_name"] = node.GrpcServiceName
		} else {
			grpcObj["service_name"] = "mygrpc" // 默认服务名
		}

		// 设置超时配置，如果没有则使用默认值
		if node.GrpcIdleTimeout != "" {
			grpcObj["idle_timeout"] = node.GrpcIdleTimeout
		} else {
			grpcObj["idle_timeout"] = "60s" // 默认空闲超时
		}

		if node.GrpcPingTimeout != "" {
			grpcObj["ping_timeout"] = node.GrpcPingTimeout
		} else {
			grpcObj["ping_timeout"] = "20s" // 默认 ping 超时
		}

		// 设置 permit_without_stream，默认为 false
		if node.GrpcPermitWithoutStream != nil {
			grpcObj["permit_without_stream"] = *node.GrpcPermitWithoutStream == 1
		} else {
			grpcObj["permit_without_stream"] = false // 默认值
		}

		proxyOutbound["transport"] = grpcObj
	} else if node.Network == "ws" {
		wsObj := map[string]interface{}{
			"type": "ws",
		}
		if node.Path != "" {
			wsObj["path"] = node.Path
		}
		proxyOutbound["transport"] = wsObj
	} else if node.Transport != nil {
		proxyOutbound["transport"] = node.Transport
	}
}

// configureVMessTransport 配置 VMess 传输层
func (s *Service) configureVMessTransport(proxyOutbound map[string]interface{}, node models.Node) {
	if node.Network == "ws" {
		wsObj := map[string]interface{}{
			"type": "ws",
		}
		if node.Path != "" {
			wsObj["path"] = node.Path
		}
		proxyOutbound["transport"] = wsObj
	} else if node.Network == "grpc" {
		grpcObj := map[string]interface{}{
			"type": "grpc",
		}
		if node.GrpcServiceName != "" {
			grpcObj["service_name"] = node.GrpcServiceName
		}
		proxyOutbound["transport"] = grpcObj
	} else if node.Network == "h2" {
		h2Obj := map[string]interface{}{
			"type": "http",
		}
		if node.Path != "" {
			h2Obj["path"] = node.Path
		}
		proxyOutbound["transport"] = h2Obj
	} else if node.Transport != nil {
		proxyOutbound["transport"] = node.Transport
	}

	// TLS 配置（如果需要）
	if node.TlsEnabled != nil && *node.TlsEnabled == 1 {
		tlsObj := map[string]interface{}{
			"enabled": true,
		}
		if node.TlsServerName != "" {
			tlsObj["server_name"] = node.TlsServerName
		}
		if node.TlsInsecure != nil {
			tlsObj["insecure"] = *node.TlsInsecure == 1
		}
		proxyOutbound["tls"] = tlsObj
	}
}

// configureVLessTransport 配置 VLESS 传输层
func (s *Service) configureVLessTransport(proxyOutbound map[string]interface{}, node models.Node) {
	if node.Network == "ws" {
		wsObj := map[string]interface{}{
			"type": "ws",
		}
		if node.Path != "" {
			wsObj["path"] = node.Path
		}
		proxyOutbound["transport"] = wsObj
	} else if node.Network == "grpc" {
		grpcObj := map[string]interface{}{
			"type": "grpc",
		}
		if node.GrpcServiceName != "" {
			grpcObj["service_name"] = node.GrpcServiceName
		}
		proxyOutbound["transport"] = grpcObj
	} else if node.Network == "h2" {
		h2Obj := map[string]interface{}{
			"type": "http",
		}
		if node.Path != "" {
			h2Obj["path"] = node.Path
		}
		proxyOutbound["transport"] = h2Obj
	} else if node.Transport != nil {
		proxyOutbound["transport"] = node.Transport
	}

	// TLS 配置（如果需要）
	if node.TlsEnabled != nil && *node.TlsEnabled == 1 {
		tlsObj := map[string]interface{}{
			"enabled": true,
		}
		if node.TlsServerName != "" {
			tlsObj["server_name"] = node.TlsServerName
		}
		if node.TlsInsecure != nil {
			tlsObj["insecure"] = *node.TlsInsecure == 1
		}
		proxyOutbound["tls"] = tlsObj
	}
}

func (s *Service) ExportConfig(outputPath string) error {
	fmt.Println("ExportConfig: Starting config generation...")

	sbConfig, err := s.GenerateConfig()
	if err != nil {
		fmt.Printf("ExportConfig: Failed to generate config: %v\n", err)
		return fmt.Errorf("failed to generate config: %w", err)
	}
	fmt.Println("ExportConfig: Config generation completed")

	jsonData, err := json.MarshalIndent(sbConfig, "", "  ")
	if err != nil {
		fmt.Printf("ExportConfig: Failed to convert to JSON: %v\n", err)
		return fmt.Errorf("failed to convert to JSON: %w", err)
	}
	fmt.Println("ExportConfig: JSON conversion completed")

	// 确保目录存在
	dir := filepath.Dir(outputPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		fmt.Printf("ExportConfig: Failed to create directory: %v\n", err)
		return fmt.Errorf("failed to create directory: %w", err)
	}
	fmt.Printf("ExportConfig: Directory created/verified: %s\n", dir)

	err = os.WriteFile(outputPath, jsonData, 0644)
	if err != nil {
		fmt.Printf("ExportConfig: Failed to write file: %v\n", err)
		return fmt.Errorf("failed to write file: %w", err)
	}
	fmt.Printf("ExportConfig: Config file written successfully: %s\n", outputPath)
	return nil
}

// DeleteAllNodes 删除所有节点
func (s *Service) DeleteAllNodes() error {
	return s.storage.DeleteAllNodes()
}

// 辅助方法

// generateID 生成唯一ID
func generateID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}

// downloadSubscription 下载订阅内容
func (s *Service) downloadSubscription(url string) (string, error) {
	// 创建带超时的HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second, // 30秒超时
	}

	resp, err := client.Get(url)
	if err != nil {
		return "", fmt.Errorf("failed to download subscription from %s: %w", url, err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("subscription server returned status %d for URL %s", resp.StatusCode, url)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	content := string(body)

	// 检查是否是 Base64 编码的内容
	if isBase64(content) {
		decoded, err := base64.StdEncoding.DecodeString(content)
		if err != nil {
			return "", fmt.Errorf("failed to decode base64 content: %w", err)
		}
		return string(decoded), nil
	}

	return content, nil
}

// isBase64 检查字符串是否是 Base64 编码
func isBase64(s string) bool {
	// 移除空白字符
	s = strings.TrimSpace(s)

	// 检查是否只包含 Base64 字符
	for _, r := range s {
		if !((r >= 'A' && r <= 'Z') || (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '+' || r == '/' || r == '=') {
			return false
		}
	}

	// 检查长度是否是 4 的倍数
	return len(s)%4 == 0
}

// parseSubscription 解析订阅内容
func (s *Service) parseSubscription(content string) ([]models.Node, error) {
	// 按行分割，每行一个节点
	lines := strings.Split(strings.TrimSpace(content), "\n")
	nodes := make([]models.Node, 0, len(lines))

	fmt.Printf("Total lines in subscription: %d\n", len(lines))

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		node, err := s.parseNodeLine(line)
		if err != nil {
			// 记录错误但继续处理其他行
			fmt.Printf("Failed to parse line %d: %v\n", i+1, err)
			continue
		}
		nodes = append(nodes, node)
	}

	fmt.Printf("Successfully parsed %d nodes\n", len(nodes))

	if len(nodes) == 0 {
		return nil, fmt.Errorf("no valid nodes found in subscription")
	}

	return nodes, nil
}

// parseNodeLine 解析单个节点链接
func (s *Service) parseNodeLine(line string) (models.Node, error) {
	if strings.HasPrefix(line, "ss://") {
		return s.parseShadowsocksLink(line)
	}
	if strings.HasPrefix(line, "ssr://") {
		return s.parseSSRLink(line)
	}
	if strings.HasPrefix(line, "vmess://") {
		return s.parseVMessLink(line)
	}
	if strings.HasPrefix(line, "vless://") {
		return s.parseVLessLink(line)
	}
	if strings.HasPrefix(line, "trojan://") {
		return s.parseTrojanLink(line)
	}
	if strings.HasPrefix(line, "hysteria://") {
		return s.parseHysteriaLink(line)
	}
	if strings.HasPrefix(line, "hysteria2://") {
		return s.parseHysteria2Link(line)
	}
	if strings.HasPrefix(line, "reality://") {
		return s.parseRealityLink(line)
	}
	if strings.HasPrefix(line, "tuic://") {
		return s.parseTuicLink(line)
	}
	if strings.HasPrefix(line, "naive+https://") {
		return s.parseNaiveProxyLink(line)
	}
	return models.Node{}, fmt.Errorf("unsupported protocol: %s", line[:strings.Index(line, "://")+3])
}

// parseShadowsocksLink 解析 Shadowsocks 链接
func (s *Service) parseShadowsocksLink(line string) (models.Node, error) {
	// 移除 ss:// 前缀
	line = strings.TrimPrefix(line, "ss://")

	// 分离 #name
	parts := strings.SplitN(line, "#", 2)
	mainPart := parts[0]
	name := ""
	if len(parts) > 1 {
		decodedName, err := url.QueryUnescape(parts[1])
		if err == nil {
			name = decodedName
		} else {
			name = parts[1]
		}
	}

	// 跳过信息节点（*******:666）
	if strings.Contains(mainPart, "@*******:666") {
		return models.Node{}, fmt.Errorf("skipping info node")
	}

	// 判断新版格式（有@host:port）
	atIdx := strings.LastIndex(mainPart, "@")
	var userinfo, hostport string
	if atIdx > 0 {
		userinfo = mainPart[:atIdx]
		hostport = mainPart[atIdx+1:]
	} else {
		userinfo = mainPart
		hostport = ""
	}

	// base64 解码 userinfo
	var decoded []byte
	var err error
	// 兼容标准和 URL base64
	decoded, err = base64.RawURLEncoding.DecodeString(userinfo)
	if err != nil {
		decoded, err = base64.StdEncoding.DecodeString(userinfo)
		if err != nil {
			return models.Node{}, fmt.Errorf("failed to decode shadowsocks userinfo: %w", err)
		}
	}
	userinfoStr := string(decoded)
	method, password, ok := strings.Cut(userinfoStr, ":")
	if !ok {
		return models.Node{}, fmt.Errorf("invalid userinfo format")
	}

	var address string
	var port int
	if hostport != "" {
		host, portStr, err := net.SplitHostPort(hostport)
		if err != nil {
			return models.Node{}, fmt.Errorf("invalid host:port: %w", err)
		}
		address = host
		port, err = strconv.Atoi(portStr)
		if err != nil {
			return models.Node{}, fmt.Errorf("invalid port: %w", err)
		}
	} else {
		// 兼容老格式: base64(method:password@host:port)
		atIndex := strings.LastIndex(userinfoStr, "@")
		if atIndex == -1 {
			return models.Node{}, fmt.Errorf("invalid shadowsocks link format")
		}
		hostPart := userinfoStr[atIndex+1:]
		host, portStr, err := net.SplitHostPort(hostPart)
		if err != nil {
			return models.Node{}, fmt.Errorf("invalid host:port: %w", err)
		}
		address = host
		port, err = strconv.Atoi(portStr)
		if err != nil {
			return models.Node{}, fmt.Errorf("invalid port: %w", err)
		}
	}

	if name == "" {
		name = fmt.Sprintf("SS-%s:%d", address, port)
	}

	return models.Node{
		ID:       generateID(),
		Name:     name,
		Type:     "shadowsocks",
		Address:  address,
		Port:     port,
		Password: password,
		Security: method,
	}, nil
}

// parseVMessLink 解析 VMess 链接
func (s *Service) parseVMessLink(line string) (models.Node, error) {
	// 移除 vmess:// 前缀
	line = strings.TrimPrefix(line, "vmess://")

	// Base64 解码
	decoded, err := base64.StdEncoding.DecodeString(line)
	if err != nil {
		return models.Node{}, fmt.Errorf("failed to decode vmess link: %w", err)
	}

	// 解析 JSON 格式的 VMess 配置
	var vmessConfig struct {
		V    string      `json:"v"`
		PS   string      `json:"ps"`   // 备注
		Add  string      `json:"add"`  // 服务器地址
		Port interface{} `json:"port"` // 端口（可能是字符串或数字）
		ID   string      `json:"id"`   // UUID
		Aid  interface{} `json:"aid"`  // 额外ID（可能是字符串或数字）
		Net  string      `json:"net"`  // 传输协议
		Type string      `json:"type"`
		TLS  string      `json:"tls"`
	}

	if err := json.Unmarshal(decoded, &vmessConfig); err != nil {
		return models.Node{}, fmt.Errorf("failed to parse vmess config: %w", err)
	}

	// 处理端口字段
	var port int
	switch v := vmessConfig.Port.(type) {
	case float64:
		port = int(v)
	case string:
		if p, err := strconv.Atoi(v); err == nil {
			port = p
		} else {
			return models.Node{}, fmt.Errorf("invalid port format: %s", v)
		}
	default:
		return models.Node{}, fmt.Errorf("unsupported port type: %T", vmessConfig.Port)
	}

	name := vmessConfig.PS
	if name != "" {
		// URL 解码名称
		decodedName, err := url.QueryUnescape(name)
		if err == nil {
			name = decodedName
		}
	}
	if name == "" {
		name = fmt.Sprintf("VMess-%s:%d", vmessConfig.Add, port)
	}

	return models.Node{
		ID:       generateID(),
		Name:     name,
		Type:     "vmess",
		Address:  vmessConfig.Add,
		Port:     port,
		UUID:     vmessConfig.ID,
		Security: "auto",
		Network:  vmessConfig.Net,
	}, nil
}

// parseVLessLink 解析 VLESS 链接
func (s *Service) parseVLessLink(line string) (models.Node, error) {
	// 移除 vless:// 前缀
	line = strings.TrimPrefix(line, "vless://")

	// 分离 UUID 和服务器信息
	parts := strings.SplitN(line, "@", 2)
	if len(parts) != 2 {
		return models.Node{}, fmt.Errorf("invalid vless link format")
	}

	uuid := parts[0]
	serverPart := parts[1]

	// 分离服务器地址和参数
	serverParts := strings.SplitN(serverPart, "?", 2)
	addressPart := serverParts[0]

	// 解析地址和端口
	colonIndex := strings.LastIndex(addressPart, ":")
	if colonIndex == -1 {
		return models.Node{}, fmt.Errorf("invalid vless address format")
	}

	server := addressPart[:colonIndex]
	portStr := addressPart[colonIndex+1:]
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return models.Node{}, fmt.Errorf("invalid port: %w", err)
	}

	name := fmt.Sprintf("VLESS-%s:%d", server, port)

	return models.Node{
		ID:       generateID(),
		Name:     name,
		Type:     "vless",
		Address:  server,
		Port:     port,
		UUID:     uuid,
		Security: "none",
	}, nil
}

// parseTrojanLink 解析 Trojan 链接
func (s *Service) parseTrojanLink(line string) (models.Node, error) {
	// 移除 trojan:// 前缀
	line = strings.TrimPrefix(line, "trojan://")

	// 分离密码和服务器信息
	parts := strings.SplitN(line, "@", 2)
	if len(parts) != 2 {
		return models.Node{}, fmt.Errorf("invalid trojan link format")
	}

	password := parts[0]
	serverPart := parts[1]

	// 分离服务器地址和参数
	serverParts := strings.SplitN(serverPart, "?", 2)
	addressPart := serverParts[0]

	// 解析地址和端口
	colonIndex := strings.LastIndex(addressPart, ":")
	if colonIndex == -1 {
		return models.Node{}, fmt.Errorf("invalid trojan address format")
	}

	server := addressPart[:colonIndex]
	portStr := addressPart[colonIndex+1:]
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return models.Node{}, fmt.Errorf("invalid port: %w", err)
	}

	// 解析URL参数
	params := make(map[string]string)
	if len(serverParts) > 1 {
		for _, kv := range strings.Split(serverParts[1], "&") {
			pair := strings.SplitN(kv, "=", 2)
			if len(pair) == 2 {
				params[pair[0]] = pair[1]
			}
		}
	}

	// 提取节点名称（从#后面的部分）
	name := ""
	if strings.Contains(line, "#") {
		namePart := strings.SplitN(line, "#", 2)[1]
		decodedName, err := url.QueryUnescape(namePart)
		if err == nil {
			name = decodedName
		} else {
			name = namePart
		}
	}

	if name == "" {
		name = fmt.Sprintf("Trojan-%s:%d", server, port)
	}

	// 解析传输协议
	network := "tcp"
	if obfs, ok := params["obfs"]; ok {
		network = obfs
	}

	// 解析安全设置
	security := "tls"
	if tlsParam, ok := params["security"]; ok {
		security = tlsParam
	}

	// 新增：直接赋值到独立字段
	node := models.Node{
		ID:       generateID(),
		Name:     name,
		Type:     "trojan",
		Address:  server,
		Port:     port,
		Password: password,
		Security: security,
		Network:  network,
	}
	if sni, ok := params["sni"]; ok {
		node.TlsEnabled = ptrInt(1)
		node.TlsServerName = sni
	}
	if insecure, ok := params["insecure"]; ok {
		if insecure == "1" || insecure == "true" {
			node.TlsInsecure = ptrInt(1)
		} else {
			node.TlsInsecure = ptrInt(0)
		}
	}
	if t, ok := params["type"]; ok && t == "grpc" {
		node.TransportType = "grpc"
		if svc, ok := params["grpc-service-name"]; ok {
			node.GrpcServiceName = svc
		}
		if idle, ok := params["grpc-idle-timeout"]; ok {
			node.GrpcIdleTimeout = idle
		}
		if ping, ok := params["grpc-ping-timeout"]; ok {
			node.GrpcPingTimeout = ping
		}
		if permit, ok := params["grpc-permit-without-stream"]; ok {
			if permit == "1" || permit == "true" {
				node.GrpcPermitWithoutStream = ptrInt(1)
			} else {
				node.GrpcPermitWithoutStream = ptrInt(0)
			}
		}
	}
	// 新增 path、obfs_param、peer 字段解析
	if path, ok := params["path"]; ok {
		node.Path = path
	}
	if obfsParam, ok := params["obfsParam"]; ok {
		node.ObfsParam = obfsParam
	}
	if peer, ok := params["peer"]; ok {
		node.Peer = peer
	}
	return node, nil
}

// parseSSRLink 解析 ShadowsocksR 链接
func (s *Service) parseSSRLink(line string) (models.Node, error) {
	// ssr:// 后面是 base64 编码
	line = strings.TrimPrefix(line, "ssr://")
	decoded, err := base64.RawURLEncoding.DecodeString(line)
	if err != nil {
		// 有些订阅用标准 base64
		decoded, err = base64.StdEncoding.DecodeString(line)
		if err != nil {
			return models.Node{}, fmt.Errorf("failed to decode ssr link: %w", err)
		}
	}
	// 格式: server:port:protocol:method:obfs:password_base64/?params
	mainAndParams := strings.SplitN(string(decoded), "/?", 2)
	mainParts := strings.Split(mainAndParams[0], ":")
	if len(mainParts) < 6 {
		return models.Node{}, fmt.Errorf("invalid ssr link format")
	}
	server := mainParts[0]
	port, err := strconv.Atoi(mainParts[1])
	if err != nil {
		return models.Node{}, fmt.Errorf("invalid port: %w", err)
	}
	protocol := mainParts[2]
	method := mainParts[3]
	obfs := mainParts[4]
	passwordBase64 := mainParts[5]
	passwordBytes, err := base64.RawURLEncoding.DecodeString(passwordBase64)
	if err != nil {
		passwordBytes, err = base64.StdEncoding.DecodeString(passwordBase64)
		if err != nil {
			return models.Node{}, fmt.Errorf("invalid password base64: %w", err)
		}
	}
	password := string(passwordBytes)
	// 解析 params
	params := make(map[string]string)
	if len(mainAndParams) > 1 {
		for _, kv := range strings.Split(mainAndParams[1], "&") {
			pair := strings.SplitN(kv, "=", 2)
			if len(pair) == 2 {
				params[pair[0]] = pair[1]
			}
		}
	}
	name := server
	if v, ok := params["remarks"]; ok {
		b, err := base64.RawURLEncoding.DecodeString(v)
		if err != nil {
			b, _ = base64.StdEncoding.DecodeString(v)
		}
		if len(b) > 0 {
			name = string(b)
		}
	}
	return models.Node{
		ID:       generateID(),
		Name:     name,
		Type:     "ssr",
		Address:  server,
		Port:     port,
		Password: password,
		Security: method,
		Network:  protocol + "+" + obfs,
	}, nil
}

// parseHysteriaLink 解析 Hysteria v1 链接
func (s *Service) parseHysteriaLink(line string) (models.Node, error) {
	// TODO: 实现 Hysteria v1 协议解析
	return models.Node{}, fmt.Errorf("Hysteria 解析暂未实现")
}

// parseHysteria2Link 解析 Hysteria v2 链接
func (s *Service) parseHysteria2Link(line string) (models.Node, error) {
	// 移除 hysteria2:// 前缀
	line = strings.TrimPrefix(line, "hysteria2://")

	// 分离密码和服务器信息
	parts := strings.SplitN(line, "@", 2)
	if len(parts) != 2 {
		return models.Node{}, fmt.Errorf("invalid hysteria2 link format")
	}

	password := parts[0]
	serverPart := parts[1]

	// 分离服务器地址和参数
	serverParts := strings.SplitN(serverPart, "?", 2)
	addressPart := serverParts[0]

	// 解析地址和端口
	colonIndex := strings.LastIndex(addressPart, ":")
	if colonIndex == -1 {
		return models.Node{}, fmt.Errorf("invalid hysteria2 address format")
	}

	server := addressPart[:colonIndex]
	portStr := addressPart[colonIndex+1:]
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return models.Node{}, fmt.Errorf("invalid port: %w", err)
	}

	// 解析URL参数
	params := make(map[string]string)
	if len(serverParts) > 1 {
		for _, kv := range strings.Split(serverParts[1], "&") {
			pair := strings.SplitN(kv, "=", 2)
			if len(pair) == 2 {
				params[pair[0]] = pair[1]
			}
		}
	}

	// 提取节点名称（从#后面的部分）
	name := ""
	if strings.Contains(line, "#") {
		namePart := strings.SplitN(line, "#", 2)[1]
		decodedName, err := url.QueryUnescape(namePart)
		if err == nil {
			name = decodedName
		} else {
			name = namePart
		}
	}

	if name == "" {
		name = fmt.Sprintf("Hysteria2-%s:%d", server, port)
	}

	return models.Node{
		ID:       generateID(),
		Name:     name,
		Type:     "hysteria2",
		Address:  server,
		Port:     port,
		Password: password,
		Security: "none",
		Network:  "udp",
	}, nil
}

// parseRealityLink 解析 Reality 链接（通常为 vless+reality）
func (s *Service) parseRealityLink(line string) (models.Node, error) {
	// TODO: 实现 Reality 协议解析
	return models.Node{}, fmt.Errorf("Reality 解析暂未实现")
}

// parseTuicLink 解析 Tuic 链接
func (s *Service) parseTuicLink(line string) (models.Node, error) {
	// TODO: 实现 Tuic 协议解析
	return models.Node{}, fmt.Errorf("Tuic 解析暂未实现")
}

// parseNaiveProxyLink 解析 NaiveProxy 链接
func (s *Service) parseNaiveProxyLink(line string) (models.Node, error) {
	// TODO: 实现 NaiveProxy 协议解析
	return models.Node{}, fmt.Errorf("NaiveProxy 解析暂未实现")
}

// 工具函数
func ptrInt(v int) *int { return &v }

// createSelectorOutbound 创建 Selector 出站配置
func (s *Service) createSelectorOutbound(sbConfig *models.SingBoxConfig, inboundTag string, nodeTags []string, inbound models.Inbound) string {
	// 兼容旧的 AutoSwitch 逻辑
	var selectorOutbounds []string

	if inbound.AutoSwitch {
		// 生成 urltest 出站
		urltestTag := inboundTag + "_urltest"
		intervalStr := inbound.UrlTestIntervalStr
		if intervalStr == "" && inbound.UrlTestInterval > 0 && inbound.UrlTestIntervalUnit != "" {
			intervalStr = fmt.Sprintf("%d%s", inbound.UrlTestInterval, inbound.UrlTestIntervalUnit)
		}
		if intervalStr == "" {
			intervalStr = "30s"
		}
		urltestOutbound := map[string]interface{}{
			"type":         "urltest",
			"tag":          urltestTag,
			"outbounds":    nodeTags,
			"url":          inbound.UrlTestUrl,
			"interval":     intervalStr,
			"idle_timeout": intervalStr,
		}

		// 添加容忍延迟
		if inbound.UrltestTolerance > 0 {
			urltestOutbound["tolerance"] = inbound.UrltestTolerance
		}

		if inbound.SwitchDelay > 0 {
			urltestOutbound["switch_delay"] = inbound.SwitchDelay
		}
		sbConfig.Outbounds = append(sbConfig.Outbounds, urltestOutbound)

		// selector 的节点为 urltest + 所有单独节点
		selectorOutbounds = append([]string{urltestTag}, nodeTags...)
	} else {
		// selector 只包含所有单独节点
		selectorOutbounds = nodeTags
	}

	// 创建 selector outbound
	selectorTag := inboundTag + "_selector"
	selectorOutbound := map[string]interface{}{
		"type":      "selector",
		"tag":       selectorTag,
		"outbounds": selectorOutbounds,
	}
	if inbound.SwitchDelay > 0 {
		selectorOutbound["switch_delay"] = inbound.SwitchDelay
	}
	sbConfig.Outbounds = append(sbConfig.Outbounds, selectorOutbound)

	return selectorTag
}

// 规则管理方法

func (s *Service) GetRules() ([]models.Rule, error) {
	return s.storage.GetRules()
}

func (s *Service) GetRule(id string) (*models.Rule, error) {
	return s.storage.GetRule(id)
}

func (s *Service) CreateRule(rule *models.Rule) error {
	// 生成唯一ID
	if rule.ID == "" {
		rule.ID = generateID()
	}

	// 设置时间戳
	now := time.Now()
	rule.CreatedAt = now
	rule.UpdatedAt = now

	return s.storage.CreateRule(rule)
}

func (s *Service) UpdateRule(rule *models.Rule) error {
	// 更新时间戳
	rule.UpdatedAt = time.Now()
	return s.storage.UpdateRule(rule)
}

func (s *Service) DeleteRule(id string) error {
	return s.storage.DeleteRule(id)
}

// generateRulesConfig 生成规则配置
func (s *Service) generateRulesConfig(sbConfig *models.SingBoxConfig) error {
	rules, err := s.storage.GetRules()
	if err != nil {
		return fmt.Errorf("failed to get rules: %w", err)
	}

	// 按优先级排序规则
	enabledRules := make([]models.Rule, 0)
	for _, rule := range rules {
		if rule.Enabled {
			enabledRules = append(enabledRules, rule)
		}
	}

	// 为每个启用的规则生成配置
	for _, rule := range enabledRules {
		// 生成出站配置
		outboundTag, err := s.generateRuleOutbound(sbConfig, rule)
		if err != nil {
			return fmt.Errorf("failed to generate outbound for rule %s: %w", rule.Name, err)
		}

		// 生成路由规则
		routeRule := s.generateRouteRule(rule, outboundTag)
		sbConfig.Route.Rules = append(sbConfig.Route.Rules, routeRule)
	}

	return nil
}

// generateRuleOutbound 为规则生成出站配置
func (s *Service) generateRuleOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	switch rule.OutboundType {
	case "selector":
		return s.createRuleSelectorOutbound(sbConfig, rule)
	case "urltest":
		return s.createRuleURLTestOutbound(sbConfig, rule)
	case "loadbalance":
		return s.createRuleLoadBalanceOutbound(sbConfig, rule)
	case "fallback":
		return s.createRuleFallbackOutbound(sbConfig, rule)
	case "direct":
		return s.createRuleDirectOutbound(sbConfig, rule)
	case "block":
		return s.createRuleBlockOutbound(sbConfig, rule)
	default:
		return "", fmt.Errorf("unsupported outbound type: %s", rule.OutboundType)
	}
}

// generateRouteRule 生成路由规则
func (s *Service) generateRouteRule(rule models.Rule, outboundTag string) models.RuleConfig {
	routeRule := models.RuleConfig{
		Outbound: outboundTag,
	}

	// 添加匹配条件
	if len(rule.Inbound) > 0 {
		routeRule.Inbound = rule.Inbound
	}
	if len(rule.Domain) > 0 {
		routeRule.Domain = rule.Domain
	}
	if len(rule.DomainSuffix) > 0 {
		routeRule.DomainSuffix = rule.DomainSuffix
	}
	if len(rule.DomainKeyword) > 0 {
		routeRule.DomainKeyword = rule.DomainKeyword
	}
	if len(rule.DomainRegex) > 0 {
		routeRule.DomainRegex = rule.DomainRegex
	}
	if len(rule.Geosite) > 0 {
		routeRule.Geosite = rule.Geosite
	}
	if len(rule.SourceGeoIP) > 0 {
		routeRule.SourceGeoIP = rule.SourceGeoIP
	}
	if len(rule.Geoip) > 0 {
		routeRule.Geoip = rule.Geoip
	}
	if len(rule.IPCidr) > 0 {
		routeRule.IPCidr = rule.IPCidr
	}
	if len(rule.SourceIPCidr) > 0 {
		routeRule.SourceIPCidr = rule.SourceIPCidr
	}
	if len(rule.Port) > 0 {
		routeRule.Port = rule.Port
	}
	if len(rule.SourcePort) > 0 {
		routeRule.SourcePort = rule.SourcePort
	}
	if len(rule.ProcessName) > 0 {
		routeRule.ProcessName = rule.ProcessName
	}
	if len(rule.ProcessPath) > 0 {
		routeRule.ProcessPath = rule.ProcessPath
	}
	if len(rule.Protocol) > 0 {
		routeRule.Protocol = rule.Protocol
	}

	return routeRule
}

// 规则出站创建方法

// createRuleSelectorOutbound 为规则创建 Selector 出站配置
func (s *Service) createRuleSelectorOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	// 获取节点标签
	nodeTags, err := s.getNodeTagsForRule(rule)
	if err != nil {
		return "", err
	}

	if len(nodeTags) == 0 {
		return "", fmt.Errorf("no nodes found for rule %s", rule.Name)
	}

	ruleTag := "rule_" + rule.ID
	tag := s.createSelectorOutbound(sbConfig, ruleTag, nodeTags, models.Inbound{
		AutoSwitch:          rule.AutoSwitch,
		UrlTestUrl:          rule.UrlTestUrl,
		UrlTestInterval:     rule.UrlTestInterval,
		UrlTestIntervalUnit: rule.UrlTestIntervalUnit,
		UrltestTolerance:    rule.UrltestTolerance,
	})
	return tag, nil
}

// createRuleURLTestOutbound 为规则创建 URLTest 出站配置
func (s *Service) createRuleURLTestOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	nodeTags, err := s.getNodeTagsForRule(rule)
	if err != nil {
		return "", err
	}

	if len(nodeTags) == 0 {
		return "", fmt.Errorf("no nodes found for rule %s", rule.Name)
	}

	ruleTag := "rule_" + rule.ID
	tag := s.createURLTestOutbound(sbConfig, ruleTag, nodeTags, models.Inbound{
		UrlTestUrl:          rule.UrlTestUrl,
		UrlTestInterval:     rule.UrlTestInterval,
		UrlTestIntervalUnit: rule.UrlTestIntervalUnit,
		UrltestTolerance:    rule.UrltestTolerance,
	})
	return tag, nil
}

// createRuleLoadBalanceOutbound 为规则创建 LoadBalance 出站配置
func (s *Service) createRuleLoadBalanceOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	nodeTags, err := s.getNodeTagsForRule(rule)
	if err != nil {
		return "", err
	}

	if len(nodeTags) == 0 {
		return "", fmt.Errorf("no nodes found for rule %s", rule.Name)
	}

	ruleTag := "rule_" + rule.ID
	tag := s.createLoadBalanceOutbound(sbConfig, ruleTag, nodeTags, models.Inbound{
		LoadbalanceStrategy: rule.LoadbalanceStrategy,
		LoadbalanceHashKey:  rule.LoadbalanceHashKey,
	})
	return tag, nil
}

// createRuleFallbackOutbound 为规则创建 Fallback 出站配置
func (s *Service) createRuleFallbackOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	nodeTags, err := s.getNodeTagsForRule(rule)
	if err != nil {
		return "", err
	}

	if len(nodeTags) == 0 {
		return "", fmt.Errorf("no nodes found for rule %s", rule.Name)
	}

	ruleTag := "rule_" + rule.ID
	tag := s.createFallbackOutbound(sbConfig, ruleTag, nodeTags, models.Inbound{
		FallbackURL:           rule.FallbackURL,
		FallbackIntervalValue: rule.FallbackIntervalValue,
		FallbackIntervalUnit:  rule.FallbackIntervalUnit,
	})
	return tag, nil
}

// createRuleDirectOutbound 为规则创建 Direct 出站配置
func (s *Service) createRuleDirectOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	ruleTag := "rule_" + rule.ID
	tag := s.createDirectOutbound(sbConfig, ruleTag)
	return tag, nil
}

// createRuleBlockOutbound 为规则创建 Block 出站配置
func (s *Service) createRuleBlockOutbound(sbConfig *models.SingBoxConfig, rule models.Rule) (string, error) {
	ruleTag := "rule_" + rule.ID
	tag := s.createBlockOutbound(sbConfig, ruleTag)
	return tag, nil
}

// getNodeTagsForRule 根据规则获取节点标签
func (s *Service) getNodeTagsForRule(rule models.Rule) ([]string, error) {
	// 获取所有节点
	nodes, err := s.storage.GetNodes()
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes: %w", err)
	}

	var filteredNodes []models.Node

	// 按分组过滤
	if rule.Group != "" {
		for _, node := range nodes {
			if node.Group == rule.Group {
				filteredNodes = append(filteredNodes, node)
			}
		}
	} else {
		filteredNodes = nodes
	}

	// 按包含名称过滤
	if len(rule.IncludeNames) > 0 {
		var includeFiltered []models.Node
		for _, node := range filteredNodes {
			for _, includeName := range rule.IncludeNames {
				if strings.Contains(node.Name, includeName) {
					includeFiltered = append(includeFiltered, node)
					break
				}
			}
		}
		filteredNodes = includeFiltered
	}

	// 按排除名称过滤
	if len(rule.ExcludeNames) > 0 {
		var excludeFiltered []models.Node
		for _, node := range filteredNodes {
			excluded := false
			for _, excludeName := range rule.ExcludeNames {
				if strings.Contains(node.Name, excludeName) {
					excluded = true
					break
				}
			}
			if !excluded {
				excludeFiltered = append(excludeFiltered, node)
			}
		}
		filteredNodes = excludeFiltered
	}

	// 转换为标签
	var nodeTags []string
	for _, node := range filteredNodes {
		nodeTags = append(nodeTags, node.ID)
	}

	return nodeTags, nil
}

// createURLTestOutbound 创建 URLTest 出站配置
func (s *Service) createURLTestOutbound(sbConfig *models.SingBoxConfig, inboundTag string, nodeTags []string, inbound models.Inbound) string {
	urltestTag := inboundTag + "_urltest"

	// 构建间隔字符串
	intervalStr := fmt.Sprintf("%d%s", inbound.UrlTestInterval, inbound.UrlTestIntervalUnit)
	if inbound.UrlTestInterval == 0 {
		intervalStr = "60s" // 默认值
	}

	urltestOutbound := map[string]interface{}{
		"type":         "urltest",
		"tag":          urltestTag,
		"outbounds":    nodeTags,
		"url":          inbound.UrlTestUrl,
		"interval":     intervalStr,
		"idle_timeout": intervalStr,
	}

	// 添加容忍延迟
	if inbound.UrltestTolerance > 0 {
		urltestOutbound["tolerance"] = inbound.UrltestTolerance
	}

	sbConfig.Outbounds = append(sbConfig.Outbounds, urltestOutbound)
	return urltestTag
}

// createLoadBalanceOutbound 创建 LoadBalance 出站配置
func (s *Service) createLoadBalanceOutbound(sbConfig *models.SingBoxConfig, inboundTag string, nodeTags []string, inbound models.Inbound) string {
	loadbalanceTag := inboundTag + "_loadbalance"

	loadbalanceOutbound := map[string]interface{}{
		"type":      "loadbalance",
		"tag":       loadbalanceTag,
		"outbounds": nodeTags,
		"strategy":  inbound.LoadbalanceStrategy,
	}

	// 如果是一致性哈希，添加哈希键
	if inbound.LoadbalanceStrategy == "consistent_hash" && inbound.LoadbalanceHashKey != "" {
		loadbalanceOutbound["hash_key"] = inbound.LoadbalanceHashKey
	}

	sbConfig.Outbounds = append(sbConfig.Outbounds, loadbalanceOutbound)
	return loadbalanceTag
}

// createFallbackOutbound 创建 Fallback 出站配置
func (s *Service) createFallbackOutbound(sbConfig *models.SingBoxConfig, inboundTag string, nodeTags []string, inbound models.Inbound) string {
	fallbackTag := inboundTag + "_fallback"

	// 构建间隔字符串
	intervalStr := fmt.Sprintf("%d%s", inbound.FallbackIntervalValue, inbound.FallbackIntervalUnit)
	if inbound.FallbackIntervalValue == 0 {
		intervalStr = "30s" // 默认值
	}

	fallbackOutbound := map[string]interface{}{
		"type":      "fallback",
		"tag":       fallbackTag,
		"outbounds": nodeTags,
		"url":       inbound.FallbackURL,
		"interval":  intervalStr,
	}

	sbConfig.Outbounds = append(sbConfig.Outbounds, fallbackOutbound)
	return fallbackTag
}

// createDirectOutbound 创建 Direct 出站配置
func (s *Service) createDirectOutbound(sbConfig *models.SingBoxConfig, inboundTag string) string {
	directTag := inboundTag + "_direct"

	directOutbound := map[string]interface{}{
		"type": "direct",
		"tag":  directTag,
	}

	sbConfig.Outbounds = append(sbConfig.Outbounds, directOutbound)
	return directTag
}

// createBlockOutbound 创建 Block 出站配置
func (s *Service) createBlockOutbound(sbConfig *models.SingBoxConfig, inboundTag string) string {
	blockTag := inboundTag + "_block"

	blockOutbound := map[string]interface{}{
		"type": "block",
		"tag":  blockTag,
	}

	sbConfig.Outbounds = append(sbConfig.Outbounds, blockOutbound)
	return blockTag
}
