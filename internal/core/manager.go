package core

import (
	"archive/tar"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

type Manager struct {
	BinaryPath       string
	Version          string
	cachedVersion    string
	versionCacheTime time.Time
}

// 获取当前系统架构
func getArch() string {
	arch := runtime.GOARCH
	os := runtime.GOOS

	switch os {
	case "darwin":
		return "darwin-amd64"
	case "linux":
		return "linux-amd64"
	case "windows":
		return "windows-amd64"
	default:
		return fmt.Sprintf("%s-%s", os, arch)
	}
}

// 获取最新版本信息
func getLatestVersion() (string, error) {
	resp, err := http.Get("https://api.github.com/repos/SagerNet/sing-box/releases/latest")
	if err != nil {
		return "", fmt.Errorf("failed to get latest version: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	version := gjson.Get(string(body), "tag_name").String()
	return version, nil
}

// 下载 sing-box 二进制文件
func (m *Manager) Download(version string) error {
	if version == "" {
		var err error
		version, err = getLatestVersion()
		if err != nil {
			return fmt.Errorf("failed to get latest version: %w", err)
		}
	}

	arch := getArch()
	downloadURL := fmt.Sprintf("https://github.com/SagerNet/sing-box/releases/download/%s/sing-box-%s.tar.gz", version, arch)

	fmt.Printf("Downloading sing-box %s for %s...\n", version, arch)

	resp, err := http.Get(downloadURL)
	if err != nil {
		return fmt.Errorf("failed to download sing-box: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("download failed with status: %d", resp.StatusCode)
	}

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "sing-box-download")
	if err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// 下载并解压
	gzr, err := gzip.NewReader(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer gzr.Close()

	tr := tar.NewReader(gzr)
	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read tar: %w", err)
		}

		if strings.HasSuffix(header.Name, "/sing-box") {
			// 创建目标文件
			targetPath := m.BinaryPath
			file, err := os.Create(targetPath)
			if err != nil {
				return fmt.Errorf("failed to create binary file: %w", err)
			}
			defer file.Close()

			// 复制文件内容
			_, err = io.Copy(file, tr)
			if err != nil {
				return fmt.Errorf("failed to copy binary: %w", err)
			}

			// 设置可执行权限
			if err := os.Chmod(targetPath, 0755); err != nil {
				return fmt.Errorf("failed to set executable permission: %w", err)
			}

			fmt.Printf("Successfully downloaded sing-box %s to %s\n", version, targetPath)
			m.Version = version
			return nil
		}
	}

	return fmt.Errorf("sing-box binary not found in archive")
}

// 检查当前版本
func (m *Manager) GetCurrentVersion() (string, error) {
	if _, err := os.Stat(m.BinaryPath); os.IsNotExist(err) {
		return "", fmt.Errorf("sing-box binary not found")
	}

	cmd := exec.Command(m.BinaryPath, "version")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get version: %w", err)
	}

	// 解析版本输出
	version := strings.TrimSpace(string(output))
	if strings.HasPrefix(version, "sing-box ") {
		version = strings.TrimPrefix(version, "sing-box ")
	}

	return version, nil
}

// 检查是否需要更新
func (m *Manager) CheckForUpdate() (bool, string, error) {
	currentVersion, err := m.GetCurrentVersion()
	if err != nil {
		return true, "", nil // 如果获取当前版本失败，认为需要更新
	}

	latestVersion, err := getLatestVersion()
	if err != nil {
		return false, "", fmt.Errorf("failed to get latest version: %w", err)
	}

	return currentVersion != latestVersion, latestVersion, nil
}

// 自动下载或更新
func (m *Manager) EnsureBinary() error {
	// 检查二进制文件是否存在
	if _, err := os.Stat(m.BinaryPath); os.IsNotExist(err) {
		fmt.Println("sing-box binary not found, downloading...")
		return m.Download("")
	}

	// 检查是否需要更新
	needsUpdate, latestVersion, err := m.CheckForUpdate()
	if err != nil {
		return fmt.Errorf("failed to check for updates: %w", err)
	}

	if needsUpdate {
		fmt.Printf("New version available: %s, updating...\n", latestVersion)
		return m.Download(latestVersion)
	}

	fmt.Println("sing-box is up to date")
	return nil
}

// 获取版本信息
func (m *Manager) GetVersionInfo() map[string]interface{} {
	currentVersion, _ := m.GetCurrentVersion()
	latestVersion, _ := getLatestVersion()

	needsUpdate, _, _ := m.CheckForUpdate()

	return map[string]interface{}{
		"current_version": currentVersion,
		"latest_version":  latestVersion,
		"needs_update":    needsUpdate,
		"binary_path":     m.BinaryPath,
		"exists":          currentVersion != "",
	}
}

// GetVersion 获取当前版本（带缓存）
func (m *Manager) GetVersion() string {
	// 如果缓存时间在5秒内，直接返回缓存版本
	if m.cachedVersion != "" && time.Since(m.versionCacheTime) < 5*time.Second {
		return m.cachedVersion
	}

	version, err := m.GetCurrentVersion()
	if err != nil {
		// 如果获取失败，返回缓存版本或默认值
		if m.cachedVersion != "" {
			return m.cachedVersion
		}
		return "unknown"
	}

	// 更新缓存
	m.cachedVersion = version
	m.versionCacheTime = time.Now()
	return version
}

// GetLatestVersion 获取最新版本
func (m *Manager) GetLatestVersion() string {
	version, err := getLatestVersion()
	if err != nil {
		return "unknown"
	}
	return version
}

// Update 更新到最新版本
func (m *Manager) Update() error {
	return m.EnsureBinary()
}
