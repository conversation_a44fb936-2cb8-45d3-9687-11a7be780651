import{d as i,r as s}from"./index-DWkPzHYP.js";import{c as a}from"./core-B3X1AWgS.js";const C=i("core",()=>{const l=s({running:!1,version:"",uptime:"",memory:"",configPath:""}),e=s(!1),n=s(0),u=5e3,t=async(r=!1)=>{const c=Date.now();if(r||c-n.value>u)try{e.value=!0;const o=await a.getStatus();l.value=o,n.value=c}catch(o){throw console.error("获取核心状态失败:",o),o}finally{e.value=!1}};return{status:l,loading:e,getCoreStatus:t,startCore:async()=>{try{e.value=!0,await a.start(),await t(!0)}catch(r){throw console.error("启动核心失败:",r),r}finally{e.value=!1}},stopCore:async()=>{try{e.value=!0,await a.stop(),await t(!0)}catch(r){throw console.error("停止核心失败:",r),r}finally{e.value=!1}},reloadConfig:async()=>{try{e.value=!0,await a.reloadConfig()}catch(r){throw console.error("重载配置失败:",r),r}finally{e.value=!1}},updateCore:async()=>{try{e.value=!0,await a.update(),await t(!0)}catch(r){throw console.error("更新核心失败:",r),r}finally{e.value=!1}}}});export{C as u};
