import{d as ee,r as v,a as le,o as ae,e as g,h as x,x as te,f as e,w as o,i as I,g as i,u as j,p as ue,A as oe,y as ne,v as T,t as re,j as f,F as h,k as se,E as V,z as de,l as _,_ as ie}from"./index-DhxT1vZ9.js";import{r as L}from"./request-XqBm8oRS.js";import{s as _e}from"./subscriptions-D_I5SwvL.js";import{c as pe}from"./core-BBTNSfha.js";const N={getInbounds(){return L.get("/api/inbounds")},addInbound(m){return L.post("/api/inbounds",m)},updateInbound(m,s){return L.put(`/api/inbounds/${m}`,s)},deleteInbound(m){return L.delete(`/api/inbounds/${m}`)}},ve=ee("inbounds",()=>{const m=v([]),s=v(!1),b=async()=>{try{s.value=!0;const d=await N.getInbounds();return m.value=d,d}catch(d){throw console.error("获取入站配置失败:",d),d}finally{s.value=!1}};return{inbounds:m,loading:s,getInbounds:b,addInbound:async d=>{try{s.value=!0;const l=await N.addInbound(d);return await b(),l}catch(l){throw console.error("添加入站配置失败:",l),l}finally{s.value=!1}},updateInbound:async(d,l)=>{try{s.value=!0;const p=await N.updateInbound(d,l);return await b(),p}catch(p){throw console.error("更新入站配置失败:",p),p}finally{s.value=!1}},deleteInbound:async d=>{try{s.value=!0,await N.deleteInbound(d),await b()}catch(l){throw console.error("删除入站配置失败:",l),l}finally{s.value=!1}}}}),me={class:"inbounds-container"},ce={class:"header"},fe={class:"header-buttons"},be={class:"dialog-footer"},ye=le({__name:"Inbounds",setup(m){const s=ve(),b=v(!1),k=v(!1),U=v(null),R=v([]),d=v([]),l=v({name:"",type:"http",port:1080,username:"",password:"",config_type:"selector",group:"",include_names:[],exclude_names:[],auto_switch:!1,urltest_url:"http://www.gstatic.com/generate_204",urltest_interval_value:60,urltest_interval_unit:"s",urltest_tolerance:50,loadbalance_strategy:"round_robin",loadbalance_hash_key:"source_ip",fallback_url:"http://www.gstatic.com/generate_204",fallback_interval_value:30,fallback_interval_unit:"s"}),p=v(""),C=v(""),A=()=>{p.value.trim()?l.value.include_names=p.value.split(`
`).map(u=>u.trim()).filter(u=>u.length>0):l.value.include_names=[]},E=()=>{C.value.trim()?l.value.exclude_names=C.value.split(`
`).map(u=>u.trim()).filter(u=>u.length>0):l.value.exclude_names=[]},F=u=>{switch(u){case"selector":break;case"urltest":l.value.urltest_url||(l.value.urltest_url="http://www.gstatic.com/generate_204"),l.value.urltest_interval_value||(l.value.urltest_interval_value=60);break;case"loadbalance":l.value.loadbalance_strategy||(l.value.loadbalance_strategy="round_robin");break;case"fallback":l.value.fallback_url||(l.value.fallback_url="http://www.gstatic.com/generate_204"),l.value.fallback_interval_value||(l.value.fallback_interval_value=30);break}},D=async()=>{b.value=!0;try{const u=await s.getInbounds();R.value=u}catch{V.error("加载入站配置失败")}finally{b.value=!1}},z=async()=>{try{const u=await _e.getSubscriptions();d.value=u}catch(u){console.error("加载订阅失败:",u)}},M=async()=>{try{await pe.generateConfig(),V.success("配置生成成功")}catch{V.error("配置生成失败")}},P=u=>{U.value=u,l.value={name:u.name,type:u.type,port:u.port,username:u.username||"",password:u.password||"",config_type:u.config_type||"selector",group:u.group||"",include_names:u.include_names||[],exclude_names:u.exclude_names||[],auto_switch:u.auto_switch||!1,urltest_url:u.urltest_url||"http://www.gstatic.com/generate_204",urltest_interval_value:u.urltest_interval||60,urltest_interval_unit:u.urltest_interval_unit||"s",urltest_tolerance:u.urltest_tolerance||50,loadbalance_strategy:u.loadbalance_strategy||"round_robin",loadbalance_hash_key:u.loadbalance_hash_key||"source_ip",fallback_url:u.fallback_url||"http://www.gstatic.com/generate_204",fallback_interval_value:u.fallback_interval_value||30,fallback_interval_unit:u.fallback_interval_unit||"s"},p.value=(u.include_names||[]).join(`
`),C.value=(u.exclude_names||[]).join(`
`),k.value=!0},H=async()=>{try{const{urltest_interval_value:u,urltest_interval_unit:a,...B}=l.value,c={...B,urltest_interval:u,urltest_interval_unit:a};U.value?(await s.updateInbound(U.value.id,c),V.success("更新成功")):(await s.addInbound(c),V.success("添加成功")),k.value=!1,U.value=null,K(),await D()}catch{V.error("操作失败")}},K=()=>{l.value={name:"",type:"http",port:1080,username:"",password:"",config_type:"selector",group:"",include_names:[],exclude_names:[],auto_switch:!1,urltest_url:"http://www.gstatic.com/generate_204",urltest_interval_value:60,urltest_interval_unit:"s",urltest_tolerance:50,loadbalance_strategy:"round_robin",loadbalance_hash_key:"source_ip",fallback_url:"http://www.gstatic.com/generate_204",fallback_interval_value:30,fallback_interval_unit:"s"},p.value="",C.value=""},O=async u=>{try{await de.confirm("确定要删除这个入站配置吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await s.deleteInbound(u.id),V.success("删除成功"),await D()}catch(a){a!=="cancel"&&V.error("删除失败")}};return ae(()=>{D(),z()}),(u,a)=>{const B=i("el-icon"),c=i("el-button"),S=i("el-table-column"),G=i("el-tag"),J=i("el-table"),y=i("el-input"),r=i("el-form-item"),n=i("el-option"),w=i("el-select"),q=i("el-input-number"),Q=i("el-switch"),$=i("el-alert"),W=i("el-form"),X=i("el-dialog"),Y=ne("loading");return _(),g("div",me,[x("div",ce,[a[28]||(a[28]=x("h2",null,"规则管理",-1)),x("div",fe,[e(c,{type:"primary",onClick:a[0]||(a[0]=t=>k.value=!0)},{default:o(()=>[e(B,null,{default:o(()=>[e(j(ue))]),_:1}),a[26]||(a[26]=I(" 添加规则 "))]),_:1,__:[26]}),e(c,{type:"success",onClick:M},{default:o(()=>[e(B,null,{default:o(()=>[e(j(oe))]),_:1}),a[27]||(a[27]=I(" 配置生成 "))]),_:1,__:[27]})])]),te((_(),T(J,{data:R.value,style:{width:"100%"}},{default:o(()=>[e(S,{prop:"name",label:"名称",width:"150"}),e(S,{prop:"type",label:"类型",width:"100"},{default:o(({row:t})=>[e(G,null,{default:o(()=>[I(re(t.type),1)]),_:2},1024)]),_:1}),e(S,{prop:"port",label:"端口",width:"80"}),e(S,{prop:"username",label:"用户名",width:"120"}),e(S,{prop:"group",label:"分组",width:"120"}),e(S,{label:"操作",width:"200",fixed:"right"},{default:o(({row:t})=>[e(c,{size:"small",onClick:Z=>P(t)},{default:o(()=>a[29]||(a[29]=[I("编辑")])),_:2,__:[29]},1032,["onClick"]),e(c,{size:"small",type:"danger",onClick:Z=>O(t)},{default:o(()=>a[30]||(a[30]=[I("删除")])),_:2,__:[30]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Y,b.value]]),e(X,{modelValue:k.value,"onUpdate:modelValue":a[25]||(a[25]=t=>k.value=t),title:U.value?"编辑规则":"添加规则",width:"700px"},{footer:o(()=>[x("span",be,[e(c,{onClick:a[24]||(a[24]=t=>k.value=!1)},{default:o(()=>a[34]||(a[34]=[I("取消")])),_:1,__:[34]}),e(c,{type:"primary",onClick:H},{default:o(()=>a[35]||(a[35]=[I("确定")])),_:1,__:[35]})])]),default:o(()=>[e(W,{model:l.value,"label-width":"100px"},{default:o(()=>[e(r,{label:"名称",required:""},{default:o(()=>[e(y,{modelValue:l.value.name,"onUpdate:modelValue":a[1]||(a[1]=t=>l.value.name=t),placeholder:"请输入名称"},null,8,["modelValue"])]),_:1}),e(r,{label:"类型",required:""},{default:o(()=>[e(w,{modelValue:l.value.type,"onUpdate:modelValue":a[2]||(a[2]=t=>l.value.type=t),placeholder:"选择类型"},{default:o(()=>[e(n,{label:"HTTP",value:"http"}),e(n,{label:"SOCKS",value:"socks"}),e(n,{label:"Shadowsocks",value:"shadowsocks"}),e(n,{label:"VMess",value:"vmess"}),e(n,{label:"Trojan",value:"trojan"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"端口",required:""},{default:o(()=>[e(q,{modelValue:l.value.port,"onUpdate:modelValue":a[3]||(a[3]=t=>l.value.port=t),min:1,max:65535},null,8,["modelValue"])]),_:1}),e(r,{label:"用户名"},{default:o(()=>[e(y,{modelValue:l.value.username,"onUpdate:modelValue":a[4]||(a[4]=t=>l.value.username=t),placeholder:"请输入用户名（可选）"},null,8,["modelValue"])]),_:1}),e(r,{label:"密码"},{default:o(()=>[e(y,{modelValue:l.value.password,"onUpdate:modelValue":a[5]||(a[5]=t=>l.value.password=t),type:"password",placeholder:"请输入密码（可选）"},null,8,["modelValue"])]),_:1}),e(r,{label:"配置方式",required:""},{default:o(()=>[e(w,{modelValue:l.value.config_type,"onUpdate:modelValue":a[6]||(a[6]=t=>l.value.config_type=t),placeholder:"选择配置方式",onChange:F},{default:o(()=>[e(n,{label:"选择器 (Selector)",value:"selector"}),e(n,{label:"URL测试 (URLTest)",value:"urltest"}),e(n,{label:"负载均衡 (LoadBalance)",value:"loadbalance"}),e(n,{label:"故障转移 (Fallback)",value:"fallback"}),e(n,{label:"直连 (Direct)",value:"direct"}),e(n,{label:"阻断 (Block)",value:"block"})]),_:1},8,["modelValue"])]),_:1}),l.value.config_type==="selector"?(_(),g(h,{key:0},[e(r,{label:"分组"},{default:o(()=>[e(w,{modelValue:l.value.group,"onUpdate:modelValue":a[7]||(a[7]=t=>l.value.group=t),placeholder:"选择订阅分组（可选）",clearable:""},{default:o(()=>[e(n,{label:"全部",value:""}),(_(!0),g(h,null,se(d.value,t=>(_(),T(n,{key:t.id,label:t.name,value:t.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"自动切换"},{default:o(()=>[e(Q,{modelValue:l.value.auto_switch,"onUpdate:modelValue":a[8]||(a[8]=t=>l.value.auto_switch=t)},null,8,["modelValue"]),a[31]||(a[31]=x("span",{style:{"margin-left":"8px",color:"#666","font-size":"12px"}}," 启用后将自动选择最快节点 ",-1))]),_:1,__:[31]}),l.value.auto_switch?(_(),g(h,{key:0},[e(r,{label:"测速URL",required:""},{default:o(()=>[e(y,{modelValue:l.value.urltest_url,"onUpdate:modelValue":a[9]||(a[9]=t=>l.value.urltest_url=t),placeholder:"如 http://www.gstatic.com/generate_204"},null,8,["modelValue"])]),_:1}),e(r,{label:"测速间隔",required:""},{default:o(()=>[e(q,{modelValue:l.value.urltest_interval_value,"onUpdate:modelValue":a[10]||(a[10]=t=>l.value.urltest_interval_value=t),min:1,step:1,style:{width:"120px"}},null,8,["modelValue"]),e(w,{modelValue:l.value.urltest_interval_unit,"onUpdate:modelValue":a[11]||(a[11]=t=>l.value.urltest_interval_unit=t),style:{width:"80px","margin-left":"8px"}},{default:o(()=>[e(n,{label:"秒",value:"s"}),e(n,{label:"分钟",value:"m"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"容忍延迟"},{default:o(()=>[e(q,{modelValue:l.value.urltest_tolerance,"onUpdate:modelValue":a[12]||(a[12]=t=>l.value.urltest_tolerance=t),min:0,step:10,style:{width:"120px"}},null,8,["modelValue"]),a[32]||(a[32]=x("span",{style:{"margin-left":"8px",color:"#666"}},"毫秒",-1))]),_:1,__:[32]})],64)):f("",!0)],64)):f("",!0),["selector","urltest","loadbalance","fallback"].includes(l.value.config_type)?(_(),g(h,{key:1},[e(r,{label:"包含名称"},{default:o(()=>[e(y,{modelValue:p.value,"onUpdate:modelValue":a[13]||(a[13]=t=>p.value=t),type:"textarea",rows:3,placeholder:"每行一个名称，留空表示不限制",onInput:A},null,8,["modelValue"])]),_:1}),e(r,{label:"排除名称"},{default:o(()=>[e(y,{modelValue:C.value,"onUpdate:modelValue":a[14]||(a[14]=t=>C.value=t),type:"textarea",rows:3,placeholder:"每行一个名称，留空表示不排除",onInput:E},null,8,["modelValue"])]),_:1})],64)):f("",!0),l.value.config_type==="urltest"?(_(),g(h,{key:2},[e(r,{label:"测速URL",required:""},{default:o(()=>[e(y,{modelValue:l.value.urltest_url,"onUpdate:modelValue":a[15]||(a[15]=t=>l.value.urltest_url=t),placeholder:"如 http://www.gstatic.com/generate_204"},null,8,["modelValue"])]),_:1}),e(r,{label:"测速间隔",required:""},{default:o(()=>[e(q,{modelValue:l.value.urltest_interval_value,"onUpdate:modelValue":a[16]||(a[16]=t=>l.value.urltest_interval_value=t),min:1,step:1,style:{width:"120px"}},null,8,["modelValue"]),e(w,{modelValue:l.value.urltest_interval_unit,"onUpdate:modelValue":a[17]||(a[17]=t=>l.value.urltest_interval_unit=t),style:{width:"80px","margin-left":"8px"}},{default:o(()=>[e(n,{label:"秒",value:"s"}),e(n,{label:"分钟",value:"m"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"容忍延迟"},{default:o(()=>[e(q,{modelValue:l.value.urltest_tolerance,"onUpdate:modelValue":a[18]||(a[18]=t=>l.value.urltest_tolerance=t),min:0,step:10,style:{width:"120px"}},null,8,["modelValue"]),a[33]||(a[33]=x("span",{style:{"margin-left":"8px",color:"#666"}},"毫秒",-1))]),_:1,__:[33]})],64)):f("",!0),l.value.config_type==="loadbalance"?(_(),g(h,{key:3},[e(r,{label:"负载均衡算法",required:""},{default:o(()=>[e(w,{modelValue:l.value.loadbalance_strategy,"onUpdate:modelValue":a[19]||(a[19]=t=>l.value.loadbalance_strategy=t),placeholder:"选择算法"},{default:o(()=>[e(n,{label:"轮询 (Round Robin)",value:"round_robin"}),e(n,{label:"最少连接 (Least Load)",value:"least_load"}),e(n,{label:"随机 (Random)",value:"random"}),e(n,{label:"一致性哈希 (Consistent Hash)",value:"consistent_hash"})]),_:1},8,["modelValue"])]),_:1}),l.value.loadbalance_strategy==="consistent_hash"?(_(),T(r,{key:0,label:"哈希键"},{default:o(()=>[e(w,{modelValue:l.value.loadbalance_hash_key,"onUpdate:modelValue":a[20]||(a[20]=t=>l.value.loadbalance_hash_key=t),placeholder:"选择哈希键"},{default:o(()=>[e(n,{label:"源IP (Source IP)",value:"source_ip"}),e(n,{label:"目标地址 (Destination)",value:"destination"}),e(n,{label:"源端口 (Source Port)",value:"source_port"})]),_:1},8,["modelValue"])]),_:1})):f("",!0)],64)):f("",!0),l.value.config_type==="fallback"?(_(),g(h,{key:4},[e(r,{label:"健康检查URL"},{default:o(()=>[e(y,{modelValue:l.value.fallback_url,"onUpdate:modelValue":a[21]||(a[21]=t=>l.value.fallback_url=t),placeholder:"如 http://www.gstatic.com/generate_204"},null,8,["modelValue"])]),_:1}),e(r,{label:"检查间隔"},{default:o(()=>[e(q,{modelValue:l.value.fallback_interval_value,"onUpdate:modelValue":a[22]||(a[22]=t=>l.value.fallback_interval_value=t),min:1,step:1,style:{width:"120px"}},null,8,["modelValue"]),e(w,{modelValue:l.value.fallback_interval_unit,"onUpdate:modelValue":a[23]||(a[23]=t=>l.value.fallback_interval_unit=t),style:{width:"80px","margin-left":"8px"}},{default:o(()=>[e(n,{label:"秒",value:"s"}),e(n,{label:"分钟",value:"m"})]),_:1},8,["modelValue"])]),_:1})],64)):f("",!0),l.value.config_type==="direct"?(_(),T($,{key:5,title:"直连配置",description:"此入站将直接连接到目标地址，不经过任何代理服务器。",type:"info",closable:!1,style:{"margin-bottom":"20px"}})):f("",!0),l.value.config_type==="block"?(_(),T($,{key:6,title:"阻断配置",description:"此入站将拒绝所有连接请求，可用于屏蔽特定流量。",type:"warning",closable:!1,style:{"margin-bottom":"20px"}})):f("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),xe=ie(ye,[["__scopeId","data-v-c5cc88f9"]]);export{xe as default};
