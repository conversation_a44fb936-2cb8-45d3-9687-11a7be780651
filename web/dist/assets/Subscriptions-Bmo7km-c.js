import{d as Q,r as f,a as W,o as X,e as Y,h as m,m as I,j as Z,p as ee,f as a,w as r,i as v,g as d,u as te,q as ae,t as D,s as le,E as i,v as re,l as C,_ as oe}from"./index-D_DfZ1f3.js";import{s as b}from"./subscriptions-DKMcZOuJ.js";import"./request-B4rikJo4.js";const ne=Q("subscriptions",()=>{const N=f([]),o=f(!1),c=async()=>{try{o.value=!0;const t=await b.getSubscriptions();return N.value=t,t}catch(t){throw console.error("获取订阅失败:",t),t}finally{o.value=!1}};return{subscriptions:N,loading:o,getSubscriptions:c,addSubscription:async t=>{try{o.value=!0;const s=await b.addSubscription(t);return await c(),s}catch(s){throw console.error("添加订阅失败:",s),s}finally{o.value=!1}},updateSubscription:async(t,s)=>{try{o.value=!0;const w=await b.updateSubscription(t,s);return await c(),w}catch(w){throw console.error("更新订阅失败:",w),w}finally{o.value=!1}},deleteSubscription:async t=>{try{o.value=!0,await b.deleteSubscription(t),await c()}catch(s){throw console.error("删除订阅失败:",s),s}finally{o.value=!1}},updateSubscriptionNodes:async t=>{try{o.value=!0,await b.updateNodes(t),await c()}catch(s){throw console.error("更新订阅节点失败:",s),s}finally{o.value=!1}},forceUpdateSubscription:async t=>{try{o.value=!0,await b.forceUpdateSubscription(t),await c()}catch(s){throw console.error("强制更新订阅失败:",s),s}finally{o.value=!1}},getSchedulerStatus:async()=>{try{return await b.getSchedulerStatus()}catch(t){throw console.error("获取调度器状态失败:",t),t}},startScheduler:async()=>{try{return await b.startScheduler()}catch(t){throw console.error("启动调度器失败:",t),t}},stopScheduler:async()=>{try{return await b.stopScheduler()}catch(t){throw console.error("停止调度器失败:",t),t}}}}),se={class:"subscriptions"},ue={class:"page-header"},ie={class:"header-actions"},de={class:"card-header"},ce={class:"scheduler-status"},pe={style:{"margin-left":"10px",color:"#666"}},me={class:"dialog-footer"},ve=W({__name:"Subscriptions",setup(N){const o=ne(),c=f(!1),p=f(!1),y=f(!1),g=f(null),x=f(),S=f(null),B=f([]),u=f({name:"",url:"",updateInterval:24,description:"",enabled:!0}),z={name:[{required:!0,message:"请输入订阅名称",trigger:"blur"}],url:[{required:!0,message:"请输入订阅地址",trigger:"blur"}],updateInterval:[{required:!0,message:"请输入更新间隔",trigger:"blur"}]},t=async()=>{c.value=!0;try{const l=await o.getSubscriptions();B.value=l}catch{i.error("加载订阅失败")}finally{c.value=!1}},s=l=>{g.value=l,u.value={name:l.name,url:l.url,updateInterval:l.updateInterval,description:l.description,enabled:l.enabled},y.value=!0},w=async()=>{if(x.value)try{await x.value.validate(),g.value?(await o.updateSubscription(g.value.id,u.value),i.success("更新成功")):(await o.addSubscription(u.value),i.success("添加成功")),y.value=!1,g.value=null,T(),await t()}catch{i.error("操作失败")}},E=async l=>{try{await re.confirm("确定要删除这个订阅吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await o.deleteSubscription(l.id),i.success("删除成功"),await t()}catch(e){e!=="cancel"&&i.error("删除失败")}},F=async l=>{try{await o.updateSubscription(l.id,{enabled:l.enabled}),i.success(l.enabled?"已启用":"已禁用")}catch{l.enabled=!l.enabled,i.error("操作失败")}},M=async l=>{try{await o.updateSubscriptionNodes(l.id),i.success("更新成功"),await t()}catch{i.error("更新失败")}},T=()=>{u.value={name:"",url:"",updateInterval:24,description:"",enabled:!0}},U=async()=>{p.value=!0;try{S.value=await o.getSchedulerStatus()}catch{i.error("获取调度器状态失败")}finally{p.value=!1}},A=async()=>{p.value=!0;try{await o.startScheduler(),i.success("调度器启动成功"),await U()}catch{i.error("启动调度器失败")}finally{p.value=!1}},R=async()=>{p.value=!0;try{await o.stopScheduler(),i.success("调度器停止成功"),await U()}catch{i.error("停止调度器失败")}finally{p.value=!1}};return X(()=>{t(),U()}),(l,e)=>{const _=d("el-button"),j=d("el-icon"),L=d("el-tag"),G=d("el-card"),h=d("el-table-column"),q=d("el-switch"),H=d("el-table"),$=d("el-input"),V=d("el-form-item"),J=d("el-input-number"),K=d("el-form"),O=d("el-dialog"),P=le("loading");return C(),Y("div",se,[m("div",ue,[e[10]||(e[10]=m("h2",null,"订阅管理",-1)),m("div",ie,[a(_,{onClick:U,loading:p.value},{default:r(()=>e[8]||(e[8]=[v(" 调度器状态 ")])),_:1,__:[8]},8,["loading"]),a(_,{type:"primary",onClick:e[0]||(e[0]=n=>y.value=!0)},{default:r(()=>[a(j,null,{default:r(()=>[a(te(ae))]),_:1}),e[9]||(e[9]=v(" 添加订阅 "))]),_:1,__:[9]})])]),S.value?(C(),I(G,{key:0,style:{"margin-bottom":"20px"}},{header:r(()=>[m("div",de,[e[13]||(e[13]=m("span",null,"自动更新调度器",-1)),m("div",null,[S.value.running?(C(),I(_,{key:1,type:"danger",size:"small",onClick:R,loading:p.value},{default:r(()=>e[12]||(e[12]=[v(" 停止 ")])),_:1,__:[12]},8,["loading"])):(C(),I(_,{key:0,type:"success",size:"small",onClick:A,loading:p.value},{default:r(()=>e[11]||(e[11]=[v(" 启动 ")])),_:1,__:[11]},8,["loading"]))])])]),default:r(()=>[m("div",ce,[a(L,{type:S.value.running?"success":"danger"},{default:r(()=>[v(D(S.value.running?"运行中":"已停止"),1)]),_:1},8,["type"]),m("span",pe,D(S.value.message),1)])]),_:1})):Z("",!0),ee((C(),I(H,{data:B.value,style:{width:"100%"}},{default:r(()=>[a(h,{prop:"name",label:"订阅名称",width:"150"}),a(h,{prop:"url",label:"订阅地址","min-width":"300","show-overflow-tooltip":""}),a(h,{prop:"nodeCount",label:"节点数量",width:"100"}),a(h,{prop:"lastUpdate",label:"最后更新",width:"150"}),a(h,{prop:"enabled",label:"状态",width:"80"},{default:r(({row:n})=>[a(q,{modelValue:n.enabled,"onUpdate:modelValue":k=>n.enabled=k,onChange:k=>F(n)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(h,{label:"操作",width:"250",fixed:"right"},{default:r(({row:n})=>[a(_,{size:"small",onClick:k=>M(n)},{default:r(()=>e[14]||(e[14]=[v("更新")])),_:2,__:[14]},1032,["onClick"]),a(_,{size:"small",onClick:k=>s(n)},{default:r(()=>e[15]||(e[15]=[v("编辑")])),_:2,__:[15]},1032,["onClick"]),a(_,{size:"small",type:"danger",onClick:k=>E(n)},{default:r(()=>e[16]||(e[16]=[v("删除")])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[P,c.value]]),a(O,{modelValue:y.value,"onUpdate:modelValue":e[7]||(e[7]=n=>y.value=n),title:g.value?"编辑订阅":"添加订阅",width:"600px"},{footer:r(()=>[m("span",me,[a(_,{onClick:e[6]||(e[6]=n=>y.value=!1)},{default:r(()=>e[18]||(e[18]=[v("取消")])),_:1,__:[18]}),a(_,{type:"primary",onClick:w},{default:r(()=>e[19]||(e[19]=[v("确定")])),_:1,__:[19]})])]),default:r(()=>[a(K,{model:u.value,"label-width":"100px",rules:z,ref_key:"subscriptionFormRef",ref:x},{default:r(()=>[a(V,{label:"订阅名称",prop:"name"},{default:r(()=>[a($,{modelValue:u.value.name,"onUpdate:modelValue":e[1]||(e[1]=n=>u.value.name=n),placeholder:"请输入订阅名称"},null,8,["modelValue"])]),_:1}),a(V,{label:"订阅地址",prop:"url"},{default:r(()=>[a($,{modelValue:u.value.url,"onUpdate:modelValue":e[2]||(e[2]=n=>u.value.url=n),placeholder:"请输入订阅地址"},null,8,["modelValue"])]),_:1}),a(V,{label:"更新间隔",prop:"updateInterval"},{default:r(()=>[a(J,{modelValue:u.value.updateInterval,"onUpdate:modelValue":e[3]||(e[3]=n=>u.value.updateInterval=n),min:1,max:168},null,8,["modelValue"]),e[17]||(e[17]=m("span",{style:{"margin-left":"10px"}},"小时",-1))]),_:1,__:[17]}),a(V,{label:"描述",prop:"description"},{default:r(()=>[a($,{modelValue:u.value.description,"onUpdate:modelValue":e[4]||(e[4]=n=>u.value.description=n),type:"textarea",placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1}),a(V,{label:"启用",prop:"enabled"},{default:r(()=>[a(q,{modelValue:u.value.enabled,"onUpdate:modelValue":e[5]||(e[5]=n=>u.value.enabled=n)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),ye=oe(ve,[["__scopeId","data-v-d89ee59c"]]);export{ye as default};
