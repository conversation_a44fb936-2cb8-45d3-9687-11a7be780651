import{d as ne,r as h,a as de,o as ie,e as E,h as L,p as pe,f as l,w as t,i as V,g as f,u as I,x as ce,y as ve,q as me,z as ye,s as _e,m as d,F as fe,k as ge,j as p,E as m,l as r,t as F,n as we,v as J,_ as be}from"./index-D_DfZ1f3.js";import{r as j}from"./request-B4rikJo4.js";const x={getNodes(){return j.get("/api/nodes")},getNodesBySubscription(){return j.get("/api/nodes/by-subscription")},getNode(b){return j.get(`/api/nodes/${b}`)},addNode(b){return j.post("/api/nodes",b)},updateNode(b,N){return j.put(`/api/nodes/${b}`,N)},deleteNode(b){return j.delete(`/api/nodes/${b}`)},deleteAllNodes(){return j.delete("/api/nodes/all")}},ke=ne("nodes",()=>{const b=h([]),N=h({}),y=h(!1),U=async()=>{try{y.value=!0;const e=await x.getNodes();return b.value=e,e}catch(e){throw console.error("获取节点失败:",e),e}finally{y.value=!1}},T=async()=>{try{y.value=!0;const e=await x.getNodesBySubscription();return N.value=e,e}catch(e){throw console.error("获取按订阅分组的节点失败:",e),e}finally{y.value=!1}};return{nodes:b,nodesBySubscription:N,loading:y,getNodes:U,getNodesBySubscription:T,getNode:async e=>{try{return y.value=!0,await x.getNode(e)}catch(k){throw console.error("获取节点失败:",k),k}finally{y.value=!1}},addNode:async e=>{try{y.value=!0;const k=await x.addNode(e);return await U(),k}catch(k){throw console.error("添加节点失败:",k),k}finally{y.value=!1}},updateNode:async(e,k)=>{try{y.value=!0;const B=await x.updateNode(e,k);return await U(),B}catch(B){throw console.error("更新节点失败:",B),B}finally{y.value=!1}},deleteNode:async e=>{await x.deleteNode(e),await T()},deleteAllNodes:async()=>{await x.deleteAllNodes(),b.value=[],N.value={}}}}),Ve={class:"nodes"},he={class:"page-header"},Ne={class:"header-actions"},Ue={key:1,class:"no-test"},Se={key:0,class:"speed-value"},Te={key:1,class:"no-test"},Ce={class:"dialog-footer"},je=de({__name:"Nodes",setup(b){const N=ke(),y=h(!1),U=h(!1),T=h(null),D=h(),O=h(""),P=h(!1),q=h(!1),C=h({}),e=h({name:"",type:"shadowsocks",address:"",port:443,uuid:"",password:"",security:"aes-256-gcm",network:"tcp",group:"",tls_enabled:1,tls_server_name:"",tls_insecure:0,transport_type:"",grpc_service_name:"",grpc_idle_timeout:"",grpc_ping_timeout:"",grpc_permit_without_stream:0}),k={name:[{required:!0,message:"请输入节点名称",trigger:"blur"}],type:[{required:!0,message:"请选择协议类型",trigger:"change"}],address:[{required:!0,message:"请输入服务器地址",trigger:"blur"}],port:[{required:!0,message:"请输入端口",trigger:"blur"}]},B=s=>({shadowsocks:"success",ssr:"warning",vmess:"primary",vless:"info",trojan:"danger",hysteria:"success",hysteria2:"success",tuic:"warning",naiveproxy:"info"})[s]||"default",A=async()=>{y.value=!0;try{const s=await N.getNodesBySubscription();C.value=s,Object.keys(s).length>0&&(O.value=Object.keys(s)[0])}catch{m.error("加载节点失败")}finally{y.value=!1}},H=s=>{T.value=s,e.value={name:s.name,type:s.type,address:s.address,port:s.port,uuid:s.uuid||"",password:s.password||"",security:s.security||"aes-256-gcm",network:s.network||"tcp",group:s.group||"",tls_enabled:s.tls_enabled??1,tls_server_name:s.tls_server_name||"",tls_insecure:s.tls_insecure??0,transport_type:s.transport_type||"",grpc_service_name:s.grpc_service_name||"",grpc_idle_timeout:s.grpc_idle_timeout||"",grpc_ping_timeout:s.grpc_ping_timeout||"",grpc_permit_without_stream:s.grpc_permit_without_stream??0},U.value=!0},R=async()=>{if(D.value)try{await D.value.validate(),T.value?(await N.updateNode(T.value.id,e.value),m.success("更新成功")):(await N.addNode(e.value),m.success("添加成功")),U.value=!1,T.value=null,K(),await A()}catch{m.error("操作失败")}},W=async s=>{try{await J.confirm("确定要删除这个节点吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await N.deleteNode(s.id),m.success("删除成功"),await A()}catch(a){a!=="cancel"&&m.error("删除失败")}},K=()=>{e.value={name:"",type:"shadowsocks",address:"",port:443,uuid:"",password:"",security:"aes-256-gcm",network:"tcp",group:"",tls_enabled:1,tls_server_name:"",tls_insecure:0,transport_type:"",grpc_service_name:"",grpc_idle_timeout:"",grpc_ping_timeout:"",grpc_permit_without_stream:0}},Q=()=>{switch(e.value.type){case"shadowsocks":e.value.security="aes-256-gcm",e.value.network="tcp",e.value.uuid="";break;case"ssr":e.value.security="aes-256-cfb",e.value.network="origin+plain",e.value.uuid="";break;case"vmess":e.value.security="auto",e.value.network="tcp",e.value.password="";break;case"vless":e.value.security="none",e.value.network="tcp",e.value.password="";break;case"trojan":e.value.security="tls",e.value.network="tcp",e.value.uuid="";break;case"hysteria":e.value.security="none",e.value.network="udp",e.value.uuid="";break;case"hysteria2":e.value.security="none",e.value.network="udp",e.value.uuid="";break;case"tuic":e.value.security="none",e.value.network="udp",e.value.uuid="";break;case"naiveproxy":e.value.security="none",e.value.network="tcp",e.value.uuid="";break}},X=async()=>{try{await J.confirm("确定要删除所有节点吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await N.deleteAllNodes(),m.success("所有节点已删除"),await A()}catch(s){s!=="cancel"&&m.error("删除失败")}},Y=async s=>{try{s.testing=!0;const a=await fetch("/api/nodes/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({node_id:s.id,url:"https://www.google.com"})});if(!a.ok)throw new Error("测试失败");const _=await a.json();s.latency=_.latency,s.speed=_.speed,s.lastTestTime=new Date().toLocaleString(),m.success(`测试完成: 延迟 ${_.latency}ms, 速度 ${_.speed}`)}catch(a){m.error("速度测试失败"),console.error("Speed test error:",a)}finally{s.testing=!1}},Z=async()=>{try{P.value=!0;const s=[];if(Object.values(C.value).forEach(i=>{i.forEach(c=>{s.push(c.id)})}),s.length===0){m.warning("没有可测试的节点");return}const a=await fetch("/api/nodes/batch-test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({node_ids:s,url:"https://www.google.com"})});if(!a.ok)throw new Error("批量测试失败");const _=await a.json();_.results.forEach(i=>{Object.values(C.value).forEach(c=>{const g=c.find($=>$.id===i.node_id);g&&i.status==="success"&&(g.latency=i.latency,g.speed=i.speed,g.lastTestTime=new Date().toLocaleString())})}),m.success(`批量测试完成，共测试 ${_.results.length} 个节点`)}catch(s){m.error("批量测试失败"),console.error("Batch test error:",s)}finally{P.value=!1}},ee=async()=>{try{q.value=!0;const s=[];if(Object.values(C.value).forEach(i=>{i.forEach(c=>{s.push(c.id)})}),s.length===0){m.warning("没有可检测的节点");return}const a=await fetch("/api/nodes/auto-detect",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({node_ids:s})});if(!a.ok)throw new Error("自动检测失败");const _=await a.json();_.results.forEach(i=>{Object.values(C.value).forEach(c=>{const g=c.find($=>$.id===i.node_id);g&&i.status==="success"&&(g.latency=i.latency,g.speed=i.speed,g.lastTestTime=new Date().toLocaleString())})}),m.success(`自动检测完成，共检测 ${_.results.length} 个节点`)}catch(s){m.error("自动检测失败"),console.error("Auto detect error:",s)}finally{q.value=!1}},le=s=>s<100?"latency-excellent":s<200?"latency-good":s<300?"latency-normal":"latency-poor";return ie(()=>{A()}),(s,a)=>{const _=f("el-icon"),i=f("el-button"),c=f("el-table-column"),g=f("el-tag"),$=f("el-table"),ae=f("el-tab-pane"),te=f("el-tabs"),w=f("el-input"),n=f("el-form-item"),u=f("el-option"),S=f("el-select"),oe=f("el-input-number"),M=f("el-switch"),se=f("el-form"),ue=f("el-dialog"),re=_e("loading");return r(),E("div",Ve,[L("div",he,[a[35]||(a[35]=L("h2",null,"节点管理",-1)),L("div",Ne,[l(i,{type:"success",onClick:Z,loading:P.value},{default:t(()=>[l(_,null,{default:t(()=>[l(I(ce))]),_:1}),a[31]||(a[31]=V(" 批量测试 "))]),_:1,__:[31]},8,["loading"]),l(i,{type:"warning",onClick:ee,loading:q.value},{default:t(()=>[l(_,null,{default:t(()=>[l(I(ve))]),_:1}),a[32]||(a[32]=V(" 自动检测 "))]),_:1,__:[32]},8,["loading"]),l(i,{type:"primary",onClick:a[0]||(a[0]=o=>U.value=!0)},{default:t(()=>[l(_,null,{default:t(()=>[l(I(me))]),_:1}),a[33]||(a[33]=V(" 添加节点 "))]),_:1,__:[33]}),l(i,{type:"danger",onClick:X,style:{"margin-left":"12px"}},{default:t(()=>[l(_,null,{default:t(()=>[l(I(ye))]),_:1}),a[34]||(a[34]=V(" 删除所有节点 "))]),_:1,__:[34]})])]),pe((r(),d(te,{modelValue:O.value,"onUpdate:modelValue":a[1]||(a[1]=o=>O.value=o),type:"card"},{default:t(()=>[(r(!0),E(fe,null,ge(C.value,(o,z)=>(r(),d(ae,{key:z,label:`${z} (${o.length})`,name:z},{default:t(()=>[l($,{data:o,style:{width:"100%"}},{default:t(()=>[l(c,{prop:"name",label:"节点名称",width:"150"}),l(c,{prop:"type",label:"协议类型",width:"100"},{default:t(({row:v})=>[l(g,{type:B(v.type)},{default:t(()=>[V(F(v.type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"address",label:"服务器地址",width:"150"}),l(c,{prop:"port",label:"端口",width:"80"}),l(c,{prop:"group",label:"分组",width:"100"}),l(c,{prop:"security",label:"加密方式",width:"120"}),l(c,{prop:"network",label:"传输协议",width:"120"}),l(c,{label:"延迟",width:"100"},{default:t(({row:v})=>[v.latency?(r(),E("span",{key:0,class:we(le(v.latency))},F(v.latency)+"ms ",3)):(r(),E("span",Ue,"未测试"))]),_:1}),l(c,{label:"速度",width:"120"},{default:t(({row:v})=>[v.speed?(r(),E("span",Se,F(v.speed),1)):(r(),E("span",Te,"未测试"))]),_:1}),l(c,{label:"状态",width:"100"},{default:t(({row:v})=>[v.available!==void 0?(r(),d(g,{key:0,type:v.available?"success":"danger"},{default:t(()=>[V(F(v.available?"可用":"不可用"),1)]),_:2},1032,["type"])):(r(),d(g,{key:1,type:"info"},{default:t(()=>a[36]||(a[36]=[V("未检测")])),_:1,__:[36]}))]),_:1}),l(c,{label:"操作",width:"280",fixed:"right"},{default:t(({row:v})=>[l(i,{size:"small",onClick:G=>Y(v),loading:v.testing},{default:t(()=>a[37]||(a[37]=[V(" 测试速度 ")])),_:2,__:[37]},1032,["onClick","loading"]),l(i,{size:"small",onClick:G=>H(v)},{default:t(()=>a[38]||(a[38]=[V("编辑")])),_:2,__:[38]},1032,["onClick"]),l(i,{size:"small",type:"danger",onClick:G=>W(v)},{default:t(()=>a[39]||(a[39]=[V("删除")])),_:2,__:[39]},1032,["onClick"])]),_:1})]),_:2},1032,["data"])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])),[[re,y.value]]),l(ue,{modelValue:U.value,"onUpdate:modelValue":a[30]||(a[30]=o=>U.value=o),title:T.value?"编辑节点":"添加节点",width:"600px"},{footer:t(()=>[L("span",Ce,[l(i,{onClick:a[29]||(a[29]=o=>U.value=!1)},{default:t(()=>a[40]||(a[40]=[V("取消")])),_:1,__:[40]}),l(i,{type:"primary",onClick:R},{default:t(()=>a[41]||(a[41]=[V("确定")])),_:1,__:[41]})])]),default:t(()=>[l(se,{model:e.value,"label-width":"100px",rules:k,ref_key:"nodeFormRef",ref:D},{default:t(()=>[l(n,{label:"节点名称",prop:"name"},{default:t(()=>[l(w,{modelValue:e.value.name,"onUpdate:modelValue":a[2]||(a[2]=o=>e.value.name=o),placeholder:"请输入节点名称"},null,8,["modelValue"])]),_:1}),l(n,{label:"协议类型",prop:"type"},{default:t(()=>[l(S,{modelValue:e.value.type,"onUpdate:modelValue":a[3]||(a[3]=o=>e.value.type=o),placeholder:"请选择协议类型",style:{width:"100%"},onChange:Q},{default:t(()=>[l(u,{label:"Shadowsocks",value:"shadowsocks"}),l(u,{label:"ShadowsocksR",value:"ssr"}),l(u,{label:"VMess",value:"vmess"}),l(u,{label:"VLESS",value:"vless"}),l(u,{label:"Trojan",value:"trojan"}),l(u,{label:"Hysteria",value:"hysteria"}),l(u,{label:"Hysteria2",value:"hysteria2"}),l(u,{label:"Tuic",value:"tuic"}),l(u,{label:"NaiveProxy",value:"naiveproxy"})]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"服务器地址",prop:"address"},{default:t(()=>[l(w,{modelValue:e.value.address,"onUpdate:modelValue":a[4]||(a[4]=o=>e.value.address=o),placeholder:"请输入服务器地址"},null,8,["modelValue"])]),_:1}),l(n,{label:"端口",prop:"port"},{default:t(()=>[l(oe,{modelValue:e.value.port,"onUpdate:modelValue":a[5]||(a[5]=o=>e.value.port=o),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),["vmess","vless"].includes(e.value.type)?(r(),d(n,{key:0,label:"UUID",prop:"uuid"},{default:t(()=>[l(w,{modelValue:e.value.uuid,"onUpdate:modelValue":a[6]||(a[6]=o=>e.value.uuid=o),placeholder:"请输入UUID"},null,8,["modelValue"])]),_:1})):p("",!0),["trojan"].includes(e.value.type)?(r(),d(n,{key:1,label:"密码",prop:"password"},{default:t(()=>[l(w,{modelValue:e.value.password,"onUpdate:modelValue":a[7]||(a[7]=o=>e.value.password=o),placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):p("",!0),["shadowsocks","ssr"].includes(e.value.type)?(r(),d(n,{key:2,label:"密码",prop:"password"},{default:t(()=>[l(w,{modelValue:e.value.password,"onUpdate:modelValue":a[8]||(a[8]=o=>e.value.password=o),placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):p("",!0),["hysteria","hysteria2"].includes(e.value.type)?(r(),d(n,{key:3,label:"密码",prop:"password"},{default:t(()=>[l(w,{modelValue:e.value.password,"onUpdate:modelValue":a[9]||(a[9]=o=>e.value.password=o),placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):p("",!0),["tuic","naiveproxy"].includes(e.value.type)?(r(),d(n,{key:4,label:"密码",prop:"password"},{default:t(()=>[l(w,{modelValue:e.value.password,"onUpdate:modelValue":a[10]||(a[10]=o=>e.value.password=o),placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):p("",!0),["shadowsocks","ssr"].includes(e.value.type)?(r(),d(n,{key:5,label:"加密方式",prop:"security"},{default:t(()=>[l(S,{modelValue:e.value.security,"onUpdate:modelValue":a[11]||(a[11]=o=>e.value.security=o),placeholder:"请选择加密方式",style:{width:"100%"}},{default:t(()=>[l(u,{label:"aes-256-gcm",value:"aes-256-gcm"}),l(u,{label:"aes-128-gcm",value:"aes-128-gcm"}),l(u,{label:"chacha20-poly1305",value:"chacha20-poly1305"}),l(u,{label:"aes-256-cfb",value:"aes-256-cfb"}),l(u,{label:"aes-128-cfb",value:"aes-128-cfb"}),l(u,{label:"chacha20",value:"chacha20"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["vmess"].includes(e.value.type)?(r(),d(n,{key:6,label:"安全设置",prop:"security"},{default:t(()=>[l(S,{modelValue:e.value.security,"onUpdate:modelValue":a[12]||(a[12]=o=>e.value.security=o),placeholder:"请选择安全设置",style:{width:"100%"}},{default:t(()=>[l(u,{label:"auto",value:"auto"}),l(u,{label:"none",value:"none"}),l(u,{label:"tls",value:"tls"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["vless"].includes(e.value.type)?(r(),d(n,{key:7,label:"安全设置",prop:"security"},{default:t(()=>[l(S,{modelValue:e.value.security,"onUpdate:modelValue":a[13]||(a[13]=o=>e.value.security=o),placeholder:"请选择安全设置",style:{width:"100%"}},{default:t(()=>[l(u,{label:"none",value:"none"}),l(u,{label:"tls",value:"tls"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["trojan"].includes(e.value.type)?(r(),d(n,{key:8,label:"安全设置",prop:"security"},{default:t(()=>[l(S,{modelValue:e.value.security,"onUpdate:modelValue":a[14]||(a[14]=o=>e.value.security=o),placeholder:"请选择安全设置",style:{width:"100%"}},{default:t(()=>[l(u,{label:"tls",value:"tls"}),l(u,{label:"none",value:"none"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["hysteria2"].includes(e.value.type)?(r(),d(n,{key:9,label:"安全设置",prop:"security"},{default:t(()=>[l(S,{modelValue:e.value.security,"onUpdate:modelValue":a[15]||(a[15]=o=>e.value.security=o),placeholder:"请选择安全设置",style:{width:"100%"}},{default:t(()=>[l(u,{label:"none",value:"none"}),l(u,{label:"tls",value:"tls"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["vmess"].includes(e.value.type)?(r(),d(n,{key:10,label:"传输协议",prop:"network"},{default:t(()=>[l(S,{modelValue:e.value.network,"onUpdate:modelValue":a[16]||(a[16]=o=>e.value.network=o),placeholder:"请选择传输协议",style:{width:"100%"}},{default:t(()=>[l(u,{label:"tcp",value:"tcp"}),l(u,{label:"ws",value:"ws"}),l(u,{label:"http",value:"http"}),l(u,{label:"grpc",value:"grpc"}),l(u,{label:"quic",value:"quic"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["vless"].includes(e.value.type)?(r(),d(n,{key:11,label:"传输协议",prop:"network"},{default:t(()=>[l(S,{modelValue:e.value.network,"onUpdate:modelValue":a[17]||(a[17]=o=>e.value.network=o),placeholder:"请选择传输协议",style:{width:"100%"}},{default:t(()=>[l(u,{label:"tcp",value:"tcp"}),l(u,{label:"ws",value:"ws"}),l(u,{label:"http",value:"http"}),l(u,{label:"grpc",value:"grpc"}),l(u,{label:"quic",value:"quic"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["trojan"].includes(e.value.type)?(r(),d(n,{key:12,label:"传输协议",prop:"network"},{default:t(()=>[l(S,{modelValue:e.value.network,"onUpdate:modelValue":a[18]||(a[18]=o=>e.value.network=o),placeholder:"请选择传输协议",style:{width:"100%"}},{default:t(()=>[l(u,{label:"tcp",value:"tcp"}),l(u,{label:"ws",value:"ws"}),l(u,{label:"grpc",value:"grpc"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["hysteria2"].includes(e.value.type)?(r(),d(n,{key:13,label:"传输协议",prop:"network"},{default:t(()=>[l(S,{modelValue:e.value.network,"onUpdate:modelValue":a[19]||(a[19]=o=>e.value.network=o),placeholder:"请选择传输协议",style:{width:"100%"}},{default:t(()=>[l(u,{label:"udp",value:"udp"}),l(u,{label:"tcp",value:"tcp"})]),_:1},8,["modelValue"])]),_:1})):p("",!0),["ssr"].includes(e.value.type)?(r(),d(n,{key:14,label:"协议+混淆",prop:"network"},{default:t(()=>[l(w,{modelValue:e.value.network,"onUpdate:modelValue":a[20]||(a[20]=o=>e.value.network=o),placeholder:"例如: origin+plain"},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"?(r(),d(n,{key:15,label:"TLS 启用"},{default:t(()=>[l(M,{modelValue:e.value.tls_enabled,"onUpdate:modelValue":a[21]||(a[21]=o=>e.value.tls_enabled=o),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"?(r(),d(n,{key:16,label:"TLS SNI"},{default:t(()=>[l(w,{modelValue:e.value.tls_server_name,"onUpdate:modelValue":a[22]||(a[22]=o=>e.value.tls_server_name=o),placeholder:"如 usb07.nwncd.com"},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"?(r(),d(n,{key:17,label:"TLS Insecure"},{default:t(()=>[l(M,{modelValue:e.value.tls_insecure,"onUpdate:modelValue":a[23]||(a[23]=o=>e.value.tls_insecure=o),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"&&e.value.network==="grpc"?(r(),d(n,{key:18,label:"GRPC Service Name"},{default:t(()=>[l(w,{modelValue:e.value.grpc_service_name,"onUpdate:modelValue":a[24]||(a[24]=o=>e.value.grpc_service_name=o),placeholder:"如 mygrpc"},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"&&e.value.network==="grpc"?(r(),d(n,{key:19,label:"GRPC Idle Timeout"},{default:t(()=>[l(w,{modelValue:e.value.grpc_idle_timeout,"onUpdate:modelValue":a[25]||(a[25]=o=>e.value.grpc_idle_timeout=o),placeholder:"如 60s"},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"&&e.value.network==="grpc"?(r(),d(n,{key:20,label:"GRPC Ping Timeout"},{default:t(()=>[l(w,{modelValue:e.value.grpc_ping_timeout,"onUpdate:modelValue":a[26]||(a[26]=o=>e.value.grpc_ping_timeout=o),placeholder:"如 20s"},null,8,["modelValue"])]),_:1})):p("",!0),e.value.type==="trojan"&&e.value.network==="grpc"?(r(),d(n,{key:21,label:"GRPC Permit Without Stream"},{default:t(()=>[l(M,{modelValue:e.value.grpc_permit_without_stream,"onUpdate:modelValue":a[27]||(a[27]=o=>e.value.grpc_permit_without_stream=o),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})):p("",!0),l(n,{label:"分组",prop:"group"},{default:t(()=>[l(w,{modelValue:e.value.group,"onUpdate:modelValue":a[28]||(a[28]=o=>e.value.group=o),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),Ee=be(je,[["__scopeId","data-v-f2b5373d"]]);export{Ee as default};
