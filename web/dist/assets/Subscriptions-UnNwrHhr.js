import{d as fe,r as u,a as Ie,o as De,e as C,h,f as t,j as ie,F as H,w as a,i as p,g,u as R,p as ce,m as je,q as Me,s as Ee,v as x,x as pe,t as q,y as Le,k as ve,E as n,z as Z,l as y,n as Re,_ as Oe}from"./index-DhxT1vZ9.js";import{s as U}from"./subscriptions-D_I5SwvL.js";import{r as z}from"./request-XqBm8oRS.js";const Pe=fe("subscriptions",()=>{const _=u([]),s=u(!1),d=async()=>{try{s.value=!0;const r=await U.getSubscriptions();return _.value=r,r}catch(r){throw console.error("获取订阅失败:",r),r}finally{s.value=!1}};return{subscriptions:_,loading:s,getSubscriptions:d,addSubscription:async r=>{try{s.value=!0;const c=await U.addSubscription(r);return await d(),c}catch(c){throw console.error("添加订阅失败:",c),c}finally{s.value=!1}},updateSubscription:async(r,c)=>{try{s.value=!0;const v=await U.updateSubscription(r,c);return await d(),v}catch(v){throw console.error("更新订阅失败:",v),v}finally{s.value=!1}},deleteSubscription:async r=>{try{s.value=!0,await U.deleteSubscription(r),await d()}catch(c){throw console.error("删除订阅失败:",c),c}finally{s.value=!1}},updateSubscriptionNodes:async r=>{try{s.value=!0,await U.updateNodes(r),await d()}catch(c){throw console.error("更新订阅节点失败:",c),c}finally{s.value=!1}},forceUpdateSubscription:async r=>{try{s.value=!0,await U.forceUpdateSubscription(r),await d()}catch(c){throw console.error("强制更新订阅失败:",c),c}finally{s.value=!1}},getSchedulerStatus:async()=>{try{return await U.getSchedulerStatus()}catch(r){throw console.error("获取调度器状态失败:",r),r}},startScheduler:async()=>{try{return await U.startScheduler()}catch(r){throw console.error("启动调度器失败:",r),r}},stopScheduler:async()=>{try{return await U.stopScheduler()}catch(r){throw console.error("停止调度器失败:",r),r}}}}),F={getNodes(){return z.get("/api/nodes")},getNodesBySubscription(){return z.get("/api/nodes/by-subscription")},getNode(_){return z.get(`/api/nodes/${_}`)},addNode(_){return z.post("/api/nodes",_)},updateNode(_,s){return z.put(`/api/nodes/${_}`,s)},deleteNode(_){return z.delete(`/api/nodes/${_}`)},deleteAllNodes(){return z.delete("/api/nodes/all")}},Ge=fe("nodes",()=>{const _=u([]),s=u({}),d=u(!1),k=async()=>{try{d.value=!0;const i=await F.getNodes();return _.value=i,i}catch(i){throw console.error("获取节点失败:",i),i}finally{d.value=!1}},B=async()=>{try{d.value=!0;const i=await F.getNodesBySubscription();return s.value=i,i}catch(i){throw console.error("获取按订阅分组的节点失败:",i),i}finally{d.value=!1}};return{nodes:_,nodesBySubscription:s,loading:d,getNodes:k,getNodesBySubscription:B,getNode:async i=>{try{return d.value=!0,await F.getNode(i)}catch(r){throw console.error("获取节点失败:",r),r}finally{d.value=!1}},addNode:async i=>{try{d.value=!0;const r=await F.addNode(i);return await k(),r}catch(r){throw console.error("添加节点失败:",r),r}finally{d.value=!1}},updateNode:async(i,r)=>{try{d.value=!0;const c=await F.updateNode(i,r);return await k(),c}catch(c){throw console.error("更新节点失败:",c),c}finally{d.value=!1}},deleteNode:async i=>{await F.deleteNode(i),await B()},deleteAllNodes:async()=>{await F.deleteAllNodes(),_.value=[],s.value={}}}}),He={class:"subscriptions"},Je={class:"page-header"},Ke={class:"header-actions"},Qe={class:"card-header"},We={class:"scheduler-status"},Xe={style:{"margin-left":"10px",color:"#666"}},Ye={key:1,class:"no-test"},Ze={key:0,class:"speed-value"},et={key:1,class:"no-test"},tt={class:"dialog-footer"},at={class:"dialog-footer"},lt=Ie({__name:"Subscriptions",setup(_){const s=Pe(),d=Ge(),k=u("subscriptions"),B=u(""),J=u(""),D=u(!1),S=u(!1),$=u(!1),T=u(null),i=u(),r=u(null),c=u([]),v=u({name:"",url:"",updateInterval:24,description:"",enabled:!0}),K=u(!1),I=u(!1),j=u(null),Q=u(),W=u(!1),X=u(!1);u([]);const ee=u({}),w=u({name:"",type:"shadowsocks",address:"",port:443,subscription:""}),me={name:[{required:!0,message:"请输入订阅名称",trigger:"blur"}],url:[{required:!0,message:"请输入订阅地址",trigger:"blur"}],updateInterval:[{required:!0,message:"请输入更新间隔",trigger:"blur"}]},ye={name:[{required:!0,message:"请输入节点名称",trigger:"blur"}],type:[{required:!0,message:"请选择协议类型",trigger:"change"}],address:[{required:!0,message:"请输入服务器地址",trigger:"blur"}],port:[{required:!0,message:"请输入端口",trigger:"blur"}],subscription:[{required:!0,message:"请选择订阅分组",trigger:"change"}]},O=async()=>{D.value=!0;try{const l=await s.getSubscriptions();c.value=l}catch{n.error("加载订阅失败")}finally{D.value=!1}},be=l=>{T.value=l,v.value={name:l.name,url:l.url,updateInterval:l.updateInterval,description:l.description,enabled:l.enabled},$.value=!0},ge=async()=>{if(i.value)try{await i.value.validate(),T.value?(await s.updateSubscription(T.value.id,v.value),n.success("更新成功")):(await s.addSubscription(v.value),n.success("添加成功")),$.value=!1,T.value=null,he(),await O()}catch{n.error("操作失败")}},_e=async l=>{try{await Z.confirm("确定要删除这个订阅吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await s.deleteSubscription(l.id),n.success("删除成功"),await O()}catch(e){e!=="cancel"&&n.error("删除失败")}},we=async l=>{try{await s.updateSubscription(l.id,{enabled:l.enabled}),n.success(l.enabled?"已启用":"已禁用")}catch{l.enabled=!l.enabled,n.error("操作失败")}},Se=async l=>{try{await s.updateSubscriptionNodes(l.id),n.success("更新成功"),await O()}catch{n.error("更新失败")}},he=()=>{v.value={name:"",url:"",updateInterval:24,description:"",enabled:!0}},P=async()=>{S.value=!0;try{r.value=await s.getSchedulerStatus()}catch{n.error("获取调度器状态失败")}finally{S.value=!1}},ke=async()=>{S.value=!0;try{await s.startScheduler(),n.success("调度器启动成功"),await P()}catch{n.error("启动调度器失败")}finally{S.value=!1}},Ve=async()=>{S.value=!0;try{await s.stopScheduler(),n.success("调度器停止成功"),await P()}catch{n.error("停止调度器失败")}finally{S.value=!1}},A=async()=>{K.value=!0;try{const l=await d.getNodesBySubscription();ee.value=l,Object.keys(l).length>0&&!B.value&&(B.value=Object.keys(l)[0])}catch{n.error("加载节点失败")}finally{K.value=!1}},Ne=l=>{j.value=l,w.value={name:l.name,type:l.type,address:l.address,port:l.port,subscription:l.subscription||""},I.value=!0},Ce=async()=>{if(Q.value)try{await Q.value.validate(),j.value?(await d.updateNode(j.value.id,w.value),n.success("更新成功")):(await d.addNode(w.value),n.success("添加成功")),I.value=!1,j.value=null,Ue(),await A()}catch{n.error("操作失败")}},xe=async l=>{try{await Z.confirm("确定要删除这个节点吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await d.deleteNode(l.id),n.success("删除成功"),await A()}catch(e){e!=="cancel"&&n.error("删除失败")}},Ue=()=>{w.value={name:"",type:"shadowsocks",address:"",port:443,subscription:""}},Be=async l=>{l.testing=!0;try{n.info("节点测速功能开发中"),await A()}catch{n.error("测试失败")}finally{l.testing=!1}},$e=async()=>{W.value=!0;try{n.info("批量测试功能开发中"),await A()}catch{n.error("批量测试失败")}finally{W.value=!1}},Te=async()=>{X.value=!0;try{n.info("自动检测功能开发中"),await A()}catch{n.error("自动检测失败")}finally{X.value=!1}},Ae=async()=>{try{await Z.confirm("确定要删除所有节点吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await d.deleteAllNodes(),n.success("删除成功"),await A()}catch(l){l!=="cancel"&&n.error("删除失败")}},qe=l=>({shadowsocks:"primary",vmess:"success",vless:"warning",trojan:"danger",hysteria:"info",hysteria2:"info"})[l]||"",ze=l=>l<100?"latency-good":l<300?"latency-medium":"latency-bad";return De(()=>{O(),A(),P()}),(l,e)=>{const f=g("el-button"),M=g("el-icon"),G=g("el-tag"),Fe=g("el-card"),b=g("el-table-column"),te=g("el-switch"),ae=g("el-table"),Y=g("el-tab-pane"),le=g("el-tabs"),E=g("el-input"),V=g("el-form-item"),oe=g("el-input-number"),re=g("el-form"),se=g("el-dialog"),L=g("el-option"),ne=g("el-select"),ue=Le("loading");return y(),C("div",He,[h("div",Je,[e[24]||(e[24]=h("h2",null,"订阅管理",-1)),h("div",Ke,[k.value==="subscriptions"?(y(),C(H,{key:0},[t(f,{onClick:P,loading:S.value},{default:a(()=>e[18]||(e[18]=[p(" 调度器状态 ")])),_:1,__:[18]},8,["loading"]),t(f,{type:"primary",onClick:e[0]||(e[0]=o=>$.value=!0)},{default:a(()=>[t(M,null,{default:a(()=>[t(R(ce))]),_:1}),e[19]||(e[19]=p(" 添加订阅 "))]),_:1,__:[19]})],64)):k.value==="nodes"?(y(),C(H,{key:1},[t(f,{type:"success",onClick:$e,loading:W.value},{default:a(()=>[t(M,null,{default:a(()=>[t(R(je))]),_:1}),e[20]||(e[20]=p(" 批量测试 "))]),_:1,__:[20]},8,["loading"]),t(f,{type:"warning",onClick:Te,loading:X.value},{default:a(()=>[t(M,null,{default:a(()=>[t(R(Me))]),_:1}),e[21]||(e[21]=p(" 自动检测 "))]),_:1,__:[21]},8,["loading"]),t(f,{type:"primary",onClick:e[1]||(e[1]=o=>I.value=!0)},{default:a(()=>[t(M,null,{default:a(()=>[t(R(ce))]),_:1}),e[22]||(e[22]=p(" 添加节点 "))]),_:1,__:[22]}),t(f,{type:"danger",onClick:Ae,style:{"margin-left":"12px"}},{default:a(()=>[t(M,null,{default:a(()=>[t(R(Ee))]),_:1}),e[23]||(e[23]=p(" 删除所有节点 "))]),_:1,__:[23]})],64)):ie("",!0)])]),t(le,{modelValue:k.value,"onUpdate:modelValue":e[3]||(e[3]=o=>k.value=o),type:"card"},{default:a(()=>[t(Y,{label:"订阅列表",name:"subscriptions"},{default:a(()=>[r.value?(y(),x(Fe,{key:0,style:{"margin-bottom":"20px"}},{header:a(()=>[h("div",Qe,[e[27]||(e[27]=h("span",null,"自动更新调度器",-1)),h("div",null,[r.value.running?(y(),x(f,{key:1,type:"danger",size:"small",onClick:Ve,loading:S.value},{default:a(()=>e[26]||(e[26]=[p(" 停止 ")])),_:1,__:[26]},8,["loading"])):(y(),x(f,{key:0,type:"success",size:"small",onClick:ke,loading:S.value},{default:a(()=>e[25]||(e[25]=[p(" 启动 ")])),_:1,__:[25]},8,["loading"]))])])]),default:a(()=>[h("div",We,[t(G,{type:r.value.running?"success":"danger"},{default:a(()=>[p(q(r.value.running?"运行中":"已停止"),1)]),_:1},8,["type"]),h("span",Xe,q(r.value.message),1)])]),_:1})):ie("",!0),pe((y(),x(ae,{data:c.value,style:{width:"100%"}},{default:a(()=>[t(b,{prop:"name",label:"订阅名称",width:"150"}),t(b,{prop:"url",label:"订阅地址","min-width":"300","show-overflow-tooltip":""}),t(b,{prop:"nodeCount",label:"节点数量",width:"100"},{default:a(({row:o})=>[t(f,{type:"text",onClick:N=>{k.value="nodes",J.value=o.name},disabled:!o.nodeCount},{default:a(()=>[p(q(o.nodeCount||0),1)]),_:2},1032,["onClick","disabled"])]),_:1}),t(b,{prop:"lastUpdate",label:"最后更新",width:"150"}),t(b,{prop:"enabled",label:"状态",width:"80"},{default:a(({row:o})=>[t(te,{modelValue:o.enabled,"onUpdate:modelValue":N=>o.enabled=N,onChange:N=>we(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(b,{label:"操作",width:"250",fixed:"right"},{default:a(({row:o})=>[t(f,{size:"small",onClick:N=>Se(o)},{default:a(()=>e[28]||(e[28]=[p("更新")])),_:2,__:[28]},1032,["onClick"]),t(f,{size:"small",onClick:N=>be(o)},{default:a(()=>e[29]||(e[29]=[p("编辑")])),_:2,__:[29]},1032,["onClick"]),t(f,{size:"small",type:"danger",onClick:N=>_e(o)},{default:a(()=>e[30]||(e[30]=[p("删除")])),_:2,__:[30]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ue,D.value]])]),_:1}),t(Y,{label:"节点管理",name:"nodes"},{default:a(()=>[pe((y(),x(le,{modelValue:B.value,"onUpdate:modelValue":e[2]||(e[2]=o=>B.value=o),type:"card"},{default:a(()=>[(y(!0),C(H,null,ve(ee.value,(o,N)=>(y(),x(Y,{key:N,label:`${N} (${o.length})`,name:N},{default:a(()=>[t(ae,{data:o,style:{width:"100%"}},{default:a(()=>[t(b,{prop:"name",label:"节点名称",width:"150"}),t(b,{prop:"type",label:"协议类型",width:"100"},{default:a(({row:m})=>[t(G,{type:qe(m.type)},{default:a(()=>[p(q(m.type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"address",label:"服务器地址",width:"150"}),t(b,{prop:"port",label:"端口",width:"80"}),t(b,{prop:"group",label:"分组",width:"100"}),t(b,{prop:"security",label:"加密方式",width:"120"}),t(b,{prop:"network",label:"传输协议",width:"120"}),t(b,{label:"延迟",width:"100"},{default:a(({row:m})=>[m.latency?(y(),C("span",{key:0,class:Re(ze(m.latency))},q(m.latency)+"ms ",3)):(y(),C("span",Ye,"未测试"))]),_:1}),t(b,{label:"速度",width:"120"},{default:a(({row:m})=>[m.speed?(y(),C("span",Ze,q(m.speed),1)):(y(),C("span",et,"未测试"))]),_:1}),t(b,{label:"状态",width:"100"},{default:a(({row:m})=>[m.available!==void 0?(y(),x(G,{key:0,type:m.available?"success":"danger"},{default:a(()=>[p(q(m.available?"可用":"不可用"),1)]),_:2},1032,["type"])):(y(),x(G,{key:1,type:"info"},{default:a(()=>e[31]||(e[31]=[p("未检测")])),_:1,__:[31]}))]),_:1}),t(b,{label:"操作",width:"280",fixed:"right"},{default:a(({row:m})=>[t(f,{size:"small",onClick:de=>Be(m),loading:m.testing},{default:a(()=>e[32]||(e[32]=[p(" 测试速度 ")])),_:2,__:[32]},1032,["onClick","loading"]),t(f,{size:"small",onClick:de=>Ne(m)},{default:a(()=>e[33]||(e[33]=[p("编辑")])),_:2,__:[33]},1032,["onClick"]),t(f,{size:"small",type:"danger",onClick:de=>xe(m)},{default:a(()=>e[34]||(e[34]=[p("删除")])),_:2,__:[34]},1032,["onClick"])]),_:1})]),_:2},1032,["data"])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])),[[ue,K.value]])]),_:1})]),_:1},8,["modelValue"]),t(se,{modelValue:$.value,"onUpdate:modelValue":e[10]||(e[10]=o=>$.value=o),title:T.value?"编辑订阅":"添加订阅",width:"600px"},{footer:a(()=>[h("span",tt,[t(f,{onClick:e[9]||(e[9]=o=>$.value=!1)},{default:a(()=>e[36]||(e[36]=[p("取消")])),_:1,__:[36]}),t(f,{type:"primary",onClick:ge},{default:a(()=>e[37]||(e[37]=[p("确定")])),_:1,__:[37]})])]),default:a(()=>[t(re,{model:v.value,"label-width":"100px",rules:me,ref_key:"subscriptionFormRef",ref:i},{default:a(()=>[t(V,{label:"订阅名称",prop:"name"},{default:a(()=>[t(E,{modelValue:v.value.name,"onUpdate:modelValue":e[4]||(e[4]=o=>v.value.name=o),placeholder:"请输入订阅名称"},null,8,["modelValue"])]),_:1}),t(V,{label:"订阅地址",prop:"url"},{default:a(()=>[t(E,{modelValue:v.value.url,"onUpdate:modelValue":e[5]||(e[5]=o=>v.value.url=o),placeholder:"请输入订阅地址"},null,8,["modelValue"])]),_:1}),t(V,{label:"更新间隔",prop:"updateInterval"},{default:a(()=>[t(oe,{modelValue:v.value.updateInterval,"onUpdate:modelValue":e[6]||(e[6]=o=>v.value.updateInterval=o),min:1,max:168},null,8,["modelValue"]),e[35]||(e[35]=h("span",{style:{"margin-left":"10px"}},"小时",-1))]),_:1,__:[35]}),t(V,{label:"描述",prop:"description"},{default:a(()=>[t(E,{modelValue:v.value.description,"onUpdate:modelValue":e[7]||(e[7]=o=>v.value.description=o),type:"textarea",placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1}),t(V,{label:"启用",prop:"enabled"},{default:a(()=>[t(te,{modelValue:v.value.enabled,"onUpdate:modelValue":e[8]||(e[8]=o=>v.value.enabled=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(se,{modelValue:I.value,"onUpdate:modelValue":e[17]||(e[17]=o=>I.value=o),title:j.value?"编辑节点":"添加节点",width:"600px"},{footer:a(()=>[h("span",at,[t(f,{onClick:e[16]||(e[16]=o=>I.value=!1)},{default:a(()=>e[38]||(e[38]=[p("取消")])),_:1,__:[38]}),t(f,{type:"primary",onClick:Ce},{default:a(()=>e[39]||(e[39]=[p("确定")])),_:1,__:[39]})])]),default:a(()=>[t(re,{model:w.value,"label-width":"100px",rules:ye,ref_key:"nodeFormRef",ref:Q},{default:a(()=>[t(V,{label:"节点名称",prop:"name"},{default:a(()=>[t(E,{modelValue:w.value.name,"onUpdate:modelValue":e[11]||(e[11]=o=>w.value.name=o),placeholder:"请输入节点名称"},null,8,["modelValue"])]),_:1}),t(V,{label:"协议类型",prop:"type"},{default:a(()=>[t(ne,{modelValue:w.value.type,"onUpdate:modelValue":e[12]||(e[12]=o=>w.value.type=o),placeholder:"请选择协议类型",style:{width:"100%"}},{default:a(()=>[t(L,{label:"Shadowsocks",value:"shadowsocks"}),t(L,{label:"VMess",value:"vmess"}),t(L,{label:"VLESS",value:"vless"}),t(L,{label:"Trojan",value:"trojan"})]),_:1},8,["modelValue"])]),_:1}),t(V,{label:"服务器地址",prop:"address"},{default:a(()=>[t(E,{modelValue:w.value.address,"onUpdate:modelValue":e[13]||(e[13]=o=>w.value.address=o),placeholder:"请输入服务器地址"},null,8,["modelValue"])]),_:1}),t(V,{label:"端口",prop:"port"},{default:a(()=>[t(oe,{modelValue:w.value.port,"onUpdate:modelValue":e[14]||(e[14]=o=>w.value.port=o),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(V,{label:"订阅分组",prop:"subscription"},{default:a(()=>[t(ne,{modelValue:w.value.subscription,"onUpdate:modelValue":e[15]||(e[15]=o=>w.value.subscription=o),placeholder:"请选择订阅分组",style:{width:"100%"}},{default:a(()=>[(y(!0),C(H,null,ve(c.value,o=>(y(),x(L,{key:o.id,label:o.name,value:o.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),nt=Oe(lt,[["__scopeId","data-v-18931586"]]);export{nt as default};
