import{d as me,r as p,a as Fe,c as Re,o as Me,e as h,h as T,f as e,j as N,F as C,w as s,i as b,g as V,u as W,p as pe,m as Pe,q as Le,s as He,v as U,x as ce,t as R,y as Ee,k as ve,E as n,z as te,l as d,n as Oe,_ as We}from"./index-CbgCyghA.js";import{s as $}from"./subscriptions-DMy3dXqD.js";import{r as M}from"./request-CKMx0Qhv.js";const Ge=me("subscriptions",()=>{const k=p([]),u=p(!1),c=async()=>{try{u.value=!0;const r=await $.getSubscriptions();return k.value=r,r}catch(r){throw console.error("获取订阅失败:",r),r}finally{u.value=!1}};return{subscriptions:k,loading:u,getSubscriptions:c,addSubscription:async r=>{try{u.value=!0;const m=await $.addSubscription(r);return await c(),m}catch(m){throw console.error("添加订阅失败:",m),m}finally{u.value=!1}},updateSubscription:async(r,m)=>{try{u.value=!0;const _=await $.updateSubscription(r,m);return await c(),_}catch(_){throw console.error("更新订阅失败:",_),_}finally{u.value=!1}},deleteSubscription:async r=>{try{u.value=!0,await $.deleteSubscription(r),await c()}catch(m){throw console.error("删除订阅失败:",m),m}finally{u.value=!1}},updateSubscriptionNodes:async r=>{try{u.value=!0,await $.updateNodes(r),await c()}catch(m){throw console.error("更新订阅节点失败:",m),m}finally{u.value=!1}},forceUpdateSubscription:async r=>{try{u.value=!0,await $.forceUpdateSubscription(r),await c()}catch(m){throw console.error("强制更新订阅失败:",m),m}finally{u.value=!1}},getSchedulerStatus:async()=>{try{return await $.getSchedulerStatus()}catch(r){throw console.error("获取调度器状态失败:",r),r}},startScheduler:async()=>{try{return await $.startScheduler()}catch(r){throw console.error("启动调度器失败:",r),r}},stopScheduler:async()=>{try{return await $.stopScheduler()}catch(r){throw console.error("停止调度器失败:",r),r}}}}),P={getNodes(){return M.get("/api/nodes")},getNodesBySubscription(){return M.get("/api/nodes/by-subscription")},getNode(k){return M.get(`/api/nodes/${k}`)},addNode(k){return M.post("/api/nodes",k)},updateNode(k,u){return M.put(`/api/nodes/${k}`,u)},deleteNode(k){return M.delete(`/api/nodes/${k}`)},deleteAllNodes(){return M.delete("/api/nodes/all")}},Je=me("nodes",()=>{const k=p([]),u=p({}),c=p(!1),B=async()=>{try{c.value=!0;const v=await P.getNodes();return k.value=v,v}catch(v){throw console.error("获取节点失败:",v),v}finally{c.value=!1}},I=async()=>{try{c.value=!0;const v=await P.getNodesBySubscription();return u.value=v,v}catch(v){throw console.error("获取按订阅分组的节点失败:",v),v}finally{c.value=!1}};return{nodes:k,nodesBySubscription:u,loading:c,getNodes:B,getNodesBySubscription:I,getNode:async v=>{try{return c.value=!0,await P.getNode(v)}catch(r){throw console.error("获取节点失败:",r),r}finally{c.value=!1}},addNode:async v=>{try{c.value=!0;const r=await P.addNode(v);return await B(),r}catch(r){throw console.error("添加节点失败:",r),r}finally{c.value=!1}},updateNode:async(v,r)=>{try{c.value=!0;const m=await P.updateNode(v,r);return await B(),m}catch(m){throw console.error("更新节点失败:",m),m}finally{c.value=!1}},deleteNode:async v=>{await P.deleteNode(v),await I()},deleteAllNodes:async()=>{await P.deleteAllNodes(),k.value=[],u.value={}}}}),Qe={class:"subscriptions"},Ke={class:"page-header"},Xe={class:"header-actions"},Ye={class:"card-header"},Ze={class:"scheduler-status"},el={style:{"margin-left":"10px",color:"#666"}},ll={key:1,class:"no-test"},al={key:0,class:"speed-value"},tl={key:1,class:"no-test"},sl={class:"dialog-footer"},ol={class:"dialog-footer"},rl=Fe({__name:"Subscriptions",setup(k){const u=Ge(),c=Je(),B=p("subscriptions"),I=p(""),X=p(""),H=p(!1),x=p(!1),A=p(!1),j=p(null),v=p(),r=p(null),m=p([]),_=p({name:"",url:"",updateInterval:24,description:"",enabled:!0}),Y=p(!1),L=p(!1),E=p(null),Z=p(),ee=p(!1),le=p(!1);p([]);const se=p({}),a=p({name:"",type:"shadowsocks",address:"",port:443,subscription:"",uuid:"",password:"",security:"",network:"tcp",ws_path:"",ws_headers:"",grpc_service_name:"",h2_path:"",h2_host:"",tls_enabled:!1,tls_server_name:"",tls_insecure:!1,up_mbps:100,down_mbps:100}),ye={name:[{required:!0,message:"请输入订阅名称",trigger:"blur"}],url:[{required:!0,message:"请输入订阅地址",trigger:"blur"}],updateInterval:[{required:!0,message:"请输入更新间隔",trigger:"blur"}]},be=Re(()=>{const o={name:[{required:!0,message:"请输入节点名称",trigger:"blur"}],type:[{required:!0,message:"请选择协议类型",trigger:"change"}],address:[{required:!0,message:"请输入服务器地址",trigger:"blur"}],port:[{required:!0,message:"请输入端口",trigger:"blur"}],subscription:[{required:!0,message:"请选择订阅分组",trigger:"change"}]};return a.value.type==="shadowsocks"?(o.password=[{required:!0,message:"请输入密码",trigger:"blur"}],o.security=[{required:!0,message:"请选择加密方式",trigger:"change"}]):["vmess","vless"].includes(a.value.type)?(o.uuid=[{required:!0,message:"请输入UUID",trigger:"blur"}],a.value.type==="vmess"&&(o.security=[{required:!0,message:"请选择加密方式",trigger:"change"}])):a.value.type==="trojan"?o.password=[{required:!0,message:"请输入密码",trigger:"blur"}]:["hysteria","hysteria2"].includes(a.value.type)&&(o.password=[{required:!0,message:"请输入密码",trigger:"blur"}]),o}),G=async()=>{H.value=!0;try{const o=await u.getSubscriptions();m.value=o}catch{n.error("加载订阅失败")}finally{H.value=!1}},fe=o=>{j.value=o,_.value={name:o.name,url:o.url,updateInterval:o.updateInterval,description:o.description,enabled:o.enabled},A.value=!0},_e=async()=>{if(v.value)try{await v.value.validate(),j.value?(await u.updateSubscription(j.value.id,_.value),n.success("更新成功")):(await u.addSubscription(_.value),n.success("添加成功")),A.value=!1,j.value=null,Ve(),await G()}catch{n.error("操作失败")}},ge=async o=>{try{await te.confirm("确定要删除这个订阅吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await u.deleteSubscription(o.id),n.success("删除成功"),await G()}catch(l){l!=="cancel"&&n.error("删除失败")}},we=async o=>{try{await u.updateSubscription(o.id,{enabled:o.enabled}),n.success(o.enabled?"已启用":"已禁用")}catch{o.enabled=!o.enabled,n.error("操作失败")}},he=async o=>{try{await u.updateSubscriptionNodes(o.id),n.success("更新成功"),await G()}catch{n.error("更新失败")}},Ve=()=>{_.value={name:"",url:"",updateInterval:24,description:"",enabled:!0}},J=async()=>{x.value=!0;try{r.value=await u.getSchedulerStatus()}catch{n.error("获取调度器状态失败")}finally{x.value=!1}},ke=async()=>{x.value=!0;try{await u.startScheduler(),n.success("调度器启动成功"),await J()}catch{n.error("启动调度器失败")}finally{x.value=!1}},Se=async()=>{x.value=!0;try{await u.stopScheduler(),n.success("调度器停止成功"),await J()}catch{n.error("停止调度器失败")}finally{x.value=!1}},Ne=o=>{switch(a.value.uuid="",a.value.password="",a.value.security="",a.value.network="tcp",a.value.tls_enabled=!1,o){case"shadowsocks":a.value.security="aes-256-gcm",a.value.port=443;break;case"vmess":a.value.security="auto",a.value.port=443,a.value.tls_enabled=!0;break;case"vless":a.value.port=443,a.value.tls_enabled=!0;break;case"trojan":a.value.port=443,a.value.tls_enabled=!0;break;case"hysteria":case"hysteria2":a.value.port=443,a.value.tls_enabled=!0;break}},Ce=o=>{switch(a.value.ws_path="",a.value.ws_headers="",a.value.grpc_service_name="",a.value.h2_path="",a.value.h2_host="",o){case"ws":a.value.ws_path="/";break;case"grpc":a.value.grpc_service_name="GunService";break;case"h2":a.value.h2_path="/";break}},z=async()=>{Y.value=!0;try{const o=await c.getNodesBySubscription();se.value=o,Object.keys(o).length>0&&!I.value&&(I.value=Object.keys(o)[0])}catch{n.error("加载节点失败")}finally{Y.value=!1}},Ue=o=>{var l,f,D,F;E.value=o,a.value={name:o.name,type:o.type,address:o.address,port:o.port,subscription:o.subscription||"",uuid:o.uuid||"",password:o.password||"",security:o.security||"",network:o.network||"tcp",ws_path:((l=o.transport)==null?void 0:l.path)||"",ws_headers:JSON.stringify(((f=o.transport)==null?void 0:f.headers)||{}),grpc_service_name:o.grpc_service_name||"",h2_path:((D=o.transport)==null?void 0:D.path)||"",h2_host:((F=o.transport)==null?void 0:F.host)||"",tls_enabled:!!o.tls_enabled,tls_server_name:o.tls_server_name||"",tls_insecure:!!o.tls_insecure,up_mbps:o.up_mbps||100,down_mbps:o.down_mbps||100},L.value=!0},xe=async()=>{if(Z.value)try{await Z.value.validate(),E.value?(await c.updateNode(E.value.id,a.value),n.success("更新成功")):(await c.addNode(a.value),n.success("添加成功")),L.value=!1,E.value=null,Be(),await z()}catch{n.error("操作失败")}},Te=async o=>{try{await te.confirm("确定要删除这个节点吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await c.deleteNode(o.id),n.success("删除成功"),await z()}catch(l){l!=="cancel"&&n.error("删除失败")}},Be=()=>{a.value={name:"",type:"shadowsocks",address:"",port:443,subscription:"",uuid:"",password:"",security:"aes-256-gcm",network:"tcp",ws_path:"",ws_headers:"",grpc_service_name:"",h2_path:"",h2_host:"",tls_enabled:!1,tls_server_name:"",tls_insecure:!1,up_mbps:100,down_mbps:100}},qe=async o=>{o.testing=!0;try{n.info("节点测速功能开发中"),await z()}catch{n.error("测试失败")}finally{o.testing=!1}},$e=async()=>{ee.value=!0;try{n.info("批量测试功能开发中"),await z()}catch{n.error("批量测试失败")}finally{ee.value=!1}},Ie=async()=>{le.value=!0;try{n.info("自动检测功能开发中"),await z()}catch{n.error("自动检测失败")}finally{le.value=!1}},Ae=async()=>{try{await te.confirm("确定要删除所有节点吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await c.deleteAllNodes(),n.success("删除成功"),await z()}catch(o){o!=="cancel"&&n.error("删除失败")}},De=o=>({shadowsocks:"primary",vmess:"success",vless:"warning",trojan:"danger",hysteria:"info",hysteria2:"info"})[o]||"",je=o=>o<100?"latency-good":o<300?"latency-medium":"latency-bad";return Me(()=>{G(),z(),J()}),(o,l)=>{const f=V("el-button"),D=V("el-icon"),F=V("el-tag"),ze=V("el-card"),w=V("el-table-column"),Q=V("el-switch"),oe=V("el-table"),ae=V("el-tab-pane"),re=V("el-tabs"),S=V("el-input"),i=V("el-form-item"),K=V("el-input-number"),ue=V("el-form"),ne=V("el-dialog"),y=V("el-option"),O=V("el-select"),de=Ee("loading");return d(),h("div",Qe,[T("div",Ke,[l[41]||(l[41]=T("h2",null,"订阅管理",-1)),T("div",Xe,[B.value==="subscriptions"?(d(),h(C,{key:0},[e(f,{onClick:J,loading:x.value},{default:s(()=>l[35]||(l[35]=[b(" 调度器状态 ")])),_:1,__:[35]},8,["loading"]),e(f,{type:"primary",onClick:l[0]||(l[0]=t=>A.value=!0)},{default:s(()=>[e(D,null,{default:s(()=>[e(W(pe))]),_:1}),l[36]||(l[36]=b(" 添加订阅 "))]),_:1,__:[36]})],64)):B.value==="nodes"?(d(),h(C,{key:1},[e(f,{type:"success",onClick:$e,loading:ee.value},{default:s(()=>[e(D,null,{default:s(()=>[e(W(Pe))]),_:1}),l[37]||(l[37]=b(" 批量测试 "))]),_:1,__:[37]},8,["loading"]),e(f,{type:"warning",onClick:Ie,loading:le.value},{default:s(()=>[e(D,null,{default:s(()=>[e(W(Le))]),_:1}),l[38]||(l[38]=b(" 自动检测 "))]),_:1,__:[38]},8,["loading"]),e(f,{type:"primary",onClick:l[1]||(l[1]=t=>L.value=!0)},{default:s(()=>[e(D,null,{default:s(()=>[e(W(pe))]),_:1}),l[39]||(l[39]=b(" 添加节点 "))]),_:1,__:[39]}),e(f,{type:"danger",onClick:Ae,style:{"margin-left":"12px"}},{default:s(()=>[e(D,null,{default:s(()=>[e(W(He))]),_:1}),l[40]||(l[40]=b(" 删除所有节点 "))]),_:1,__:[40]})],64)):N("",!0)])]),e(re,{modelValue:B.value,"onUpdate:modelValue":l[3]||(l[3]=t=>B.value=t),type:"card"},{default:s(()=>[e(ae,{label:"订阅列表",name:"subscriptions"},{default:s(()=>[r.value?(d(),U(ze,{key:0,style:{"margin-bottom":"20px"}},{header:s(()=>[T("div",Ye,[l[44]||(l[44]=T("span",null,"自动更新调度器",-1)),T("div",null,[r.value.running?(d(),U(f,{key:1,type:"danger",size:"small",onClick:Se,loading:x.value},{default:s(()=>l[43]||(l[43]=[b(" 停止 ")])),_:1,__:[43]},8,["loading"])):(d(),U(f,{key:0,type:"success",size:"small",onClick:ke,loading:x.value},{default:s(()=>l[42]||(l[42]=[b(" 启动 ")])),_:1,__:[42]},8,["loading"]))])])]),default:s(()=>[T("div",Ze,[e(F,{type:r.value.running?"success":"danger"},{default:s(()=>[b(R(r.value.running?"运行中":"已停止"),1)]),_:1},8,["type"]),T("span",el,R(r.value.message),1)])]),_:1})):N("",!0),ce((d(),U(oe,{data:m.value,style:{width:"100%"}},{default:s(()=>[e(w,{prop:"name",label:"订阅名称",width:"150"}),e(w,{prop:"url",label:"订阅地址","min-width":"300","show-overflow-tooltip":""}),e(w,{prop:"nodeCount",label:"节点数量",width:"100"},{default:s(({row:t})=>[e(f,{type:"text",onClick:q=>{B.value="nodes",X.value=t.name},disabled:!t.nodeCount},{default:s(()=>[b(R(t.nodeCount||0),1)]),_:2},1032,["onClick","disabled"])]),_:1}),e(w,{prop:"lastUpdate",label:"最后更新",width:"150"}),e(w,{prop:"enabled",label:"状态",width:"80"},{default:s(({row:t})=>[e(Q,{modelValue:t.enabled,"onUpdate:modelValue":q=>t.enabled=q,onChange:q=>we(t)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(w,{label:"操作",width:"250",fixed:"right"},{default:s(({row:t})=>[e(f,{size:"small",onClick:q=>he(t)},{default:s(()=>l[45]||(l[45]=[b("更新")])),_:2,__:[45]},1032,["onClick"]),e(f,{size:"small",onClick:q=>fe(t)},{default:s(()=>l[46]||(l[46]=[b("编辑")])),_:2,__:[46]},1032,["onClick"]),e(f,{size:"small",type:"danger",onClick:q=>ge(t)},{default:s(()=>l[47]||(l[47]=[b("删除")])),_:2,__:[47]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,H.value]])]),_:1}),e(ae,{label:"节点管理",name:"nodes"},{default:s(()=>[ce((d(),U(re,{modelValue:I.value,"onUpdate:modelValue":l[2]||(l[2]=t=>I.value=t),type:"card"},{default:s(()=>[(d(!0),h(C,null,ve(se.value,(t,q)=>(d(),U(ae,{key:q,label:`${q} (${t.length})`,name:q},{default:s(()=>[e(oe,{data:t,style:{width:"100%"}},{default:s(()=>[e(w,{prop:"name",label:"节点名称",width:"150"}),e(w,{prop:"type",label:"协议类型",width:"100"},{default:s(({row:g})=>[e(F,{type:De(g.type)},{default:s(()=>[b(R(g.type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),e(w,{prop:"address",label:"服务器地址",width:"150"}),e(w,{prop:"port",label:"端口",width:"80"}),e(w,{prop:"group",label:"分组",width:"100"}),e(w,{prop:"security",label:"加密方式",width:"120"}),e(w,{prop:"network",label:"传输协议",width:"120"}),e(w,{label:"延迟",width:"100"},{default:s(({row:g})=>[g.latency?(d(),h("span",{key:0,class:Oe(je(g.latency))},R(g.latency)+"ms ",3)):(d(),h("span",ll,"未测试"))]),_:1}),e(w,{label:"速度",width:"120"},{default:s(({row:g})=>[g.speed?(d(),h("span",al,R(g.speed),1)):(d(),h("span",tl,"未测试"))]),_:1}),e(w,{label:"状态",width:"100"},{default:s(({row:g})=>[g.available!==void 0?(d(),U(F,{key:0,type:g.available?"success":"danger"},{default:s(()=>[b(R(g.available?"可用":"不可用"),1)]),_:2},1032,["type"])):(d(),U(F,{key:1,type:"info"},{default:s(()=>l[48]||(l[48]=[b("未检测")])),_:1,__:[48]}))]),_:1}),e(w,{label:"操作",width:"280",fixed:"right"},{default:s(({row:g})=>[e(f,{size:"small",onClick:ie=>qe(g),loading:g.testing},{default:s(()=>l[49]||(l[49]=[b(" 测试速度 ")])),_:2,__:[49]},1032,["onClick","loading"]),e(f,{size:"small",onClick:ie=>Ue(g)},{default:s(()=>l[50]||(l[50]=[b("编辑")])),_:2,__:[50]},1032,["onClick"]),e(f,{size:"small",type:"danger",onClick:ie=>Te(g)},{default:s(()=>l[51]||(l[51]=[b("删除")])),_:2,__:[51]},1032,["onClick"])]),_:1})]),_:2},1032,["data"])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])),[[de,Y.value]])]),_:1})]),_:1},8,["modelValue"]),e(ne,{modelValue:A.value,"onUpdate:modelValue":l[10]||(l[10]=t=>A.value=t),title:j.value?"编辑订阅":"添加订阅",width:"600px"},{footer:s(()=>[T("span",sl,[e(f,{onClick:l[9]||(l[9]=t=>A.value=!1)},{default:s(()=>l[53]||(l[53]=[b("取消")])),_:1,__:[53]}),e(f,{type:"primary",onClick:_e},{default:s(()=>l[54]||(l[54]=[b("确定")])),_:1,__:[54]})])]),default:s(()=>[e(ue,{model:_.value,"label-width":"100px",rules:ye,ref_key:"subscriptionFormRef",ref:v},{default:s(()=>[e(i,{label:"订阅名称",prop:"name"},{default:s(()=>[e(S,{modelValue:_.value.name,"onUpdate:modelValue":l[4]||(l[4]=t=>_.value.name=t),placeholder:"请输入订阅名称"},null,8,["modelValue"])]),_:1}),e(i,{label:"订阅地址",prop:"url"},{default:s(()=>[e(S,{modelValue:_.value.url,"onUpdate:modelValue":l[5]||(l[5]=t=>_.value.url=t),placeholder:"请输入订阅地址"},null,8,["modelValue"])]),_:1}),e(i,{label:"更新间隔",prop:"updateInterval"},{default:s(()=>[e(K,{modelValue:_.value.updateInterval,"onUpdate:modelValue":l[6]||(l[6]=t=>_.value.updateInterval=t),min:1,max:168},null,8,["modelValue"]),l[52]||(l[52]=T("span",{style:{"margin-left":"10px"}},"小时",-1))]),_:1,__:[52]}),e(i,{label:"描述",prop:"description"},{default:s(()=>[e(S,{modelValue:_.value.description,"onUpdate:modelValue":l[7]||(l[7]=t=>_.value.description=t),type:"textarea",placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1}),e(i,{label:"启用",prop:"enabled"},{default:s(()=>[e(Q,{modelValue:_.value.enabled,"onUpdate:modelValue":l[8]||(l[8]=t=>_.value.enabled=t)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(ne,{modelValue:L.value,"onUpdate:modelValue":l[34]||(l[34]=t=>L.value=t),title:E.value?"编辑节点":"添加节点",width:"600px"},{footer:s(()=>[T("span",ol,[e(f,{onClick:l[33]||(l[33]=t=>L.value=!1)},{default:s(()=>l[55]||(l[55]=[b("取消")])),_:1,__:[55]}),e(f,{type:"primary",onClick:xe},{default:s(()=>l[56]||(l[56]=[b("确定")])),_:1,__:[56]})])]),default:s(()=>[e(ue,{model:a.value,"label-width":"120px",rules:be.value,ref_key:"nodeFormRef",ref:Z},{default:s(()=>[e(i,{label:"节点名称",prop:"name"},{default:s(()=>[e(S,{modelValue:a.value.name,"onUpdate:modelValue":l[11]||(l[11]=t=>a.value.name=t),placeholder:"请输入节点名称"},null,8,["modelValue"])]),_:1}),e(i,{label:"协议类型",prop:"type"},{default:s(()=>[e(O,{modelValue:a.value.type,"onUpdate:modelValue":l[12]||(l[12]=t=>a.value.type=t),placeholder:"请选择协议类型",style:{width:"100%"},onChange:Ne},{default:s(()=>[e(y,{label:"Shadowsocks",value:"shadowsocks"}),e(y,{label:"VMess",value:"vmess"}),e(y,{label:"VLESS",value:"vless"}),e(y,{label:"Trojan",value:"trojan"}),e(y,{label:"Hysteria",value:"hysteria"}),e(y,{label:"Hysteria2",value:"hysteria2"})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"服务器地址",prop:"address"},{default:s(()=>[e(S,{modelValue:a.value.address,"onUpdate:modelValue":l[13]||(l[13]=t=>a.value.address=t),placeholder:"请输入服务器地址"},null,8,["modelValue"])]),_:1}),e(i,{label:"端口",prop:"port"},{default:s(()=>[e(K,{modelValue:a.value.port,"onUpdate:modelValue":l[14]||(l[14]=t=>a.value.port=t),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a.value.type==="shadowsocks"?(d(),h(C,{key:0},[e(i,{label:"密码",prop:"password"},{default:s(()=>[e(S,{modelValue:a.value.password,"onUpdate:modelValue":l[15]||(l[15]=t=>a.value.password=t),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1}),e(i,{label:"加密方式",prop:"security"},{default:s(()=>[e(O,{modelValue:a.value.security,"onUpdate:modelValue":l[16]||(l[16]=t=>a.value.security=t),placeholder:"请选择加密方式",style:{width:"100%"}},{default:s(()=>[e(y,{label:"aes-128-gcm",value:"aes-128-gcm"}),e(y,{label:"aes-256-gcm",value:"aes-256-gcm"}),e(y,{label:"chacha20-ietf-poly1305",value:"chacha20-ietf-poly1305"}),e(y,{label:"xchacha20-ietf-poly1305",value:"xchacha20-ietf-poly1305"}),e(y,{label:"2022-blake3-aes-128-gcm",value:"2022-blake3-aes-128-gcm"}),e(y,{label:"2022-blake3-aes-256-gcm",value:"2022-blake3-aes-256-gcm"}),e(y,{label:"2022-blake3-chacha20-poly1305",value:"2022-blake3-chacha20-poly1305"})]),_:1},8,["modelValue"])]),_:1})],64)):N("",!0),["vmess","vless"].includes(a.value.type)?(d(),h(C,{key:1},[e(i,{label:"UUID",prop:"uuid"},{default:s(()=>[e(S,{modelValue:a.value.uuid,"onUpdate:modelValue":l[17]||(l[17]=t=>a.value.uuid=t),placeholder:"请输入UUID"},null,8,["modelValue"])]),_:1}),a.value.type==="vmess"?(d(),U(i,{key:0,label:"加密方式",prop:"security"},{default:s(()=>[e(O,{modelValue:a.value.security,"onUpdate:modelValue":l[18]||(l[18]=t=>a.value.security=t),placeholder:"请选择加密方式",style:{width:"100%"}},{default:s(()=>[e(y,{label:"auto",value:"auto"}),e(y,{label:"aes-128-gcm",value:"aes-128-gcm"}),e(y,{label:"chacha20-poly1305",value:"chacha20-poly1305"}),e(y,{label:"none",value:"none"})]),_:1},8,["modelValue"])]),_:1})):N("",!0)],64)):N("",!0),a.value.type==="trojan"?(d(),U(i,{key:2,label:"密码",prop:"password"},{default:s(()=>[e(S,{modelValue:a.value.password,"onUpdate:modelValue":l[19]||(l[19]=t=>a.value.password=t),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):N("",!0),["hysteria","hysteria2"].includes(a.value.type)?(d(),h(C,{key:3},[e(i,{label:"密码",prop:"password"},{default:s(()=>[e(S,{modelValue:a.value.password,"onUpdate:modelValue":l[20]||(l[20]=t=>a.value.password=t),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1}),e(i,{label:"上行速度",prop:"up_mbps"},{default:s(()=>[e(K,{modelValue:a.value.up_mbps,"onUpdate:modelValue":l[21]||(l[21]=t=>a.value.up_mbps=t),min:1,placeholder:"上行速度 (Mbps)",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(i,{label:"下行速度",prop:"down_mbps"},{default:s(()=>[e(K,{modelValue:a.value.down_mbps,"onUpdate:modelValue":l[22]||(l[22]=t=>a.value.down_mbps=t),min:1,placeholder:"下行速度 (Mbps)",style:{width:"100%"}},null,8,["modelValue"])]),_:1})],64)):N("",!0),["vmess","vless","trojan"].includes(a.value.type)?(d(),h(C,{key:4},[e(i,{label:"传输协议",prop:"network"},{default:s(()=>[e(O,{modelValue:a.value.network,"onUpdate:modelValue":l[23]||(l[23]=t=>a.value.network=t),placeholder:"请选择传输协议",style:{width:"100%"},onChange:Ce},{default:s(()=>[e(y,{label:"TCP",value:"tcp"}),e(y,{label:"WebSocket",value:"ws"}),e(y,{label:"gRPC",value:"grpc"}),e(y,{label:"HTTP/2",value:"h2"}),e(y,{label:"QUIC",value:"quic"})]),_:1},8,["modelValue"])]),_:1}),a.value.network==="ws"?(d(),h(C,{key:0},[e(i,{label:"WebSocket路径",prop:"ws_path"},{default:s(()=>[e(S,{modelValue:a.value.ws_path,"onUpdate:modelValue":l[24]||(l[24]=t=>a.value.ws_path=t),placeholder:"如: /path"},null,8,["modelValue"])]),_:1}),e(i,{label:"WebSocket头部",prop:"ws_headers"},{default:s(()=>[e(S,{modelValue:a.value.ws_headers,"onUpdate:modelValue":l[25]||(l[25]=t=>a.value.ws_headers=t),type:"textarea",rows:2,placeholder:'如: {"Host": "example.com"}'},null,8,["modelValue"])]),_:1})],64)):N("",!0),a.value.network==="grpc"?(d(),U(i,{key:1,label:"gRPC服务名",prop:"grpc_service_name"},{default:s(()=>[e(S,{modelValue:a.value.grpc_service_name,"onUpdate:modelValue":l[26]||(l[26]=t=>a.value.grpc_service_name=t),placeholder:"请输入gRPC服务名"},null,8,["modelValue"])]),_:1})):N("",!0),a.value.network==="h2"?(d(),h(C,{key:2},[e(i,{label:"HTTP/2路径",prop:"h2_path"},{default:s(()=>[e(S,{modelValue:a.value.h2_path,"onUpdate:modelValue":l[27]||(l[27]=t=>a.value.h2_path=t),placeholder:"如: /path"},null,8,["modelValue"])]),_:1}),e(i,{label:"HTTP/2主机",prop:"h2_host"},{default:s(()=>[e(S,{modelValue:a.value.h2_host,"onUpdate:modelValue":l[28]||(l[28]=t=>a.value.h2_host=t),placeholder:"如: example.com"},null,8,["modelValue"])]),_:1})],64)):N("",!0)],64)):N("",!0),a.value.type!=="shadowsocks"?(d(),h(C,{key:5},[e(i,{label:"启用TLS"},{default:s(()=>[e(Q,{modelValue:a.value.tls_enabled,"onUpdate:modelValue":l[29]||(l[29]=t=>a.value.tls_enabled=t)},null,8,["modelValue"])]),_:1}),a.value.tls_enabled?(d(),h(C,{key:0},[e(i,{label:"TLS服务器名",prop:"tls_server_name"},{default:s(()=>[e(S,{modelValue:a.value.tls_server_name,"onUpdate:modelValue":l[30]||(l[30]=t=>a.value.tls_server_name=t),placeholder:"如: example.com"},null,8,["modelValue"])]),_:1}),e(i,{label:"跳过证书验证"},{default:s(()=>[e(Q,{modelValue:a.value.tls_insecure,"onUpdate:modelValue":l[31]||(l[31]=t=>a.value.tls_insecure=t)},null,8,["modelValue"])]),_:1})],64)):N("",!0)],64)):N("",!0),e(i,{label:"订阅分组",prop:"subscription"},{default:s(()=>[e(O,{modelValue:a.value.subscription,"onUpdate:modelValue":l[32]||(l[32]=t=>a.value.subscription=t),placeholder:"请选择订阅分组",style:{width:"100%"}},{default:s(()=>[(d(!0),h(C,null,ve(m.value,t=>(d(),U(y,{key:t.id,label:t.name,value:t.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}}),il=We(rl,[["__scopeId","data-v-508e4645"]]);export{il as default};
