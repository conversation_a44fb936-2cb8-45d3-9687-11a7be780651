import{d as me,r as c,a as Ie,c as je,o as Ee,e as C,h,f as t,j as ce,F as H,w as a,i as v,g,u as R,p as pe,m as Me,q as Le,s as Re,v as x,x as ve,t as q,y as Oe,k as fe,E as n,z as ee,l as y,n as Pe,_ as Ge}from"./index-DWkPzHYP.js";import{s as U}from"./subscriptions-DhK109iV.js";import{r as z}from"./request-B4LoKHA1.js";const He=me("subscriptions",()=>{const _=c([]),r=c(!1),u=async()=>{try{r.value=!0;const s=await U.getSubscriptions();return _.value=s,s}catch(s){throw console.error("获取订阅失败:",s),s}finally{r.value=!1}};return{subscriptions:_,loading:r,getSubscriptions:u,addSubscription:async s=>{try{r.value=!0;const i=await U.addSubscription(s);return await u(),i}catch(i){throw console.error("添加订阅失败:",i),i}finally{r.value=!1}},updateSubscription:async(s,i)=>{try{r.value=!0;const f=await U.updateSubscription(s,i);return await u(),f}catch(f){throw console.error("更新订阅失败:",f),f}finally{r.value=!1}},deleteSubscription:async s=>{try{r.value=!0,await U.deleteSubscription(s),await u()}catch(i){throw console.error("删除订阅失败:",i),i}finally{r.value=!1}},updateSubscriptionNodes:async s=>{try{r.value=!0,await U.updateNodes(s),await u()}catch(i){throw console.error("更新订阅节点失败:",i),i}finally{r.value=!1}},forceUpdateSubscription:async s=>{try{r.value=!0,await U.forceUpdateSubscription(s),await u()}catch(i){throw console.error("强制更新订阅失败:",i),i}finally{r.value=!1}},getSchedulerStatus:async()=>{try{return await U.getSchedulerStatus()}catch(s){throw console.error("获取调度器状态失败:",s),s}},startScheduler:async()=>{try{return await U.startScheduler()}catch(s){throw console.error("启动调度器失败:",s),s}},stopScheduler:async()=>{try{return await U.stopScheduler()}catch(s){throw console.error("停止调度器失败:",s),s}}}}),F={getNodes(){return z.get("/api/nodes")},getNodesBySubscription(){return z.get("/api/nodes/by-subscription")},getNode(_){return z.get(`/api/nodes/${_}`)},addNode(_){return z.post("/api/nodes",_)},updateNode(_,r){return z.put(`/api/nodes/${_}`,r)},deleteNode(_){return z.delete(`/api/nodes/${_}`)},deleteAllNodes(){return z.delete("/api/nodes/all")}},Je=me("nodes",()=>{const _=c([]),r=c({}),u=c(!1),k=async()=>{try{u.value=!0;const d=await F.getNodes();return _.value=d,d}catch(d){throw console.error("获取节点失败:",d),d}finally{u.value=!1}},$=async()=>{try{u.value=!0;const d=await F.getNodesBySubscription();return r.value=d,d}catch(d){throw console.error("获取按订阅分组的节点失败:",d),d}finally{u.value=!1}};return{nodes:_,nodesBySubscription:r,loading:u,getNodes:k,getNodesBySubscription:$,getNode:async d=>{try{return u.value=!0,await F.getNode(d)}catch(s){throw console.error("获取节点失败:",s),s}finally{u.value=!1}},addNode:async d=>{try{u.value=!0;const s=await F.addNode(d);return await k(),s}catch(s){throw console.error("添加节点失败:",s),s}finally{u.value=!1}},updateNode:async(d,s)=>{try{u.value=!0;const i=await F.updateNode(d,s);return await k(),i}catch(i){throw console.error("更新节点失败:",i),i}finally{u.value=!1}},deleteNode:async d=>{await F.deleteNode(d),await $()},deleteAllNodes:async()=>{await F.deleteAllNodes(),_.value=[],r.value={}}}}),Ke={class:"subscriptions"},Qe={class:"page-header"},We={class:"header-actions"},Xe={class:"card-header"},Ye={class:"scheduler-status"},Ze={style:{"margin-left":"10px",color:"#666"}},et={key:1,class:"no-test"},tt={key:0,class:"speed-value"},at={key:1,class:"no-test"},lt={class:"dialog-footer"},ot={class:"dialog-footer"},st=Ie({__name:"Subscriptions",setup(_){const r=He(),u=Je(),k=c("subscriptions"),$=c(""),J=c(""),I=c(!1),S=c(!1),B=c(!1),T=c(null),d=c(),s=c(null),i=c([]),f=c({name:"",url:"",updateInterval:24,description:"",enabled:!0}),K=c(!1),D=c(!1),j=c(null),Q=c(),W=c(!1),X=c(!1),te=c([]),w=c({name:"",type:"shadowsocks",address:"",port:443,subscription:""}),ye={name:[{required:!0,message:"请输入订阅名称",trigger:"blur"}],url:[{required:!0,message:"请输入订阅地址",trigger:"blur"}],updateInterval:[{required:!0,message:"请输入更新间隔",trigger:"blur"}]},be={name:[{required:!0,message:"请输入节点名称",trigger:"blur"}],type:[{required:!0,message:"请选择协议类型",trigger:"change"}],address:[{required:!0,message:"请输入服务器地址",trigger:"blur"}],port:[{required:!0,message:"请输入端口",trigger:"blur"}],subscription:[{required:!0,message:"请选择订阅分组",trigger:"change"}]},Y=je(()=>{const l={};return te.value.forEach(e=>{const p=e.subscription||"未分组";l[p]||(l[p]=[]),l[p].push(e)}),l}),O=async()=>{I.value=!0;try{const l=await r.getSubscriptions();i.value=l}catch{n.error("加载订阅失败")}finally{I.value=!1}},ge=l=>{T.value=l,f.value={name:l.name,url:l.url,updateInterval:l.updateInterval,description:l.description,enabled:l.enabled},B.value=!0},_e=async()=>{if(d.value)try{await d.value.validate(),T.value?(await r.updateSubscription(T.value.id,f.value),n.success("更新成功")):(await r.addSubscription(f.value),n.success("添加成功")),B.value=!1,T.value=null,ke(),await O()}catch{n.error("操作失败")}},we=async l=>{try{await ee.confirm("确定要删除这个订阅吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await r.deleteSubscription(l.id),n.success("删除成功"),await O()}catch(e){e!=="cancel"&&n.error("删除失败")}},Se=async l=>{try{await r.updateSubscription(l.id,{enabled:l.enabled}),n.success(l.enabled?"已启用":"已禁用")}catch{l.enabled=!l.enabled,n.error("操作失败")}},he=async l=>{try{await r.updateSubscriptionNodes(l.id),n.success("更新成功"),await O()}catch{n.error("更新失败")}},ke=()=>{f.value={name:"",url:"",updateInterval:24,description:"",enabled:!0}},P=async()=>{S.value=!0;try{s.value=await r.getSchedulerStatus()}catch{n.error("获取调度器状态失败")}finally{S.value=!1}},Ve=async()=>{S.value=!0;try{await r.startScheduler(),n.success("调度器启动成功"),await P()}catch{n.error("启动调度器失败")}finally{S.value=!1}},Ne=async()=>{S.value=!0;try{await r.stopScheduler(),n.success("调度器停止成功"),await P()}catch{n.error("停止调度器失败")}finally{S.value=!1}},A=async()=>{K.value=!0;try{const l=await u.getNodes();te.value=l,Object.keys(Y.value).length>0&&!$.value&&($.value=Object.keys(Y.value)[0])}catch{n.error("加载节点失败")}finally{K.value=!1}},Ce=l=>{j.value=l,w.value={name:l.name,type:l.type,address:l.address,port:l.port,subscription:l.subscription||""},D.value=!0},xe=async()=>{if(Q.value)try{await Q.value.validate(),j.value?(await u.updateNode(j.value.id,w.value),n.success("更新成功")):(await u.addNode(w.value),n.success("添加成功")),D.value=!1,j.value=null,$e(),await A()}catch{n.error("操作失败")}},Ue=async l=>{try{await ee.confirm("确定要删除这个节点吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await u.deleteNode(l.id),n.success("删除成功"),await A()}catch(e){e!=="cancel"&&n.error("删除失败")}},$e=()=>{w.value={name:"",type:"shadowsocks",address:"",port:443,subscription:""}},Be=async l=>{l.testing=!0;try{await u.testNodeSpeed(l.id),n.success("测试完成"),await A()}catch{n.error("测试失败")}finally{l.testing=!1}},Te=async()=>{W.value=!0;try{await u.batchTestSpeed(),n.success("批量测试完成"),await A()}catch{n.error("批量测试失败")}finally{W.value=!1}},Ae=async()=>{X.value=!0;try{await u.autoDetectNodes(),n.success("自动检测完成"),await A()}catch{n.error("自动检测失败")}finally{X.value=!1}},qe=async()=>{try{await ee.confirm("确定要删除所有节点吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await u.deleteAllNodes(),n.success("删除成功"),await A()}catch(l){l!=="cancel"&&n.error("删除失败")}},ze=l=>({shadowsocks:"primary",vmess:"success",vless:"warning",trojan:"danger",hysteria:"info",hysteria2:"info"})[l]||"",Fe=l=>l<100?"latency-good":l<300?"latency-medium":"latency-bad";return Ee(()=>{O(),A(),P()}),(l,e)=>{const p=g("el-button"),E=g("el-icon"),G=g("el-tag"),De=g("el-card"),b=g("el-table-column"),ae=g("el-switch"),le=g("el-table"),Z=g("el-tab-pane"),oe=g("el-tabs"),M=g("el-input"),V=g("el-form-item"),se=g("el-input-number"),re=g("el-form"),ne=g("el-dialog"),L=g("el-option"),ue=g("el-select"),de=Oe("loading");return y(),C("div",Ke,[h("div",Qe,[e[24]||(e[24]=h("h2",null,"订阅管理",-1)),h("div",We,[k.value==="subscriptions"?(y(),C(H,{key:0},[t(p,{onClick:P,loading:S.value},{default:a(()=>e[18]||(e[18]=[v(" 调度器状态 ")])),_:1,__:[18]},8,["loading"]),t(p,{type:"primary",onClick:e[0]||(e[0]=o=>B.value=!0)},{default:a(()=>[t(E,null,{default:a(()=>[t(R(pe))]),_:1}),e[19]||(e[19]=v(" 添加订阅 "))]),_:1,__:[19]})],64)):k.value==="nodes"?(y(),C(H,{key:1},[t(p,{type:"success",onClick:Te,loading:W.value},{default:a(()=>[t(E,null,{default:a(()=>[t(R(Me))]),_:1}),e[20]||(e[20]=v(" 批量测试 "))]),_:1,__:[20]},8,["loading"]),t(p,{type:"warning",onClick:Ae,loading:X.value},{default:a(()=>[t(E,null,{default:a(()=>[t(R(Le))]),_:1}),e[21]||(e[21]=v(" 自动检测 "))]),_:1,__:[21]},8,["loading"]),t(p,{type:"primary",onClick:e[1]||(e[1]=o=>D.value=!0)},{default:a(()=>[t(E,null,{default:a(()=>[t(R(pe))]),_:1}),e[22]||(e[22]=v(" 添加节点 "))]),_:1,__:[22]}),t(p,{type:"danger",onClick:qe,style:{"margin-left":"12px"}},{default:a(()=>[t(E,null,{default:a(()=>[t(R(Re))]),_:1}),e[23]||(e[23]=v(" 删除所有节点 "))]),_:1,__:[23]})],64)):ce("",!0)])]),t(oe,{modelValue:k.value,"onUpdate:modelValue":e[3]||(e[3]=o=>k.value=o),type:"card"},{default:a(()=>[t(Z,{label:"订阅列表",name:"subscriptions"},{default:a(()=>[s.value?(y(),x(De,{key:0,style:{"margin-bottom":"20px"}},{header:a(()=>[h("div",Xe,[e[27]||(e[27]=h("span",null,"自动更新调度器",-1)),h("div",null,[s.value.running?(y(),x(p,{key:1,type:"danger",size:"small",onClick:Ne,loading:S.value},{default:a(()=>e[26]||(e[26]=[v(" 停止 ")])),_:1,__:[26]},8,["loading"])):(y(),x(p,{key:0,type:"success",size:"small",onClick:Ve,loading:S.value},{default:a(()=>e[25]||(e[25]=[v(" 启动 ")])),_:1,__:[25]},8,["loading"]))])])]),default:a(()=>[h("div",Ye,[t(G,{type:s.value.running?"success":"danger"},{default:a(()=>[v(q(s.value.running?"运行中":"已停止"),1)]),_:1},8,["type"]),h("span",Ze,q(s.value.message),1)])]),_:1})):ce("",!0),ve((y(),x(le,{data:i.value,style:{width:"100%"}},{default:a(()=>[t(b,{prop:"name",label:"订阅名称",width:"150"}),t(b,{prop:"url",label:"订阅地址","min-width":"300","show-overflow-tooltip":""}),t(b,{prop:"nodeCount",label:"节点数量",width:"100"},{default:a(({row:o})=>[t(p,{type:"text",onClick:N=>{k.value="nodes",J.value=o.name},disabled:!o.nodeCount},{default:a(()=>[v(q(o.nodeCount||0),1)]),_:2},1032,["onClick","disabled"])]),_:1}),t(b,{prop:"lastUpdate",label:"最后更新",width:"150"}),t(b,{prop:"enabled",label:"状态",width:"80"},{default:a(({row:o})=>[t(ae,{modelValue:o.enabled,"onUpdate:modelValue":N=>o.enabled=N,onChange:N=>Se(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(b,{label:"操作",width:"250",fixed:"right"},{default:a(({row:o})=>[t(p,{size:"small",onClick:N=>he(o)},{default:a(()=>e[28]||(e[28]=[v("更新")])),_:2,__:[28]},1032,["onClick"]),t(p,{size:"small",onClick:N=>ge(o)},{default:a(()=>e[29]||(e[29]=[v("编辑")])),_:2,__:[29]},1032,["onClick"]),t(p,{size:"small",type:"danger",onClick:N=>we(o)},{default:a(()=>e[30]||(e[30]=[v("删除")])),_:2,__:[30]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,I.value]])]),_:1}),t(Z,{label:"节点管理",name:"nodes"},{default:a(()=>[ve((y(),x(oe,{modelValue:$.value,"onUpdate:modelValue":e[2]||(e[2]=o=>$.value=o),type:"card"},{default:a(()=>[(y(!0),C(H,null,fe(Y.value,(o,N)=>(y(),x(Z,{key:N,label:`${N} (${o.length})`,name:N},{default:a(()=>[t(le,{data:o,style:{width:"100%"}},{default:a(()=>[t(b,{prop:"name",label:"节点名称",width:"150"}),t(b,{prop:"type",label:"协议类型",width:"100"},{default:a(({row:m})=>[t(G,{type:ze(m.type)},{default:a(()=>[v(q(m.type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"address",label:"服务器地址",width:"150"}),t(b,{prop:"port",label:"端口",width:"80"}),t(b,{prop:"group",label:"分组",width:"100"}),t(b,{prop:"security",label:"加密方式",width:"120"}),t(b,{prop:"network",label:"传输协议",width:"120"}),t(b,{label:"延迟",width:"100"},{default:a(({row:m})=>[m.latency?(y(),C("span",{key:0,class:Pe(Fe(m.latency))},q(m.latency)+"ms ",3)):(y(),C("span",et,"未测试"))]),_:1}),t(b,{label:"速度",width:"120"},{default:a(({row:m})=>[m.speed?(y(),C("span",tt,q(m.speed),1)):(y(),C("span",at,"未测试"))]),_:1}),t(b,{label:"状态",width:"100"},{default:a(({row:m})=>[m.available!==void 0?(y(),x(G,{key:0,type:m.available?"success":"danger"},{default:a(()=>[v(q(m.available?"可用":"不可用"),1)]),_:2},1032,["type"])):(y(),x(G,{key:1,type:"info"},{default:a(()=>e[31]||(e[31]=[v("未检测")])),_:1,__:[31]}))]),_:1}),t(b,{label:"操作",width:"280",fixed:"right"},{default:a(({row:m})=>[t(p,{size:"small",onClick:ie=>Be(m),loading:m.testing},{default:a(()=>e[32]||(e[32]=[v(" 测试速度 ")])),_:2,__:[32]},1032,["onClick","loading"]),t(p,{size:"small",onClick:ie=>Ce(m)},{default:a(()=>e[33]||(e[33]=[v("编辑")])),_:2,__:[33]},1032,["onClick"]),t(p,{size:"small",type:"danger",onClick:ie=>Ue(m)},{default:a(()=>e[34]||(e[34]=[v("删除")])),_:2,__:[34]},1032,["onClick"])]),_:1})]),_:2},1032,["data"])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])),[[de,K.value]])]),_:1})]),_:1},8,["modelValue"]),t(ne,{modelValue:B.value,"onUpdate:modelValue":e[10]||(e[10]=o=>B.value=o),title:T.value?"编辑订阅":"添加订阅",width:"600px"},{footer:a(()=>[h("span",lt,[t(p,{onClick:e[9]||(e[9]=o=>B.value=!1)},{default:a(()=>e[36]||(e[36]=[v("取消")])),_:1,__:[36]}),t(p,{type:"primary",onClick:_e},{default:a(()=>e[37]||(e[37]=[v("确定")])),_:1,__:[37]})])]),default:a(()=>[t(re,{model:f.value,"label-width":"100px",rules:ye,ref_key:"subscriptionFormRef",ref:d},{default:a(()=>[t(V,{label:"订阅名称",prop:"name"},{default:a(()=>[t(M,{modelValue:f.value.name,"onUpdate:modelValue":e[4]||(e[4]=o=>f.value.name=o),placeholder:"请输入订阅名称"},null,8,["modelValue"])]),_:1}),t(V,{label:"订阅地址",prop:"url"},{default:a(()=>[t(M,{modelValue:f.value.url,"onUpdate:modelValue":e[5]||(e[5]=o=>f.value.url=o),placeholder:"请输入订阅地址"},null,8,["modelValue"])]),_:1}),t(V,{label:"更新间隔",prop:"updateInterval"},{default:a(()=>[t(se,{modelValue:f.value.updateInterval,"onUpdate:modelValue":e[6]||(e[6]=o=>f.value.updateInterval=o),min:1,max:168},null,8,["modelValue"]),e[35]||(e[35]=h("span",{style:{"margin-left":"10px"}},"小时",-1))]),_:1,__:[35]}),t(V,{label:"描述",prop:"description"},{default:a(()=>[t(M,{modelValue:f.value.description,"onUpdate:modelValue":e[7]||(e[7]=o=>f.value.description=o),type:"textarea",placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1}),t(V,{label:"启用",prop:"enabled"},{default:a(()=>[t(ae,{modelValue:f.value.enabled,"onUpdate:modelValue":e[8]||(e[8]=o=>f.value.enabled=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(ne,{modelValue:D.value,"onUpdate:modelValue":e[17]||(e[17]=o=>D.value=o),title:j.value?"编辑节点":"添加节点",width:"600px"},{footer:a(()=>[h("span",ot,[t(p,{onClick:e[16]||(e[16]=o=>D.value=!1)},{default:a(()=>e[38]||(e[38]=[v("取消")])),_:1,__:[38]}),t(p,{type:"primary",onClick:xe},{default:a(()=>e[39]||(e[39]=[v("确定")])),_:1,__:[39]})])]),default:a(()=>[t(re,{model:w.value,"label-width":"100px",rules:be,ref_key:"nodeFormRef",ref:Q},{default:a(()=>[t(V,{label:"节点名称",prop:"name"},{default:a(()=>[t(M,{modelValue:w.value.name,"onUpdate:modelValue":e[11]||(e[11]=o=>w.value.name=o),placeholder:"请输入节点名称"},null,8,["modelValue"])]),_:1}),t(V,{label:"协议类型",prop:"type"},{default:a(()=>[t(ue,{modelValue:w.value.type,"onUpdate:modelValue":e[12]||(e[12]=o=>w.value.type=o),placeholder:"请选择协议类型",style:{width:"100%"}},{default:a(()=>[t(L,{label:"Shadowsocks",value:"shadowsocks"}),t(L,{label:"VMess",value:"vmess"}),t(L,{label:"VLESS",value:"vless"}),t(L,{label:"Trojan",value:"trojan"})]),_:1},8,["modelValue"])]),_:1}),t(V,{label:"服务器地址",prop:"address"},{default:a(()=>[t(M,{modelValue:w.value.address,"onUpdate:modelValue":e[13]||(e[13]=o=>w.value.address=o),placeholder:"请输入服务器地址"},null,8,["modelValue"])]),_:1}),t(V,{label:"端口",prop:"port"},{default:a(()=>[t(se,{modelValue:w.value.port,"onUpdate:modelValue":e[14]||(e[14]=o=>w.value.port=o),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(V,{label:"订阅分组",prop:"subscription"},{default:a(()=>[t(ue,{modelValue:w.value.subscription,"onUpdate:modelValue":e[15]||(e[15]=o=>w.value.subscription=o),placeholder:"请选择订阅分组",style:{width:"100%"}},{default:a(()=>[(y(!0),C(H,null,fe(i.value,o=>(y(),x(L,{key:o.id,label:o.name,value:o.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),dt=Ge(st,[["__scopeId","data-v-23105374"]]);export{dt as default};
