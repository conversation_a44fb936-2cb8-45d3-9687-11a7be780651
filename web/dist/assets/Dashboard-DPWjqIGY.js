import{u as H}from"./core-C4BaOiUe.js";import{d as J,r as w,a as O,c as k,o as P,b as Q,e as x,f as n,w as o,g as _,h as e,i as r,t as i,j as W,F as X,k as Y,l as L,n as Z,_ as tt}from"./index-DWkPzHYP.js";import{r as et}from"./request-B4LoKHA1.js";import"./core-B3X1AWgS.js";const st={getStats(){return et.get("/api/stats")}},ot=J("stats",()=>{const M=w({activeConnections:0,totalTraffic:"0MB",todayTraffic:"0MB",uploadSpeed:"0KB/s",downloadSpeed:"0KB/s"}),l=w(!1),v=w(0),a=1e4,d=async(p=!1)=>{const c=Date.now();if(p||c-v.value>a)try{l.value=!0;const f=await st.getStats();M.value=f,v.value=c}catch(f){throw console.error("获取统计信息失败:",f),f}finally{l.value=!1}};return{stats:M,loading:l,getStats:d,startRealTimeStats:()=>{const p=setInterval(async()=>{try{await d()}catch(c){console.error("更新实时统计失败:",c)}},3e4);return()=>clearInterval(p)}}}),nt={class:"dashboard"},at={class:"card-header"},lt={class:"status-content"},rt={class:"status-content"},ut={class:"status-content"},it={class:"quick-actions"},dt={class:"card-header"},ct={class:"log-controls"},ft={class:"log-container"},vt={class:"log-time"},pt={class:"log-message"},mt={key:0,class:"no-logs"},gt=O({__name:"Dashboard",setup(M){const l=H(),v=ot();let a=null,d=null;const y=k(()=>{const s=l.status.running;return{running:s,type:s?"success":"danger",text:s?"运行中":"未运行"}}),p=k(()=>({version:l.status.version||"未知",uptime:l.status.uptime||"0s",memory:l.status.memory||"0MB"})),c=k(()=>({activeConnections:v.stats.activeConnections||0,totalTraffic:v.stats.totalTraffic||"0MB",todayTraffic:v.stats.todayTraffic||"0MB"})),f=w({inboundCount:0,outboundCount:0,routeCount:0}),m=w([{time:"12:34:56",level:"info",message:"系统启动完成"},{time:"12:34:55",level:"warn",message:"配置文件加载中..."},{time:"12:34:54",level:"error",message:"核心启动失败"}]),u=(s,t)=>{const g=new Date().toLocaleTimeString();m.value.unshift({time:g,level:s,message:t}),m.value.length>50&&(m.value=m.value.slice(0,50))},T=w("all"),h=k(()=>T.value==="all"?m.value:m.value.filter(s=>s.level===T.value)),N=async()=>{try{u("info","正在启动核心..."),await l.startCore(),l.status.running?(u("info","核心启动成功"),console.log("核心启动成功，重新开始状态检测"),V()):u("error","核心启动失败")}catch(s){u("error",`启动核心失败: ${s}`),console.error("启动核心失败:",s)}},q=async()=>{try{u("info","正在停止核心..."),await l.stopCore(),u("info","核心已停止")}catch(s){u("error",`停止核心失败: ${s}`),console.error("停止核心失败:",s)}},z=async()=>{try{u("info","正在重载配置..."),await l.reloadConfig(),u("info","配置重载成功")}catch(s){u("error",`重载配置失败: ${s}`),console.error("重载配置失败:",s)}},K=async()=>{try{u("info","正在更新核心..."),await l.updateCore(),u("info","核心更新成功")}catch(s){u("error",`更新核心失败: ${s}`),console.error("更新核心失败:",s)}},R=()=>{I(),D(),a=setInterval(()=>{I()},6e4),d=setInterval(()=>{D()},3e4)},V=()=>{a&&clearInterval(a),a=setInterval(()=>{I()},6e4)},U=()=>{a&&clearInterval(a),a=setInterval(()=>{I()},3e5)},I=async()=>{try{if(await l.getCoreStatus(),l.status.running){a||V();return}}catch(s){console.error("获取核心状态失败:",s)}a&&(clearInterval(a),a=null),U(),console.log("核心未运行，切换到低频状态检测（每5分钟）")},D=async()=>{try{await v.getStats()}catch(s){console.error("获取统计信息失败:",s),d&&(clearInterval(d),d=null,console.log("统计信息获取失败，停止自动统计检测"))}},j=()=>{a&&(clearInterval(a),a=null),d&&(clearInterval(d),d=null)},A=()=>{m.value=[]};return P(()=>{R()}),Q(()=>{j()}),(s,t)=>{const $=_("el-tag"),g=_("el-card"),S=_("el-col"),F=_("el-row"),b=_("el-button"),B=_("el-option"),E=_("el-select");return L(),x("div",nt,[n(F,{gutter:20},{default:o(()=>[n(S,{span:8},{default:o(()=>[n(g,{class:"status-card"},{header:o(()=>[e("div",at,[t[1]||(t[1]=e("span",null,"核心状态",-1)),n($,{type:y.value.type},{default:o(()=>[r(i(y.value.text),1)]),_:1},8,["type"])])]),default:o(()=>[e("div",lt,[e("p",null,[t[2]||(t[2]=e("strong",null,"版本:",-1)),r(" "+i(p.value.version||"未知"),1)]),e("p",null,[t[3]||(t[3]=e("strong",null,"运行时间:",-1)),r(" "+i(p.value.uptime||"0s"),1)]),e("p",null,[t[4]||(t[4]=e("strong",null,"内存使用:",-1)),r(" "+i(p.value.memory||"0MB"),1)])])]),_:1})]),_:1}),n(S,{span:8},{default:o(()=>[n(g,{class:"status-card"},{header:o(()=>t[5]||(t[5]=[e("div",{class:"card-header"},[e("span",null,"连接统计")],-1)])),default:o(()=>[e("div",rt,[e("p",null,[t[6]||(t[6]=e("strong",null,"活跃连接:",-1)),r(" "+i(c.value.activeConnections||0),1)]),e("p",null,[t[7]||(t[7]=e("strong",null,"总流量:",-1)),r(" "+i(c.value.totalTraffic||"0MB"),1)]),e("p",null,[t[8]||(t[8]=e("strong",null,"今日流量:",-1)),r(" "+i(c.value.todayTraffic||"0MB"),1)])])]),_:1})]),_:1}),n(S,{span:8},{default:o(()=>[n(g,{class:"status-card"},{header:o(()=>t[9]||(t[9]=[e("div",{class:"card-header"},[e("span",null,"配置信息")],-1)])),default:o(()=>[e("div",ut,[e("p",null,[t[10]||(t[10]=e("strong",null,"入站数量:",-1)),r(" "+i(f.value.inboundCount||0),1)]),e("p",null,[t[11]||(t[11]=e("strong",null,"出站数量:",-1)),r(" "+i(f.value.outboundCount||0),1)]),e("p",null,[t[12]||(t[12]=e("strong",null,"路由规则:",-1)),r(" "+i(f.value.routeCount||0),1)])])]),_:1})]),_:1})]),_:1}),n(F,{gutter:20,style:{"margin-top":"20px"}},{default:o(()=>[n(S,{span:12},{default:o(()=>[n(g,null,{header:o(()=>t[13]||(t[13]=[e("div",{class:"card-header"},[e("span",null,"快速操作")],-1)])),default:o(()=>[e("div",it,[n(b,{type:"primary",onClick:N,disabled:y.value.running},{default:o(()=>t[14]||(t[14]=[r(" 启动核心 ")])),_:1,__:[14]},8,["disabled"]),n(b,{type:"danger",onClick:q,disabled:!y.value.running},{default:o(()=>t[15]||(t[15]=[r(" 停止核心 ")])),_:1,__:[15]},8,["disabled"]),n(b,{type:"warning",onClick:z,disabled:!y.value.running},{default:o(()=>t[16]||(t[16]=[r(" 重载配置 ")])),_:1,__:[16]},8,["disabled"]),n(b,{onClick:K},{default:o(()=>t[17]||(t[17]=[r(" 更新核心 ")])),_:1,__:[17]})])]),_:1})]),_:1}),n(S,{span:12},{default:o(()=>[n(g,null,{header:o(()=>[e("div",dt,[t[19]||(t[19]=e("span",null,"系统日志",-1)),e("div",ct,[n(E,{modelValue:T.value,"onUpdate:modelValue":t[0]||(t[0]=C=>T.value=C),placeholder:"日志级别",size:"small",style:{width:"100px","margin-right":"10px"}},{default:o(()=>[n(B,{label:"全部",value:"all"}),n(B,{label:"信息",value:"info"}),n(B,{label:"警告",value:"warn"}),n(B,{label:"错误",value:"error"})]),_:1},8,["modelValue"]),n(b,{size:"small",onClick:A,type:"danger"},{default:o(()=>t[18]||(t[18]=[r("清除日志")])),_:1,__:[18]})])])]),default:o(()=>[e("div",ft,[(L(!0),x(X,null,Y(h.value,(C,G)=>(L(),x("div",{key:G,class:"log-item"},[e("span",vt,i(C.time),1),e("span",{class:Z(["log-level",`log-${C.level}`])},i(C.level),3),e("span",pt,i(C.message),1)]))),128)),h.value.length===0?(L(),x("div",mt," 暂无日志记录 ")):W("",!0)])]),_:1})]),_:1})]),_:1})])}}}),St=tt(gt,[["__scopeId","data-v-7f54d3fc"]]);export{St as default};
