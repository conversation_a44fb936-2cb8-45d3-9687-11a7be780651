import{r as s}from"./request-XqBm8oRS.js";const p={getSubscriptions(){return s.get("/api/subscriptions")},addSubscription(t){return s.post("/api/subscriptions",t)},updateSubscription(t,r){return s.put(`/api/subscriptions/${t}`,r)},deleteSubscription(t){return s.delete(`/api/subscriptions/${t}`)},updateNodes(t){return s.post(`/api/subscriptions/${t}/import`)},forceUpdateSubscription(t){return s.post(`/api/subscriptions/${t}/force-update`)},getSchedulerStatus(){return s.get("/api/scheduler/status")},startScheduler(){return s.post("/api/scheduler/start")},stopScheduler(){return s.post("/api/scheduler/stop")}};export{p as s};
