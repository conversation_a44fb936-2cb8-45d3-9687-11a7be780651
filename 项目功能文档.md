# SingBox UI 项目功能文档

## 项目概述

SingBox UI 是一个基于 sing-box 核心的现代化代理管理工具，提供 Web 界面来管理代理服务器配置。该项目采用前后端分离架构，后端使用 Go 语言开发，前端使用 Vue 3 + TypeScript 构建。

### 主要特性

- 🚀 **高性能代理服务器**: 基于 sing-box 核心，支持多种代理协议
- 🌐 **现代化 Web 界面**: 响应式设计，支持桌面端和移动端
- 📱 **实时状态监控**: 实时显示代理服务器运行状态和连接统计
- 🔧 **可视化配置管理**: 直观的界面管理代理节点和订阅
- ⚡ **自动订阅更新**: 支持定时自动更新订阅内容
- 🎯 **简洁的 CLI 界面**: 提供命令行工具进行基础操作

## 技术架构

### 后端架构 (Go)

```
singboxui/
├── main.go                    # 程序入口
├── internal/                  # 内部包
│   ├── config/               # 配置管理
│   │   └── config.go         # 配置加载和验证
│   ├── core/                 # 核心管理
│   │   └── manager.go        # sing-box 核心管理器
│   ├── models/               # 数据模型
│   │   └── types.go          # 数据结构定义
│   ├── scheduler/            # 定时任务
│   │   └── scheduler.go      # 订阅自动更新调度器
│   ├── server/               # 服务器管理
│   │   ├── singbox.go        # sing-box 进程管理
│   │   └── web.go            # Web 服务器
│   ├── service/              # 业务逻辑层
│   │   └── service.go        # 核心业务逻辑
│   ├── storage/              # 数据存储层
│   │   ├── sqlite.go         # SQLite 数据库操作
│   │   └── storage.go        # 存储接口定义
│   └── web/                  # Web API
│       └── web.go            # API 路由和处理器
├── cmd/                      # 命令行工具
│   └── root.go               # CLI 根命令
└── config/                   # 配置文件
    └── singbox.json          # sing-box 配置文件
```

### 前端架构 (Vue 3)

```
frontend/
├── src/
│   ├── api/                  # API 接口层
│   │   ├── core.ts           # 核心状态 API
│   │   ├── inbounds.ts       # 入站配置 API
│   │   ├── nodes.ts          # 节点管理 API
│   │   ├── settings.ts       # 系统设置 API
│   │   ├── stats.ts          # 统计信息 API
│   │   └── subscriptions.ts  # 订阅管理 API
│   ├── components/           # 通用组件
│   │   ├── Header.vue        # 页面头部组件
│   │   └── Sidebar.vue       # 侧边栏导航组件
│   ├── store/                # 状态管理 (Pinia)
│   │   ├── core.ts           # 核心状态管理
│   │   ├── inbounds.ts       # 入站配置状态
│   │   ├── nodes.ts          # 节点管理状态
│   │   ├── settings.ts       # 系统设置状态
│   │   ├── stats.ts          # 统计信息状态
│   │   └── subscriptions.ts  # 订阅管理状态
│   ├── views/                # 页面视图
│   │   ├── Dashboard.vue     # 仪表盘页面
│   │   ├── Subscriptions.vue # 订阅管理页面
│   │   ├── Nodes.vue         # 节点管理页面
│   │   ├── Inbounds.vue      # 入站配置页面
│   │   └── Settings.vue      # 系统设置页面
│   ├── router/               # 路由配置
│   │   └── index.ts          # 路由定义
│   ├── utils/                # 工具函数
│   │   └── request.ts        # HTTP 请求封装
│   ├── App.vue               # 根组件
│   └── main.ts               # 应用入口
├── package.json              # 依赖配置
└── vite.config.ts            # 构建配置
```

## 核心功能模块

### 1. 核心管理模块 (Core Management)

**功能描述**: 管理 sing-box 核心进程的启动、停止、状态监控等操作。

**主要功能**:
- 启动/停止 sing-box 核心进程
- 实时监控核心运行状态
- 配置文件热重载
- 核心版本管理和更新
- 进程 PID 管理和清理

**技术实现**:
- 使用 `os/exec` 管理子进程
- 通过 `context.Context` 实现优雅关闭
- 进程组管理确保完整清理
- PID 文件记录便于进程追踪

### 2. 订阅管理模块 (Subscription Management)

**功能描述**: 管理代理服务器订阅，支持多种订阅格式的导入和自动更新。

**主要功能**:
- 添加、编辑、删除订阅
- 支持多种订阅格式 (V2Ray、Clash、Shadowsocks)
- 自动解析订阅内容为节点列表
- 定时自动更新订阅
- 订阅状态监控和错误处理

**技术实现**:
- 支持 Base64 解码和 URL 解析
- 多种协议格式解析器
- 定时任务调度器
- 数据库持久化存储

### 3. 节点管理模块 (Node Management)

**功能描述**: 管理代理服务器节点，支持多种代理协议。

**支持的协议**:
- **Shadowsocks**: 支持多种加密方式
- **VMess**: 支持 WebSocket、HTTP/2 等传输方式
- **Trojan**: 支持 TLS 伪装
- **VLESS**: 轻量级协议

**主要功能**:
- 节点的增删改查
- 节点分组管理
- 节点状态检测
- 批量导入/导出
- 节点配置验证

**技术实现**:
- 协议特定的配置解析
- TLS 配置管理
- 传输层配置 (gRPC、WebSocket)
- 数据库关系映射

### 4. 入站配置模块 (Inbound Configuration)

**功能描述**: 管理代理服务器的入站连接配置。

**主要功能**:
- 配置本地代理端口
- 支持多种入站协议
- 用户认证配置
- 流量探测设置
- 自动切换配置

**技术实现**:
- 动态端口分配
- 用户权限管理
- 流量分析配置
- 负载均衡策略

### 5. 系统设置模块 (System Settings)

**功能描述**: 管理系统全局配置和参数。

**主要功能**:
- 应用基础配置
- 日志级别设置
- 数据目录管理
- 系统信息显示
- 配置备份/恢复

**技术实现**:
- YAML 配置文件管理
- 配置热重载
- 数据持久化
- 系统资源监控

### 6. 统计监控模块 (Statistics & Monitoring)

**功能描述**: 实时监控代理服务器的运行状态和连接统计。

**主要功能**:
- 实时连接数统计
- 流量使用统计
- 节点延迟测试
- 系统资源监控
- 日志实时查看

**技术实现**:
- WebSocket 实时数据推送
- 定时数据采集
- 数据可视化展示
- 历史数据存储

## 数据模型

### 核心数据结构

```go
// 节点信息
type Node struct {
    ID         string    `json:"id"`
    Name       string    `json:"name"`
    Type       string    `json:"type"`        // vmess, vless, trojan, ss
    Address    string    `json:"address"`
    Port       int       `json:"port"`
    UUID       string    `json:"uuid,omitempty"`
    Password   string    `json:"password,omitempty"`
    Security   string    `json:"security,omitempty"`
    Network    string    `json:"network,omitempty"`
    Group      string    `json:"group,omitempty"`
    CreatedAt  time.Time `json:"created_at"`
    UpdatedAt  time.Time `json:"updated_at"`
    // TLS 配置
    TlsEnabled    *int   `json:"tls_enabled,omitempty"`
    TlsServerName string `json:"tls_server_name,omitempty"`
    TlsInsecure   *int   `json:"tls_insecure,omitempty"`
    // 传输配置
    TransportType           string `json:"transport_type,omitempty"`
    GrpcServiceName         string `json:"grpc_service_name,omitempty"`
    GrpcIdleTimeout         string `json:"grpc_idle_timeout,omitempty"`
    GrpcPingTimeout         string `json:"grpc_ping_timeout,omitempty"`
    GrpcPermitWithoutStream *int   `json:"grpc_permit_without_stream,omitempty"`
}

// 订阅信息
type Subscription struct {
    ID             string    `json:"id"`
    Name           string    `json:"name"`
    URL            string    `json:"url"`
    NodeCount      int       `json:"nodeCount"`
    LastUpdate     string    `json:"lastUpdate"`
    UpdateInterval int       `json:"updateInterval"`
    Enabled        bool      `json:"enabled"`
    Description    string    `json:"description"`
    Nodes          []Node    `json:"nodes,omitempty"`
    CreatedAt      time.Time `json:"created_at"`
    UpdatedAt      time.Time `json:"updated_at"`
}

// 入站配置
type Inbound struct {
    ID                  string                 `json:"id"`
    Name                string                 `json:"name"`
    Type                string                 `json:"type"`
    Port                int                    `json:"port"`
    Username            string                 `json:"username,omitempty"`
    Password            string                 `json:"password,omitempty"`
    Group               string                 `json:"group,omitempty"`
    IncludeNames        []string               `json:"include_names,omitempty"`
    ExcludeNames        []string               `json:"exclude_names,omitempty"`
    Settings            map[string]interface{} `json:"settings"`
    Sniffing            *Sniffing              `json:"sniffing,omitempty"`
    AutoSwitch          bool                   `json:"auto_switch,omitempty"`
    SwitchDelay         int                    `json:"switch_delay,omitempty"`
    SwitchDelayUnit     string                 `json:"switch_delay_unit,omitempty"`
    UrlTestUrl          string                 `json:"urltest_url,omitempty"`
    UrlTestInterval     int                    `json:"urltest_interval,omitempty"`
    UrlTestIntervalUnit string                 `json:"urltest_interval_unit,omitempty"`
    CreatedAt           time.Time              `json:"created_at"`
    UpdatedAt           time.Time              `json:"updated_at"`
}
```

## API 接口设计

### RESTful API 结构

```
/api/core/status          # 核心状态查询
/api/core/start           # 启动核心
/api/core/stop            # 停止核心
/api/core/reload          # 重载配置

/api/subscriptions        # 订阅管理
/api/subscriptions/{id}   # 单个订阅操作
/api/subscriptions/{id}/import  # 导入订阅

/api/nodes               # 节点管理
/api/nodes/{id}          # 单个节点操作
/api/nodes/by-subscription  # 按订阅分组查询

/api/inbounds            # 入站配置管理
/api/inbounds/{id}       # 单个入站配置操作

/api/settings            # 系统设置
/api/scheduler/status    # 调度器状态

/api/stats               # 统计信息
```

### WebSocket 实时数据

- 核心状态实时推送
- 连接统计实时更新
- 日志实时流式传输

## 数据库设计

### SQLite 表结构

```sql
-- 节点表
CREATE TABLE nodes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    address TEXT NOT NULL,
    port INTEGER NOT NULL,
    uuid TEXT,
    password TEXT,
    security TEXT,
    network TEXT,
    outbound_id TEXT,
    group_name TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    tls_enabled BOOLEAN,
    tls_server_name TEXT,
    tls_insecure BOOLEAN,
    transport_type TEXT,
    grpc_service_name TEXT,
    grpc_idle_timeout INTEGER,
    grpc_ping_timeout INTEGER,
    grpc_permit_without_stream BOOLEAN,
    path TEXT,
    obfs_param TEXT,
    peer TEXT
);

-- 订阅表
CREATE TABLE subscriptions (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    enabled BOOLEAN DEFAULT 1,
    node_count INTEGER DEFAULT 0,
    last_update TEXT,
    update_interval INTEGER DEFAULT 0,
    description TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 入站表
CREATE TABLE inbounds (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    port INTEGER NOT NULL,
    username TEXT,
    password TEXT,
    group_name TEXT,
    include_names TEXT,
    exclude_names TEXT,
    auto_switch BOOLEAN,
    switch_delay INTEGER,
    switch_delay_unit TEXT,
    urltest_url TEXT,
    urltest_interval INTEGER,
    urltest_interval_unit TEXT,
    settings TEXT,
    sniffing TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 部署和运维

### 环境要求

- **Go**: 1.21 或更高版本
- **Node.js**: 18 或更高版本
- **npm**: 9 或更高版本
- **SQLite**: 3.x (Go 驱动自动管理)

### 构建流程

```bash
# 1. 构建前端
cd frontend
npm install
npm run build

# 2. 构建后端
go mod tidy
go build -ldflags="-s -w" -o singboxui main.go

# 3. 或使用 Makefile
make build
```

### 运行配置

```yaml
# config.yaml
server:
  port: 8080

singbox:
  config_path: ./config/singbox.json
  binary_path: ./sing-box

data_dir: ./data
```

### 进程管理

- 使用 PID 文件管理 sing-box 进程
- 优雅关闭机制
- 自动清理僵尸进程
- 进程组管理

## 安全特性

### 数据安全

- 敏感信息加密存储
- 配置文件权限控制
- 数据库访问控制
- 日志脱敏处理

### 网络安全

- HTTPS 支持
- API 访问控制
- 请求频率限制
- 输入验证和过滤

## 监控和日志

### 日志系统

- 分级日志记录 (DEBUG, INFO, WARN, ERROR)
- 结构化日志输出
- 日志轮转和清理
- 实时日志查看

### 性能监控

- 内存使用监控
- CPU 使用率监控
- 网络连接统计
- 响应时间监控

## 扩展性设计

### 插件系统

- 协议扩展接口
- 订阅解析器插件
- 统计收集器插件
- 通知系统插件

### 配置管理

- 多环境配置支持
- 配置热重载
- 配置版本管理
- 配置备份恢复

## 故障排除

### 常见问题

1. **sing-box 启动失败**
   - 检查二进制文件权限
   - 验证配置文件格式
   - 查看错误日志

2. **订阅更新失败**
   - 检查网络连接
   - 验证订阅 URL 格式
   - 查看解析错误日志

3. **Web 界面无法访问**
   - 检查端口占用
   - 验证防火墙设置
   - 查看服务器日志

### 调试工具

- 详细的日志输出
- 状态检查命令
- 配置验证工具
- 性能分析工具

## 开发指南

### 代码规范

- Go 代码遵循 `gofmt` 格式化
- Vue 组件使用 Composition API
- TypeScript 严格模式
- 统一的错误处理

### 测试策略

- 单元测试覆盖核心逻辑
- 集成测试验证 API 接口
- 端到端测试验证用户流程
- 性能测试确保响应时间

### 贡献流程

1. Fork 项目仓库
2. 创建功能分支
3. 编写测试用例
4. 提交 Pull Request
5. 代码审查和合并

## 版本历史

### v1.0.0 (当前版本)

- ✅ 基础代理管理功能
- ✅ Web 界面管理
- ✅ 订阅自动更新
- ✅ 多协议支持
- ✅ 实时状态监控

### 计划功能

- 🔄 多用户支持
- 🔄 高级路由规则
- 🔄 插件系统
- 🔄 移动端应用
- 🔄 集群部署支持

---

*本文档持续更新，反映项目的最新功能和架构设计。* 